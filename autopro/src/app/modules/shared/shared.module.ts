import { Injectable, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { WelcomeComponent } from '../../pages/welcome/welcome.component';
import { TituloComponent } from '../../components/titulo/titulo.component';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzPageHeaderModule } from 'ng-zorro-antd/page-header';


@NgModule({
  declarations: [
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzBreadCrumbModule,
    NzPageHeaderModule
  ],
  exports: [
  ]
})
export class SharedModule { }
