import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { ToastInstance, ToastType, ToastAction } from './toast.service';

type NzButtonType = 'primary' | 'default' | 'dashed' | 'text' | 'link';

@Component({
  selector: 'app-toast',
  standalone: true,
  imports: [CommonModule, NzIconModule, NzButtonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div 
      *ngIf="toast && toast.message"
      class="toast"
      [class]="'toast--' + toast.type"
      [@slideIn]="toast.visible ? 'in' : 'out'"
      role="alert"
      [attr.aria-live]="toast.type === 'error' ? 'assertive' : 'polite'"
      [attr.aria-atomic]="true"
      [attr.aria-describedby]="'toast-' + toast.id + '-message'"
    >
      <div class="toast__content">
        <div class="toast__icon" [attr.aria-hidden]="true">
          <span nz-icon [nzType]="getIcon()" [nzTheme]="'fill'"></span>
        </div>
        
        <div class="toast__body">
          <div class="toast__title" *ngIf="toast.title">
            {{ toast.title }}
          </div>
          <div 
            class="toast__message" 
            [id]="'toast-' + toast.id + '-message'"
          >
            {{ toast.message }}
          </div>
          
          <div class="toast__actions" *ngIf="toast.actions && toast.actions.length > 0">
            <button
              *ngFor="let action of toast.actions"
              nz-button
              [nzType]="getActionButtonType(action.style)"
              [nzSize]="'small'"
              (click)="onActionClick(action)"
              class="toast__action"
            >
              {{ action.label }}
            </button>
          </div>
        </div>
        
        <button
          *ngIf="toast.dismissible"
          class="toast__close"
          nz-button
          nzType="text"
          nzSize="small"
          (click)="onDismiss()"
          [attr.aria-label]="'Fechar notificação'"
          [attr.title]="'Fechar notificação'"
        >
          <span nz-icon nzType="close" nzTheme="outline"></span>
        </button>
      </div>
      
      <div 
        *ngIf="toast.duration && toast.duration > 0"
        class="toast__progress"
        [style.animation-duration.ms]="toast.duration"
      ></div>
    </div>
  `,
  styles: [`
    .toast {
      min-width: 300px;
      max-width: 500px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border-left: 4px solid;
      margin-bottom: 12px;
      overflow: hidden;
      position: relative;
    }

    .toast--success {
      border-left-color: #52c41a;
    }

    .toast--error {
      border-left-color: #ff4d4f;
    }

    .toast--warning {
      border-left-color: #faad14;
    }

    .toast--info {
      border-left-color: #1890ff;
    }

    .toast__content {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      gap: 12px;
    }

    .toast__icon {
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 2px;
    }

    .toast--success .toast__icon {
      color: #52c41a;
    }

    .toast--error .toast__icon {
      color: #ff4d4f;
    }

    .toast--warning .toast__icon {
      color: #faad14;
    }

    .toast--info .toast__icon {
      color: #1890ff;
    }

    .toast__body {
      flex: 1;
      min-width: 0;
    }

    .toast__title {
      font-weight: 600;
      margin-bottom: 4px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      line-height: 1.5;
    }

    .toast__message {
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;
    }

    .toast__actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
    }

    .toast__action {
      height: 28px !important;
      font-size: 12px;
    }

    .toast__close {
      flex-shrink: 0;
      width: 20px !important;
      height: 20px !important;
      min-width: 20px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 0 !important;
      color: rgba(0, 0, 0, 0.45) !important;
      margin-top: 2px;
    }

    .toast__close:hover {
      color: rgba(0, 0, 0, 0.75) !important;
      background-color: rgba(0, 0, 0, 0.06) !important;
    }

    .toast__progress {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 3px;
      background: linear-gradient(90deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.2));
      animation: progress linear forwards;
      transform-origin: left;
    }

    @keyframes progress {
      from {
        transform: scaleX(1);
      }
      to {
        transform: scaleX(0);
      }
    }

    /* Focus styles for accessibility */
    .toast__close:focus,
    .toast__action:focus {
      outline: 2px solid #1890ff;
      outline-offset: 2px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      .toast {
        border: 2px solid;
      }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      .toast__progress {
        animation: none;
      }
    }
  `],
  animations: [
    trigger('slideIn', [
      state('in', style({ 
        opacity: 1, 
        transform: 'translateX(0) scale(1)' 
      })),
      state('out', style({ 
        opacity: 0, 
        transform: 'translateX(100%) scale(0.95)' 
      })),
      transition('void => out', [
        style({ opacity: 0, transform: 'translateX(100%) scale(0.95)' })
      ]),
      transition('out => in', [
        style({ opacity: 0, transform: 'translateX(60px) scale(0.95)' }),
        animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', 
          style({ opacity: 1, transform: 'translateX(0) scale(1)' })
        )
      ]),
      transition('in => out', [
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)', 
          style({ opacity: 0, transform: 'translateX(60px) scale(0.95)' })
        )
      ])
    ])
  ]
})
export class ToastComponent implements OnInit, OnDestroy {
  @Input() toast!: ToastInstance;
  @Output() dismiss = new EventEmitter<string>();
  @Output() actionClick = new EventEmitter<{ toastId: string; action: ToastAction }>();

  private progressTimer?: number;
  private isBrowser: boolean;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  ngOnInit(): void {
    // Announce to screen readers
    this.announceToScreenReader();
  }

  ngOnDestroy(): void {
    if (this.progressTimer) {
      clearTimeout(this.progressTimer);
    }
  }

  getIcon(): string {
    switch (this.toast.type) {
      case ToastType.SUCCESS:
        return 'check-circle';
      case ToastType.ERROR:
        return 'close-circle';
      case ToastType.WARNING:
        return 'exclamation-circle';
      case ToastType.INFO:
        return 'info-circle';
      default:
        return 'info-circle';
    }
  }

  getActionButtonType(style?: string): NzButtonType {
    switch (style) {
      case 'primary':
        return 'primary';
      case 'danger':
        return 'primary';
      default:
        return 'default';
    }
  }

  onDismiss(): void {
    this.dismiss.emit(this.toast.id);
  }

  onActionClick(action: ToastAction): void {
    this.actionClick.emit({ toastId: this.toast.id, action });
    action.action();
  }

  private announceToScreenReader(): void {
    // Only run in browser environment
    if (!this.isBrowser) {
      return;
    }

    // For screen readers, announce the toast content
    const announcement = this.toast.title 
      ? `${this.toast.title}. ${this.toast.message}`
      : this.toast.message;
    
    // Create a temporary element to announce the content
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', this.toast.type === 'error' ? 'assertive' : 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = announcement;
    
    document.body.appendChild(announcer);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  }
}