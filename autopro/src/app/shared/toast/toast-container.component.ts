import { Component, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ToastComponent } from './toast.component';
import { ToastService, ToastInstance, ToastPosition, ToastAction } from './toast.service';

@Component({
  selector: 'app-toast-container',
  standalone: true,
  imports: [CommonModule, ToastComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="toast-container">
      <!-- Top positions -->
      <div class="toast-position toast-position--top-left">
        <app-toast
          *ngFor="let toast of getToastsByPosition('top-left') | async; trackBy: trackByFn"
          [toast]="toast"
          (dismiss)="onDismiss($event)"
          (actionClick)="onActionClick($event)"
        ></app-toast>
      </div>

      <div class="toast-position toast-position--top-center">
        <app-toast
          *ngFor="let toast of getToastsByPosition('top-center') | async; trackBy: trackByFn"
          [toast]="toast"
          (dismiss)="onDismiss($event)"
          (actionClick)="onActionClick($event)"
        ></app-toast>
      </div>

      <div class="toast-position toast-position--top-right">
        <app-toast
          *ngFor="let toast of getToastsByPosition('top-right') | async; trackBy: trackByFn"
          [toast]="toast"
          (dismiss)="onDismiss($event)"
          (actionClick)="onActionClick($event)"
        ></app-toast>
      </div>

      <!-- Bottom positions -->
      <div class="toast-position toast-position--bottom-left">
        <app-toast
          *ngFor="let toast of getToastsByPosition('bottom-left') | async; trackBy: trackByFn"
          [toast]="toast"
          (dismiss)="onDismiss($event)"
          (actionClick)="onActionClick($event)"
        ></app-toast>
      </div>

      <div class="toast-position toast-position--bottom-center">
        <app-toast
          *ngFor="let toast of getToastsByPosition('bottom-center') | async; trackBy: trackByFn"
          [toast]="toast"
          (dismiss)="onDismiss($event)"
          (actionClick)="onActionClick($event)"
        ></app-toast>
      </div>

      <div class="toast-position toast-position--bottom-right">
        <app-toast
          *ngFor="let toast of getToastsByPosition('bottom-right') | async; trackBy: trackByFn"
          [toast]="toast"
          (dismiss)="onDismiss($event)"
          (actionClick)="onActionClick($event)"
        ></app-toast>
      </div>
    </div>

    <!-- Screen reader live region for announcements -->
    <div 
      class="sr-only"
      aria-live="polite"
      aria-atomic="true"
      #announcer
    ></div>
  `,
  styles: [`
    .toast-container {
      position: fixed;
      z-index: 2000;
      pointer-events: none;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    .toast-position {
      position: absolute;
      pointer-events: auto;
      max-height: 100vh;
      overflow-y: auto;
      padding: 20px;
      box-sizing: border-box;
    }

    /* Top positions */
    .toast-position--top-left {
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .toast-position--top-center {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .toast-position--top-right {
      top: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    /* Bottom positions */
    .toast-position--bottom-left {
      bottom: 0;
      left: 0;
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
    }

    .toast-position--bottom-center {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column-reverse;
      align-items: center;
    }

    .toast-position--bottom-right {
      bottom: 0;
      right: 0;
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-end;
    }

    /* Screen reader only */
    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .toast-position {
        padding: 16px;
      }

      .toast-position--top-left,
      .toast-position--top-center,
      .toast-position--top-right {
        left: 0;
        right: 0;
        transform: none;
        display: flex;
        flex-direction: column;
        align-items: stretch;
      }

      .toast-position--bottom-left,
      .toast-position--bottom-center,
      .toast-position--bottom-right {
        left: 0;
        right: 0;
        transform: none;
        display: flex;
        flex-direction: column-reverse;
        align-items: stretch;
      }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
      .toast-container {
        z-index: 2100;
      }
    }
  `]
})
export class ToastContainerComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  constructor(private toastService: ToastService) {}

  ngOnInit(): void {
    // Additional initialization if needed
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getToastsByPosition(position: string): Observable<ToastInstance[]> {
    return new Observable(observer => {
      this.toastService.getToasts()
        .pipe(takeUntil(this.destroy$))
        .subscribe(toasts => {
          const filteredToasts = toasts
            .filter(toast => toast.position === position || 
              (!toast.position && position === ToastPosition.TOP_RIGHT))
            .sort((a, b) => a.timestamp - b.timestamp);
          observer.next(filteredToasts);
        });
    });
  }

  onDismiss(toastId: string): void {
    this.toastService.dismiss(toastId);
  }

  onActionClick(event: { toastId: string; action: ToastAction }): void {
    // Action is already executed in ToastComponent, 
    // we could add additional logic here if needed
  }

  trackByFn(index: number, toast: ToastInstance): string {
    return toast.id;
  }
}