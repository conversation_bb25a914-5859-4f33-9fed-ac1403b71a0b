# Sistema de Toast Notifications

Um sistema moderno e acessível de notificações toast para substituir os tradicionais `alert()`, `confirm()` e componentes de notificação do ng-zorro-antd.

## ✨ Funcionalidades

- 🎨 **4 tipos de toast**: Success, Error, Warning, Info
- 📍 **6 posições configuráveis**: Top/Bottom × Left/Center/Right
- ♿ **Totalmente acessível**: WCAG 2.1 AA compliant
- 🎭 **Animações suaves**: Transições otimizadas e responsivas
- 🎯 **Ações customizáveis**: Botões de ação nos toasts
- ⏱️ **Auto-dismiss configurável**: Controle total da duração
- 📱 **Responsivo**: Adaptação automática para mobile
- 🔧 **API simples**: Substitui alert/confirm com facilidade
- 🚀 **Performance otimizada**: Change Detection OnPush

## 🚀 Instalação e Configuração

### 1. Importar o ToastContainerComponent

No `app.component.ts`:

```typescript
import { ToastContainerComponent } from './shared/toast/toast-container.component';

@Component({
  imports: [
    // outros imports...
    ToastContainerComponent
  ],
  // ...
})
export class AppComponent {}
```

### 2. Adicionar o container no template

No `app.component.html`:

```html
<router-outlet></router-outlet>
<app-toast-container></app-toast-container>
```

### 3. Usar o ToastService

Em qualquer componente:

```typescript
import { ToastService } from './shared/toast/toast.service';

constructor(private toastService: ToastService) {}
```

## 📖 Uso Básico

### Tipos Simples

```typescript
// Sucesso
this.toastService.success('Operação realizada com sucesso!');

// Erro
this.toastService.error('Algo deu errado!', 'Erro');

// Aviso
this.toastService.warning('Atenção: dados não salvos');

// Informação
this.toastService.info('Nova versão disponível');
```

### Com Configurações Avançadas

```typescript
this.toastService.success(
  'Usuário criado com sucesso!',
  'Sucesso',
  {
    duration: 8000,
    position: ToastPosition.TOP_CENTER,
    actions: [
      {
        label: 'Ver Detalhes',
        action: () => this.openDetails(),
        style: 'primary'
      }
    ]
  }
);
```

## 🔄 Migração de Código Existente

### Substituindo alert()

**ANTES:**
```typescript
alert('Usuário salvo!');
```

**DEPOIS:**
```typescript
this.toastService.success('Usuário salvo!');
```

### Substituindo confirm()

**ANTES:**
```typescript
if (confirm('Tem certeza?')) {
  this.deleteUser();
}
```

**DEPOIS:**
```typescript
this.toastService.show({
  type: ToastType.WARNING,
  title: 'Confirmar',
  message: 'Tem certeza?',
  duration: 0,
  actions: [
    {
      label: 'Sim',
      action: () => this.deleteUser(),
      style: 'danger'
    },
    {
      label: 'Cancelar',
      action: () => {}
    }
  ]
});
```

### Substituindo NzNotificationService

**ANTES:**
```typescript
this.notification.success('Título', 'Mensagem');
this.notification.error('Erro', 'Mensagem de erro');
```

**DEPOIS:**
```typescript
this.toastService.success('Mensagem', 'Título');
this.toastService.error('Mensagem de erro', 'Erro');
```

## 🎯 Exemplos Práticos

### 1. Operação com Loading

```typescript
saveUser() {
  // Mostra loading
  const loadingId = this.toastService.info(
    'Salvando usuário...', 
    'Aguarde',
    { duration: 0 }
  );

  this.userService.save(this.user).subscribe({
    next: () => {
      this.toastService.dismiss(loadingId);
      this.toastService.success('Usuário salvo com sucesso!');
    },
    error: () => {
      this.toastService.dismiss(loadingId);
      this.toastService.error('Erro ao salvar usuário');
    }
  });
}
```

### 2. Ação com Desfazer

```typescript
removeItem(item: any) {
  // Remove o item da lista
  this.items = this.items.filter(i => i.id !== item.id);
  
  // Mostra toast com opção de desfazer
  this.toastService.success(
    'Item removido da lista',
    undefined,
    {
      duration: 8000,
      actions: [
        {
          label: 'Desfazer',
          action: () => {
            this.items.push(item);
            this.toastService.info('Remoção desfeita');
          }
        }
      ]
    }
  );
}
```

### 3. Confirmação de Exclusão

```typescript
deleteUser(id: number) {
  this.toastService.show({
    type: ToastType.WARNING,
    title: 'Confirmar Exclusão',
    message: 'Esta ação não pode ser desfeita.',
    duration: 0,
    position: ToastPosition.TOP_CENTER,
    actions: [
      {
        label: 'Excluir',
        action: () => this.performDelete(id),
        style: 'danger'
      },
      {
        label: 'Cancelar',
        action: () => {}
      }
    ]
  });
}
```

### 4. Progresso em Lote

```typescript
bulkOperation(items: any[]) {
  const total = items.length;
  let completed = 0;
  
  const progressId = this.toastService.info(
    \`Processando... (0/\${total})\`,
    'Operação em Andamento',
    { duration: 0 }
  );

  items.forEach((item, index) => {
    this.processItem(item).subscribe(() => {
      completed++;
      
      if (completed === total) {
        this.toastService.dismiss(progressId);
        this.toastService.success('Todos os itens processados!');
      } else {
        this.toastService.updateToast(progressId, {
          message: \`Processando... (\${completed}/\${total})\`
        });
      }
    });
  });
}
```

## 🎨 Posicionamento

```typescript
// Disponível em ToastPosition enum
ToastPosition.TOP_LEFT
ToastPosition.TOP_CENTER
ToastPosition.TOP_RIGHT
ToastPosition.BOTTOM_LEFT
ToastPosition.BOTTOM_CENTER
ToastPosition.BOTTOM_RIGHT
```

## ⚙️ Configuração Global

```typescript
// No app.component.ts ou em um serviço de configuração
constructor(private toastService: ToastService) {
  // Configurações padrão
  this.toastService.setDefaultConfig({
    duration: 6000,
    position: ToastPosition.TOP_RIGHT,
    dismissible: true
  });
}
```

## ♿ Acessibilidade

O sistema foi desenvolvido seguindo as diretrizes WCAG 2.1 AA:

- ✅ **ARIA Labels**: Todos os elementos possuem labels apropriados
- ✅ **Live Regions**: Anúncios automáticos para screen readers
- ✅ **Keyboard Navigation**: Navegação completa via teclado
- ✅ **Focus Management**: Foco adequado nos botões de ação
- ✅ **Color Contrast**: Contraste adequado para todos os tipos
- ✅ **Reduced Motion**: Respeitam preferências de movimento reduzido
- ✅ **High Contrast**: Suporte para modo alto contraste

## 📱 Responsividade

- Em telas mobile (< 768px), todos os toasts ocupam a largura total
- Posicionamento se adapta automaticamente
- Tamanhos de fonte e espaçamentos otimizados para touch

## 🎛️ API Completa

### ToastService Methods

```typescript
// Tipos específicos
success(message: string, title?: string, options?: Partial<ToastConfig>): string
error(message: string, title?: string, options?: Partial<ToastConfig>): string
warning(message: string, title?: string, options?: Partial<ToastConfig>): string
info(message: string, title?: string, options?: Partial<ToastConfig>): string

// Método genérico
show(config: ToastConfig): string

// Controle
dismiss(id: string): void
dismissAll(): void
updateToast(id: string, updates: Partial<ToastConfig>): void

// Configuração
setDefaultConfig(config: Partial<ToastConfig>): void
```

### ToastConfig Interface

```typescript
interface ToastConfig {
  id?: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;           // 0 = não remove automaticamente
  position?: ToastPosition;
  dismissible?: boolean;       // mostra botão X
  actions?: ToastAction[];     // botões de ação
  data?: any;                 // dados customizados
}
```

### ToastAction Interface

```typescript
interface ToastAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}
```

## 🏗️ Arquitetura

- **ToastService**: Gerenciamento de estado e API
- **ToastComponent**: Componente individual do toast
- **ToastContainerComponent**: Container que gerencia posicionamento
- **Standalone Components**: Não requer NgModule

## 🎯 Vantagens sobre Soluções Tradicionais

1. **Não Bloqueia a UI**: Diferente de alert/confirm
2. **Ações Contextuais**: Botões diretamente no toast
3. **Feedback Rico**: Progress, loading, undo
4. **Acessibilidade Completa**: WCAG 2.1 AA compliance
5. **Performance**: Change detection otimizada
6. **Flexibilidade**: API poderosa e customizável

## 🔧 Personalização

### CSS Custom Properties

```css
:root {
  --toast-success-color: #52c41a;
  --toast-error-color: #ff4d4f;
  --toast-warning-color: #faad14;
  --toast-info-color: #1890ff;
  --toast-border-radius: 8px;
  --toast-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### Tema Customizado

```typescript
// Criar ToastThemeService para temas customizados
@Injectable()
export class ToastThemeService {
  applyDarkTheme() {
    // Aplicar variáveis CSS para tema escuro
  }
}
```

## 📋 TODO / Melhorias Futuras

- [ ] Tema escuro automático
- [ ] Persistência de toasts entre navegações
- [ ] Templates customizáveis
- [ ] Integração com notificações do browser
- [ ] Suporte a rich content (HTML/componentes)
- [ ] Métricas de interação
- [ ] Animações customizáveis

## 🐛 Problemas Conhecidos

- Nenhum problema crítico identificado
- Testado em Chrome, Firefox, Safari e Edge
- Suporte completo ao Angular 19.2+

## 📄 Licença

Este código é parte do projeto AutoPro e segue as mesmas diretrizes de licença.