export interface AttendanceModel {
    id: number;
    guid: string;
    dateRequest: Date;
    dateAttendance?: Date;
    dateFinish?: Date;
    customerId: number;
    professionalId?: number;
    referenceCar: string;
    brandId: number;
    modelId: number;
    postalCode: string;
    address: string;
    number: string;
    addressComplement: string;
    districtId: number;
    cityId: number;
    stateId: number;
    status: number;
    
    // ✅ NOVOS CAMPOS: Service Request Information
    vehicleOwnerName?: string;
    vehicleOwnerPhone?: string;
    hasCautelarService: boolean;
    hasVistoriaService: boolean;
    customerNotes?: string;
    
    // ✅ NOVOS CAMPOS: Alternative Vehicle Address
    vehicleAtDifferentAddress: boolean;
    vehiclePostalCode?: string;
    vehicleAddress?: string;
    vehicleNumber?: string;
    vehicleAddressComplement?: string;
    vehicleDistrictId?: number;
    vehicleCityId?: number;
    vehicleStateId?: number;
    
    // Navigation properties
    customer?: CustomerModel;
    professional?: ProfessionalModel;
    brand?: BrandModel;
    model?: ModelModel;
    district?: DistrictModel;
    city?: CityModel;
    state?: StateModel;
    
    // ✅ NOVOS Navigation properties: Vehicle Address
    vehicleDistrict?: DistrictModel;
    vehicleCity?: CityModel;
    vehicleState?: StateModel;
}

export interface CustomerModel {
    id: number;
    guid: string;
    name: string;
    type: string;
    fiscalNumber: string;
    birthDate: number;
    address: string;
    number: string;
    addressComplement: string;
    districtId: number;
    cityId: number;
    stateId: number;
    active: boolean;
    deleted: boolean;
}

export interface ProfessionalModel {
    id: number;
    guid: string;
    name: string;
    cpf: string;
    cellphone1: string;
    cellPhone2: string;
    postalCode: string;
    address: string;
    number: string;
    addressComplement: string;
    districtId: number;
    cityId: number;
    stateId: number;
    status: number;
    source: number;
    active: boolean;
    deleted: boolean;
}

export interface BrandModel {
    id: number;
    brand: string;
}

export interface ModelModel {
    id: number;
    model: string;
    brandId: number;
}

export interface DistrictModel {
    id: number;
    district: string;
    code: string;
}

export interface CityModel {
    id: number;
    city: string;
}

export interface StateModel {
    id: number;
    state: string;
}

export enum AttendanceStatus {
    Requested = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4
    // TODO: Future payment statuses
    // AwaitingPayment = 5,
    // Paid = 6
}

export const AttendanceStatusLabels: { [key: number]: string } = {
    [AttendanceStatus.Requested]: 'Solicitado',
    [AttendanceStatus.InProgress]: 'Em Andamento',
    [AttendanceStatus.Completed]: 'Concluído',
    [AttendanceStatus.Cancelled]: 'Cancelado'
};

// Paginação - Interfaces para paginação server-side
export interface AttendancePagedRequest {
    pageNumber: number;
    pageSize: number;
    searchTerm?: string;
    sortBy?: string;
    sortDescending: boolean;
    statusFilter?: number;
}

// DTO otimizado para listagem (corresponde ao AttendanceListDTO do backend)
export interface AttendanceListModel {
    id: number;
    guid: string;
    dateRequest: Date;
    dateAttendance?: Date;
    dateFinish?: Date;
    status: number;
    
    // Dados básicos do cliente
    customerId: number;
    customerName?: string;
    customerType?: string;
    customerFiscalNumber?: string;
    
    // Dados básicos do profissional
    professionalId?: number;
    professionalName?: string;
    
    // Informações do veículo
    referenceCar: string;
    brandId: number;
    brandName?: string;
    modelId: number;
    modelName?: string;
    
    // Serviços
    hasCautelarService: boolean;
    hasVistoriaService: boolean;
    
    // Endereço principal
    address: string;
    number: string;
    addressComplement?: string;
    postalCode: string;
    districtName?: string;
    cityName?: string;
    stateName?: string;
    
    // Notas do cliente
    customerNotes?: string;
}

export interface AttendancePagedResult {
    items: AttendanceListModel[];
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
}