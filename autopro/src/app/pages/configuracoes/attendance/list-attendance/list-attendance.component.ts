import { CommonModule } from '@angular/common';
import { Component, ChangeDetectionStrategy, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { TituloComponent } from '../../../../components/titulo/titulo.component';
import { GlobalService } from '../../../../../services/global.service';
import { AttendanceModel, AttendanceListModel, AttendanceStatus, AttendanceStatusLabels, AttendancePagedRequest, AttendancePagedResult } from '../model/attendance.model';
import { AttendanceDetailsModalComponent } from '../../../../components/modals/attendance-details-modal/attendance-details-modal.component';

@Component({
  selector: 'app-list-attendance',
  imports: [
    TituloComponent,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    CommonModule,
    FormsModule,
    NzIconModule,
    NzPopconfirmModule,
    NzToolTipModule,
    NzSelectModule,
    NzDropDownModule,
    NzInputModule
  ],
  providers: [
    NzMessageService,
    NzNotificationService,
    NzModalService
  ],
  templateUrl: './list-attendance.component.html',
  styleUrl: './list-attendance.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: { ngSkipHydration: 'true' }
})
export class ListAttendanceComponent implements OnDestroy {

  tituloPagina: string = "Lista de Solicitações";
  firstLevel: string = "Configurações";
  secondLevel: string = "Solicitações de Serviço";
  subtitle: string = "Listagem de Solicitações de Serviço";
  isLoadingTable: boolean = false;
  lstAttendance: AttendanceListModel[] = [];
  
  // Filters
  statusFilter: number | null = null;
  
  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  pageSizeOptions = [10, 20, 50, 100];
  
  // Search properties
  searchTerm = '';
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();
  
  // Sort properties
  sortBy: string = 'dateRequest';
  sortDescending: boolean = true;
  
  // Cache system
  private cache = new Map<string, { data: AttendancePagedResult, timestamp: number }>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes
  
  // Status options
  statusOptions = Object.keys(AttendanceStatus)
    .filter(k => !isNaN(Number(k)))
    .map(k => ({
      label: AttendanceStatusLabels[Number(k)],
      value: Number(k)
    }));

  // Expandable rows functionality
  expandedRows = new Set<number>();

  constructor(
    private messageService: NzMessageService,
    private notificationService: NzNotificationService,
    private globalService: GlobalService,
    private cdr: ChangeDetectorRef,
    private modalService: NzModalService
  ) {
    // Configure search debounce
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.currentPage = 1;
      this.clearCache();
      this.loadAttendancesPaged();
    });
  }

  ngOnInit() {
    this.loadAttendancesPaged();
  }
  
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }



  onDelete(id: number) {
    this.globalService.delete(`attendance/delete/${id}`).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Solicitação cancelada com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.clearCache();
        this.loadAttendancesPaged();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao cancelar solicitação!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }


  
  loadAttendancesPaged() {
    // Debug: Detectar chamadas excessivas
    if (this.isLoadingTable) {
      return;
    }

    const request: AttendancePagedRequest = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm || undefined,
      sortBy: this.sortBy,
      sortDescending: this.sortDescending,
      statusFilter: this.statusFilter || undefined,
    };
    
    // Check cache first
    const cacheKey = JSON.stringify(request);
    const cachedData = this.cache.get(cacheKey);
    
    if (cachedData && (Date.now() - cachedData.timestamp) < this.cacheExpiry) {
      this.applyPagedResult(cachedData.data);
      return;
    }
    
    this.isLoadingTable = true;
    
    const params = new URLSearchParams();
    params.append('pageNumber', request.pageNumber.toString());
    params.append('pageSize', request.pageSize.toString());
    if (request.searchTerm) {
      params.append('searchTerm', request.searchTerm);
    }
    if (request.sortBy) {
      params.append('sortBy', request.sortBy);
    }
    params.append('sortDescending', request.sortDescending.toString());
    if (request.statusFilter) {
      params.append('statusFilter', request.statusFilter.toString());
    }
    
    this.globalService.get(`attendance/paged?${params.toString()}`).subscribe({
      next: (response: any) => {
        const pagedResult = response as AttendancePagedResult;
        // Cache the result
        this.cache.set(cacheKey, { data: pagedResult, timestamp: Date.now() });
        this.applyPagedResult(pagedResult);
        this.isLoadingTable = false;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.isLoadingTable = false;
        this.cdr.markForCheck();
        this.notificationService.error(
          'Erro',
          'Erro ao carregar solicitações!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }
  
  private applyPagedResult(result: AttendancePagedResult) {
    this.lstAttendance = result.items;
    this.totalItems = result.totalCount;
    this.currentPage = result.pageNumber;
    this.pageSize = result.pageSize;
    this.cdr.markForCheck();
  }

  getStatusTag(status: number): { color: string; label: string } {
    switch (status) {
      case AttendanceStatus.Requested:
        return { color: 'blue', label: AttendanceStatusLabels[status] };
      case AttendanceStatus.InProgress:
        return { color: 'orange', label: AttendanceStatusLabels[status] };
      case AttendanceStatus.Completed:
        return { color: 'green', label: AttendanceStatusLabels[status] };
      case AttendanceStatus.Cancelled:
        return { color: 'red', label: AttendanceStatusLabels[status] };
      default:
        return { color: 'default', label: 'Desconhecido' };
    }
  }

  onStatusFilterChange() {
    this.currentPage = 1;
    this.clearCache();
    this.loadAttendancesPaged();
  }



  updateStatus(attendance: AttendanceListModel, newStatus: number) {
    this.globalService.put(`attendance/${attendance.id}/status/${newStatus}`, {}).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Status atualizado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.clearCache();
        this.loadAttendancesPaged();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao atualizar status!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  viewAttendanceDetails(attendance: AttendanceListModel): void {
    // Primeiro buscar os detalhes completos do atendimento
    this.globalService.getByGuid('attendance', attendance.guid).subscribe({
      next: (response: any) => {
        const modalRef = this.modalService.create({
          nzTitle: `Detalhes da Solicitação #${attendance.id}`,
          nzContent: AttendanceDetailsModalComponent,
          nzData: { attendance: response },
          nzWidth: '90%',
          nzStyle: { top: '20px' },
          nzBodyStyle: { maxHeight: '80vh', overflow: 'auto' },
          nzFooter: null,
          nzMaskClosable: true,
          nzClosable: true,
          nzKeyboard: true,
          nzCentered: false
        });
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao carregar detalhes da solicitação!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  formatDate(date: Date | undefined): string {
    if (!date) return '-';
    return new Date(date).toLocaleString('pt-BR');
  }

  getFullAddress(data: AttendanceListModel): string {
    if (!data.address) return '';
    
    const parts = [];
    if (data.address) parts.push(data.address);
    if (data.number) parts.push(data.number);
    if (data.addressComplement) parts.push(data.addressComplement);
    if (data.districtName) parts.push(data.districtName);
    if (data.cityName) parts.push(data.cityName);
    if (data.stateName) parts.push(data.stateName);
    if (data.postalCode) parts.push(`CEP: ${data.postalCode}`);
    
    return parts.join(', ');
  }

  getVehicleAddress(data: AttendanceListModel): string {
    // AttendanceListModel não tem endereço alternativo de veículo
    // Funcionalidade removida da listagem otimizada para melhor performance
    return '';
  }

  getStatusIcon(status: number): string {
    switch (status) {
      case AttendanceStatus.Requested:
        return 'clock-circle';
      case AttendanceStatus.InProgress:
        return 'play-circle';
      case AttendanceStatus.Completed:
        return 'check-circle';
      case AttendanceStatus.Cancelled:
        return 'close-circle';
      default:
        return 'question-circle';
    }
  }

  truncateText(text: string, maxLength: number): string {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  toggleRowExpansion(index: number): void {
    if (this.expandedRows.has(index)) {
      this.expandedRows.delete(index);
    } else {
      this.expandedRows.add(index);
    }
  }

  cancel() {
    // Método para cancelar ação no popconfirm
  }
  
  // Pagination methods
  onPageIndexChange(pageIndex: number) {
    this.currentPage = pageIndex;
    this.loadAttendancesPaged();
  }
  
  onPageSizeChange(pageSize: number) {
    this.pageSize = pageSize;
    this.currentPage = 1;
    this.clearCache();
    this.loadAttendancesPaged();
  }
  
  // Search methods
  onSearch() {
    this.searchSubject.next(this.searchTerm);
  }

  clearSearch() {
    this.searchTerm = '';
    this.onSearch();
  }
  
  // Sort methods
  onSort(field: string) {
    if (this.sortBy === field) {
      this.sortDescending = !this.sortDescending;
    } else {
      this.sortBy = field;
      this.sortDescending = true;
    }
    this.currentPage = 1;
    this.clearCache();
    this.loadAttendancesPaged();
  }
  
  getSortDirection(field: string): string | null {
    if (this.sortBy !== field) return null;
    return this.sortDescending ? 'descend' : 'ascend';
  }
  
  // Cache methods
  clearCache() {
    this.cache.clear();
  }

  // TrackBy function para melhor performance do *ngFor
  trackByFn(index: number, item: AttendanceListModel): number {
    return item.id;
  }

}