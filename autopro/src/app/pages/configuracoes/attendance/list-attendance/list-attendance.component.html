<app-titulo [title]="tituloPagina" 
            [firstLevel]="firstLevel" 
            [secondLevel]="secondLevel"
            [subtitle]="subtitle">
</app-titulo>

<div class="page-content">
  <!-- Busca e Filtros na mesma linha -->
  <div class="search-section" style="margin-bottom: 16px;">
    <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
      <nz-input-group [nzSuffix]="suffixIconButton" style="width: 300px;">
        <input type="text" 
               nz-input 
               placeholder="Buscar por cliente, veículo ou ID..." 
               [(ngModel)]="searchTerm"
               (input)="onSearch()" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button 
                nzType="text" 
                nzSize="small" 
                (click)="clearSearch()" 
                *ngIf="searchTerm"
                nz-tooltip="Limpar busca">
          <i nz-icon nzType="close" nzTheme="outline"></i>
        </button>
        <i nz-icon nzType="search" nzTheme="outline" *ngIf="!searchTerm"></i>
      </ng-template>

      <nz-select [(ngModel)]="statusFilter" 
                 (ngModelChange)="onStatusFilterChange()" 
                 nzPlaceHolder="Filtrar por status"
                 nzAllowClear
                 style="width: 200px;">
        <nz-option nzValue="" nzLabel="Todos"></nz-option>
        <nz-option *ngFor="let status of statusOptions" 
                   [nzValue]="status.value" 
                   [nzLabel]="status.label">
        </nz-option>
      </nz-select>
    </div>
  </div>

    <nz-table #basicTable 
              [nzLoading]="isLoadingTable" 
              ngSkipHydration="true" 
              [nzData]="lstAttendance" 
              [nzBordered]="true" 
              [nzSize]="'middle'"
              [nzFrontPagination]="false"
              [nzShowSizeChanger]="true"
              [nzPageSizeOptions]="pageSizeOptions"
              [nzPageSize]="pageSize"
              [nzPageIndex]="currentPage"
              [nzTotal]="totalItems"
              (nzPageIndexChange)="onPageIndexChange($event)"
              (nzPageSizeChange)="onPageSizeChange($event)">
      <thead>
        <tr>
          <th (click)="onSort('id')" style="cursor: pointer; user-select: none;">
            ID
            <i *ngIf="getSortDirection('id') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
            <i *ngIf="getSortDirection('id') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
            <i *ngIf="getSortDirection('id') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
          </th>
          <th>Cliente</th>
          <th>Veículo</th>
          <th (click)="onSort('status')" style="cursor: pointer; user-select: none;">
            Status
            <i *ngIf="getSortDirection('status') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
            <i *ngIf="getSortDirection('status') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
            <i *ngIf="getSortDirection('status') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
          </th>
          <th (click)="onSort('dateRequest')" style="cursor: pointer; user-select: none;">
            Data Solicitação
            <i *ngIf="getSortDirection('dateRequest') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
            <i *ngIf="getSortDirection('dateRequest') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
            <i *ngIf="getSortDirection('dateRequest') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
          </th>
          <th>Serviços</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index; trackBy: trackByFn">
          <td>{{ data.id }}</td>
          <td>
            <div>
              <strong>{{ data.customerName || 'Cliente não informado' }}</strong>
              <br *ngIf="data.customerType">
              <small *ngIf="data.customerType">{{ data.customerType === 'PF' ? 'CPF' : 'CNPJ' }}</small>
            </div>
          </td>
          <td>
            <div>
              <strong>{{ data.brandName || '-' }} {{ data.modelName || '-' }}</strong>
              <br *ngIf="data.referenceCar">
              <small *ngIf="data.referenceCar">{{ data.referenceCar }}</small>
            </div>
          </td>
          <td>
            <nz-tag [nzColor]="getStatusTag(data.status).color">
              {{ getStatusTag(data.status).label }}
            </nz-tag>
          </td>
          <td>{{ formatDate(data.dateRequest) }}</td>
          <td>
            <div>
              <nz-tag *ngIf="data.hasCautelarService" nzColor="blue">Cautelar</nz-tag>
              <nz-tag *ngIf="data.hasVistoriaService" nzColor="green">Vistoria</nz-tag>
              <span *ngIf="!data.hasCautelarService && !data.hasVistoriaService">-</span>
            </div>
          </td>
          <td>
            <button nz-button 
                    nzType="text" 
                    nz-tooltip="Visualizar Detalhes"
                    nzTooltipTitle="Visualizar Detalhes"
                    (click)="viewAttendanceDetails(data)">
              <i nz-icon nzType="eye" nzTheme="outline"></i>
            </button>
            <a nz-popconfirm
               nzPopconfirmTitle="Deseja cancelar essa solicitação?"
               nzOkText="Ok"
               nzDanger
               nzCancelText="Cancelar"
               (nzOnConfirm)="onDelete(data.id)"
               (nzOnCancel)="cancel()"
               nzTooltipTitle="Cancelar Solicitação"
               [class.disabled]="data.status === 4">
              <i nz-icon nzType="delete" nzTheme="outline"></i>
            </a>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>

