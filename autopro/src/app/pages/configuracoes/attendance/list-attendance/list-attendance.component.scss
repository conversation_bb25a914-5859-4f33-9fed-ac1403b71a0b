// Estilos para a página de listagem de atendimentos
// Seguindo o padrão das outras páginas do sistema

.attendance-container {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin: 24px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 16px;

  button {
    margin-left: auto;
  }
}

.page-content {
  nz-table {
    margin-top: 24px;
    
    .ant-table {
      th, td {
        text-align: left;
        vertical-align: top;
        padding: 12px 8px;
      }
      
      th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
      }
      
      tbody tr {
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

// Estilos para tags de status seguindo o padrão
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

// Estilos para ações desabilitadas
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

// Responsividade básica
@media (max-width: 768px) {
  .attendance-container {
    padding: 16px;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    button {
      margin-left: 0;
    }
  }
  
  .page-content {
    nz-table .ant-table {
      font-size: 12px;
      
      th, td {
        padding: 8px 4px;
      }
    }
  }
}