import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { debounceTime, distinctUntilChanged, Subject, takeUntil, switchMap, finalize, catchError, of, timeout } from 'rxjs';
import { TituloComponent } from '../../../../components/titulo/titulo.component';
import { CadCautelaristaComponent } from '../cad-cautelarista/cad-cautelarista.component';
import { GlobalService } from '../../../../../services/global.service';
import { CautelaristaModel, CautelaristaListModel, PagedRequest, PagedResult } from '../model/cautelarista.model';

@Component({
  selector: 'app-list-cautelarista',
  imports: [
    TituloComponent,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    CommonModule,
    NzIconModule,
    CadCautelaristaComponent,
    NzPopconfirmModule,
    NzToolTipModule,
    NzPaginationModule,
    NzInputModule,
    FormsModule
  ],
  providers: [
    NzMessageService,
    NzNotificationService
  ],
  templateUrl: './list-cautelarista.component.html',
  styleUrl: './list-cautelarista.component.scss',
  host: { ngSkipHydration: 'true' },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListCautelaristaComponent {
  tituloPagina = "Lista de Cautelaristas";
  firstLevel = "Configurações";
  secondLevel = "Cautelaristas";
  subtitle = "Listagem de Cautelaristas cadastrados pelo site";
  
  // Estado simples
  isLoading = false;
  isLoadingEdit: { [key: number]: boolean } = {};
  isLoadingApprove: { [key: number]: boolean } = {};
  lstCautelarista: CautelaristaListModel[] = [];
  showModalNovo = false;
  cautelaristaModel: CautelaristaModel | null = null;

  // Paginação
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  pageSizeOptions = [10, 20, 50, 100];

  // Busca e ordenação
  searchTerm = '';
  sortBy = 'name';
  sortDescending = false;
  
  // Cache simples para performance
  private cache = new Map<string, { data: PagedResult<CautelaristaListModel>, timestamp: number }>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutos
  
  private destroy$ = new Subject<void>();
  private searchSubject = new Subject<string>();

  constructor(
    private messageService: NzMessageService,
    private notificationService: NzNotificationService,
    private globalService: GlobalService,
    private cdr: ChangeDetectorRef
  ) {
    // Configurar debounce para busca
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.currentPage = 1;
      this.clearCache(); // Limpar cache na busca
      this.loadCautelaristas();
    });
  }

  ngOnInit() {
    this.loadCautelaristas();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }


  onModalClosed() {
    this.showModalNovo = false;
    this.clearCache(); // Limpar cache após modificação
    this.loadCautelaristas();
  }

  onEdit(item: CautelaristaListModel) {
    this.isLoadingEdit[item.id] = true;
    this.cdr.markForCheck();
    
    // Primeiro carregar dados básicos (sem documentos pesados)
    this.globalService.getbyid('cautelarist', item.id).subscribe({
      next: (response: any) => {
        this.cautelaristaModel = response;
        this.showModalNovo = true;
        this.isLoadingEdit[item.id] = false;
        this.cdr.markForCheck();
        
        // Carregar documentos separadamente para melhor performance
        this.loadDocuments(item.id);
      },
      error: () => {
        this.isLoadingEdit[item.id] = false;
        this.cdr.markForCheck();
        this.notificationService.error(
          'Erro',
          'Erro ao carregar dados do cautelarista!'
        );
      }
    });
  }

  private loadDocuments(id: number) {
    this.globalService.get(`cautelarist/${id}/documents`).subscribe({
      next: (documents: any) => {
        if (this.cautelaristaModel && documents) {
          // Atualizar apenas os campos de documentos/imagens
          this.cautelaristaModel.cnhDocumentBase64 = documents.cnhDocumentBase64;
          this.cautelaristaModel.proofOfResidenceBase64 = documents.proofOfResidenceBase64;
          this.cautelaristaModel.qualificationsDocumentBase64 = documents.qualificationsDocumentBase64;
          this.cautelaristaModel.cnhImageBase64 = documents.cnhImageBase64;
          this.cautelaristaModel.qualificationsImageBase64 = documents.qualificationsImageBase64;
          this.cdr.markForCheck();
        }
      },
      error: (error: any) => {
        // Não mostrar erro ao usuário para não interromper o fluxo
      }
    });
  }

  onDelete(id: number) {
    this.globalService.delete(`cautelarist/${id}`).subscribe({
      next: () => {
        this.notificationService.success(
          'Sucesso',
          'Cautelarista excluído com sucesso!'
        );
        this.clearCache(); // Limpar cache após exclusão
        this.loadCautelaristas();
      },
      error: () => {
        this.notificationService.error(
          'Erro',
          'Erro ao excluir cautelarista!'
        );
      }
    });
  }

  onApprove(item: CautelaristaListModel) {
    const newStatus = !item.isAprove;
    this.isLoadingApprove[item.id] = true;
    this.cdr.markForCheck();
    
    this.globalService.patch(`cautelarist/${item.id}/approval`, { isAprove: newStatus }).subscribe({
      next: () => {
        this.isLoadingApprove[item.id] = false;
        this.cdr.markForCheck();
        this.notificationService.success(
          'Sucesso',
          `Cautelarista ${newStatus ? 'aprovado' : 'reprovado'} com sucesso!`
        );
        this.clearCache(); // Limpar cache após aprovação
        this.loadCautelaristas();
      },
      error: () => {
        this.isLoadingApprove[item.id] = false;
        this.cdr.markForCheck();
        this.notificationService.error(
          'Erro',
          'Erro ao atualizar status de aprovação!'
        );
      }
    });
  }

  onNew() {
    this.showModalNovo = true;
    this.cautelaristaModel = null;
  }

  cancel(): void {
    this.messageService.info('Operação cancelada');
  }

  loadCautelaristas() {
    const cacheKey = `${this.currentPage}_${this.pageSize}_${this.searchTerm}_${this.sortBy}_${this.sortDescending}`;
    
    // Verificar cache primeiro
    const cached = this.cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      this.lstCautelarista = cached.data.items;
      this.totalItems = cached.data.totalCount;
      this.cdr.markForCheck();
      return;
    }

    this.isLoading = true;
    
    const request: PagedRequest = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm.trim() || undefined,
      sortBy: this.sortBy,
      sortDescending: this.sortDescending
    };

    const params = new URLSearchParams();
    Object.entries(request).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, String(value));
      }
    });

    this.globalService.get(`cautelarist/paged?${params.toString()}`)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoading = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (response: any) => {
          this.lstCautelarista = response?.items || [];
          this.totalItems = response?.totalCount || 0;
          
          // Armazenar no cache
          this.cache.set(cacheKey, {
            data: response,
            timestamp: Date.now()
          });
          
          this.cdr.markForCheck();
        },
        error: () => {
          this.notificationService.error(
            'Erro',
            'Erro ao carregar cautelaristas!'
          );
        }
      });
  }

  getStatusText(origem: boolean): string {
    return origem ? 'Site' : 'Admin';
  }

  // Métodos de paginação
  onPageIndexChange(pageIndex: number): void {
    this.currentPage = pageIndex;
    this.loadCautelaristas();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1;
    this.clearCache(); // Limpar cache ao mudar tamanho da página
    this.loadCautelaristas();
  }

  // Método de busca com debounce
  onSearch(): void {
    this.searchSubject.next(this.searchTerm);
  }

  // Limpar busca
  clearSearch(): void {
    this.searchTerm = '';
    this.onSearch();
  }

  // Método para ordenação
  onSort(field: string): void {
    if (this.sortBy === field) {
      this.sortDescending = !this.sortDescending;
    } else {
      this.sortBy = field;
      this.sortDescending = false;
    }
    this.currentPage = 1;
    this.clearCache(); // Limpar cache ao ordenar
    this.loadCautelaristas();
  }

  // Método para obter o tipo de seta de ordenação
  getSortDirection(field: string): 'asc' | 'desc' | null {
    if (this.sortBy !== field) {
      return null;
    }
    return this.sortDescending ? 'desc' : 'asc';
  }

  // TrackBy function para melhor performance
  trackByFn(index: number, item: CautelaristaListModel): number {
    return item.id;
  }

  // Limpar cache
  private clearCache(): void {
    this.cache.clear();
  }
}