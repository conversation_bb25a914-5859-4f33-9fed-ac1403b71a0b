.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.btnNovo {
  display: flex;
  justify-content: flex-end;
}

.page-content {
  background: white;
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// Estilo específico para ações de aprovação
.approval-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

// Indicadores visuais para status
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}