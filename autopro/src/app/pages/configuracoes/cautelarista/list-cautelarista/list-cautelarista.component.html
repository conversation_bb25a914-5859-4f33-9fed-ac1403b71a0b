<app-titulo 
    [title]="tituloPagina" 
    [firstLevel]="firstLevel" 
    [secondLevel]="secondLevel"
    [subtitle]="subtitle"
    >
</app-titulo>

<!-- NOVO -->
<div class="page-header">
    <app-cad-cautelarista 
        [isVisible]="showModalNovo" 
        [model]="cautelaristaModel"
        (modalClose)="onModalClosed()">
    </app-cad-cautelarista> 
    <div class="btnNovo">
        <button nz-button nzType="primary" (click)="onNew()">+ Novo</button>
    </div>
 </div> 

 <div class="page-content">
    <!-- Busca -->
    <div class="search-section" style="margin-bottom: 16px;">
        <nz-input-group [nzSuffix]="suffixIconButton" style="width: 300px;">
            <input 
                type="text" 
                nz-input 
                placeholder="Buscar por nome, email ou celular..." 
                [(ngModel)]="searchTerm"
                (input)="onSearch()"
                (keyup.enter)="onSearch()">
        </nz-input-group>
        <ng-template #suffixIconButton>
            <button 
                nz-button 
                nzType="text" 
                nzSize="small" 
                (click)="clearSearch()" 
                *ngIf="searchTerm"
                nz-tooltip="Limpar busca">
                <i nz-icon nzType="close" nzTheme="outline"></i>
            </button>
            <i nz-icon nzType="search" nzTheme="outline" *ngIf="!searchTerm"></i>
        </ng-template>
    </div>

    <!-- Tabela com binding melhorado -->
    <nz-table 
        #basicTable 
        [nzLoading]="isLoading" 
        ngSkipHydration="true" 
        [nzData]="lstCautelarista" 
        [nzBordered]="true" 
        [nzSize]="'middle'"
        [nzFrontPagination]="false"
        [nzShowSizeChanger]="true"
        [nzPageSizeOptions]="pageSizeOptions"
        [nzPageSize]="pageSize"
        [nzPageIndex]="currentPage"
        [nzTotal]="totalItems"
        [nzShowTotal]="totalTemplate"
        (nzPageIndexChange)="onPageIndexChange($event)"
        (nzPageSizeChange)="onPageSizeChange($event)">
        
        <ng-template #totalTemplate let-total let-range="range">
          {{ range[0] }}-{{ range[1] }} de {{ total }} itens
        </ng-template>
        <thead>
          <tr>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('id')">
              ID
              <i *ngIf="getSortDirection('id') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('id') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('id') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('name')">
              Nome
              <i *ngIf="getSortDirection('name') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('name') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('name') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('cellphone')">
              Celular
              <i *ngIf="getSortDirection('cellphone') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('cellphone') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('cellphone') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('email')">
              Email
              <i *ngIf="getSortDirection('email') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('email') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('email') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th>Tipo de Contato</th>
            <th>Origem</th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('isAprove')">
              Status
              <i *ngIf="getSortDirection('isAprove') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('isAprove') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('isAprove') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of basicTable.data; trackBy: trackByFn">
            <td>{{ item.id }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.cellphone }}</td>
            <td>{{ item.email || 'N/A' }}</td>
            <td>{{ item.typeOfContact }}</td>
            <td>
              <nz-tag [nzColor]="item.origem ? 'blue' : 'orange'">
                {{ getStatusText(item.origem) }}
              </nz-tag>
            </td>
            <td>
              <nz-tag [nzColor]="item.isAprove ? 'green' : 'red'">
                {{ item.isAprove ? 'Aprovado' : 'Pendente' }}
              </nz-tag>
            </td>
            <td>
                <button 
                    nz-button 
                    nzTooltipTitle="Editar" 
                    nzType="text" 
                    nz-tooltip="Editar" 
                    (click)="onEdit(item)"
                    [nzLoading]="isLoadingEdit[item.id]"
                    [disabled]="isLoadingEdit[item.id]">
                    <i nz-icon nzType="edit" nzTheme="outline" *ngIf="!isLoadingEdit[item.id]"></i>
                </button>
                
                <button 
                    nz-button 
                    [nzTooltipTitle]="item.isAprove ? 'Reprovar' : 'Aprovar'" 
                    nzType="text" 
                    [nz-tooltip]="item.isAprove ? 'Reprovar' : 'Aprovar'"
                    (click)="onApprove(item)"
                    [style.color]="item.isAprove ? '#ff4d4f' : '#52c41a'"
                    [nzLoading]="isLoadingApprove[item.id]"
                    [disabled]="isLoadingApprove[item.id]">
                    <i nz-icon [nzType]="item.isAprove ? 'close-circle' : 'check-circle'" nzTheme="outline" *ngIf="!isLoadingApprove[item.id]"></i>
                </button>
                
                <a
                    nz-popconfirm
                    nzPopconfirmTitle="Deseja excluir esse cautelarista?"
                    nzOkText="ok"
                    nzCancelText="Cancelar"
                    (nzOnConfirm)="onDelete(item.id)"
                    (nzOnCancel)="cancel()">
                    <button nz-button nzTooltipTitle="Excluir" nzType="text" nz-tooltip="Excluir">
                        <i nz-icon nzType="delete" nzTheme="outline"></i>
                    </button>
                </a>
            </td>
          </tr>
        </tbody>
    </nz-table>
 </div>