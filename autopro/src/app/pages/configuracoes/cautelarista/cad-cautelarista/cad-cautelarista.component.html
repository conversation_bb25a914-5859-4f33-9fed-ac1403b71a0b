<nz-modal
    ngSkipHydration="true"
    nzDraggable
    nzCentered
    [(nzVisible)]="isVisible"
    [nzTitle]="titlePage"
    (nzOnCancel)="handleCancel()"
    nzWidth="900px"
>
    <ng-container *nzModalContent>
        <form nz-form [formGroup]="form" skipHydration="true">
            <!-- Dados Básicos -->
            <nz-divider nzText="Dados Básicos" nzOrientation="left"></nz-divider>
            
            <nz-form-item>
                <nz-form-label [nzSpan]="6" nzFor="name" nzRequired>Nome Completo</nz-form-label>
                <nz-form-control [nzSpan]="14" nzErrorTip="Nome é obrigatório">
                    <input type="hidden" formControlName="id" id="id" />
                    <input nz-input formControlName="name" id="name" placeholder="Nome completo do cautelarista" />
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label [nzSpan]="6" nzFor="cellphone" nzRequired>Celular</nz-form-label>
                <nz-form-control [nzSpan]="14" nzErrorTip="Celular é obrigatório">
                    <input nz-input formControlName="cellphone" id="cellphone" placeholder="(11) 99999-9999" />
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label [nzSpan]="6" nzFor="email">Email</nz-form-label>
                <nz-form-control [nzSpan]="14" nzErrorTip="Email deve ter formato válido">
                    <input nz-input formControlName="email" id="email" type="email" placeholder="<EMAIL>" />
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label [nzSpan]="6" nzFor="typeOfContact" nzRequired>Tipo de Contato</nz-form-label>
                <nz-form-control [nzSpan]="14" nzErrorTip="Tipo de contato é obrigatório">
                    <nz-select formControlName="typeOfContact" id="typeOfContact" nzPlaceHolder="Selecione o tipo de contato">
                        <nz-option *ngFor="let tipo of tiposContato" [nzValue]="tipo.value" [nzLabel]="tipo.label"></nz-option>
                    </nz-select>
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label [nzSpan]="6" nzFor="observacoes">Observações</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <textarea nz-input formControlName="observacoes" id="observacoes" rows="3" placeholder="Observações adicionais"></textarea>
                </nz-form-control>
            </nz-form-item>

            <!-- Documentos -->
            <nz-divider nzText="Documentos" nzOrientation="left"></nz-divider>

            <!-- CNH Document -->
            <nz-form-item>
                <nz-form-label [nzSpan]="24" nzFor="cnhDocument" nzRequired>Documento da CNH (PDF)</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Documento da CNH é obrigatório">
                    <div class="upload-section">
                        <div class="upload-item">
                            <div class="upload-input-container">
                                <input type="text"
                                       class="upload-input"
                                       [value]="getFileName('cnhDocumentBase64')"
                                       placeholder="Selecione o documento da CNH (PDF)"
                                       readonly>
                                <i class="fa fa-check-circle upload-success-icon" *ngIf="form.get('cnhDocumentBase64')?.value"></i>
                            </div>
                            <label class="upload-btn">
                                UPLOAD
                                <input type="file" (change)="onCnhDocumentSelected($event)" style="display: none;" accept=".pdf">
                            </label>
                        </div>
                        <!-- Preview do PDF CNH -->
                        <div class="pdf-preview" *ngIf="form.get('cnhDocumentBase64')?.value">
                            <div class="pdf-preview-header">
                                <span class="pdf-preview-title">Documento da CNH</span>
                                <div class="pdf-preview-actions">
                                    <button type="button" class="preview-btn" (click)="expandPdf(form.get('cnhDocumentBase64')?.value, 'Documento da CNH')" title="Visualizar PDF">
                                        <i class="fa fa-eye"></i> Visualizar
                                    </button>
                                    <button type="button" class="remove-pdf-btn" (click)="removeFile('cnhDocumentBase64')" title="Remover arquivo">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="pdf-preview-info">
                                <i class="fa fa-file-pdf-o"></i>
                                <span>Documento PDF carregado</span>
                            </div>
                        </div>
                    </div>
                </nz-form-control>
            </nz-form-item>

            <!-- Proof of Residence -->
            <nz-form-item>
                <nz-form-label [nzSpan]="24" nzFor="proofOfResidence" nzRequired>Comprovante de Residência (PDF)</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Comprovante de residência é obrigatório">
                    <div class="upload-section">
                        <div class="upload-item">
                            <div class="upload-input-container">
                                <input type="text"
                                       class="upload-input"
                                       [value]="getFileName('proofOfResidenceBase64')"
                                       placeholder="Selecione o comprovante de residência (PDF)"
                                       readonly>
                                <i class="fa fa-check-circle upload-success-icon" *ngIf="form.get('proofOfResidenceBase64')?.value"></i>
                            </div>
                            <label class="upload-btn">
                                UPLOAD
                                <input type="file" (change)="onProofOfResidenceSelected($event)" style="display: none;" accept=".pdf">
                            </label>
                        </div>
                        <!-- Preview do PDF Comprovante de Residência -->
                        <div class="pdf-preview" *ngIf="form.get('proofOfResidenceBase64')?.value">
                            <div class="pdf-preview-header">
                                <span class="pdf-preview-title">Comprovante de Residência</span>
                                <div class="pdf-preview-actions">
                                    <button type="button" class="preview-btn" (click)="expandPdf(form.get('proofOfResidenceBase64')?.value, 'Comprovante de Residência')" title="Visualizar PDF">
                                        <i class="fa fa-eye"></i> Visualizar
                                    </button>
                                    <button type="button" class="remove-pdf-btn" (click)="removeFile('proofOfResidenceBase64')" title="Remover arquivo">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="pdf-preview-info">
                                <i class="fa fa-file-pdf-o"></i>
                                <span>Documento PDF carregado</span>
                            </div>
                        </div>
                    </div>
                </nz-form-control>
            </nz-form-item>

            <!-- Qualifications Document -->
            <nz-form-item>
                <nz-form-label [nzSpan]="24" nzFor="qualificationsDocument" nzRequired>Documento de Qualificações (PDF)</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Documento de qualificações é obrigatório">
                    <div class="upload-section">
                        <div class="upload-item">
                            <div class="upload-input-container">
                                <input type="text"
                                       class="upload-input"
                                       [value]="getFileName('qualificationsDocumentBase64')"
                                       placeholder="Selecione o documento de qualificações (PDF)"
                                       readonly>
                                <i class="fa fa-check-circle upload-success-icon" *ngIf="form.get('qualificationsDocumentBase64')?.value"></i>
                            </div>
                            <label class="upload-btn">
                                UPLOAD
                                <input type="file" (change)="onQualificationsDocumentSelected($event)" style="display: none;" accept=".pdf">
                            </label>
                        </div>
                        <!-- Preview do PDF Documento de Qualificações -->
                        <div class="pdf-preview" *ngIf="form.get('qualificationsDocumentBase64')?.value">
                            <div class="pdf-preview-header">
                                <span class="pdf-preview-title">Documento de Qualificações</span>
                                <div class="pdf-preview-actions">
                                    <button type="button" class="preview-btn" (click)="expandPdf(form.get('qualificationsDocumentBase64')?.value, 'Documento de Qualificações')" title="Visualizar PDF">
                                        <i class="fa fa-eye"></i> Visualizar
                                    </button>
                                    <button type="button" class="remove-pdf-btn" (click)="removeFile('qualificationsDocumentBase64')" title="Remover arquivo">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="pdf-preview-info">
                                <i class="fa fa-file-pdf-o"></i>
                                <span>Documento PDF carregado</span>
                            </div>
                        </div>
                    </div>
                </nz-form-control>
            </nz-form-item>

            <!-- Imagens -->
            <nz-divider nzText="Imagens" nzOrientation="left"></nz-divider>

            <!-- CNH Image -->
            <nz-form-item>
                <nz-form-label [nzSpan]="24" nzFor="cnhImage" nzRequired>Foto da CNH</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Foto da CNH é obrigatória">
                    <div class="upload-section">
                        <div class="upload-item">
                            <div class="upload-input-container">
                                <input type="text"
                                       class="upload-input"
                                       [value]="getFileName('cnhImageBase64')"
                                       placeholder="Selecione a foto da CNH (JPG/PNG)"
                                       readonly>
                                <i class="fa fa-check-circle upload-success-icon" *ngIf="form.get('cnhImageBase64')?.value"></i>
                            </div>
                            <label class="upload-btn">
                                UPLOAD
                                <input type="file" (change)="onCnhImageSelected($event)" style="display: none;" accept=".jpg,.jpeg,.png">
                            </label>
                        </div>
                        <!-- Preview da imagem CNH -->
                        <div class="image-preview" *ngIf="form.get('cnhImageBase64')?.value">
                            <img [src]="form.get('cnhImageBase64')?.value" alt="Preview CNH" class="preview-image" 
                                 (click)="expandImage(form.get('cnhImageBase64')?.value, 'CNH')" 
                                 title="Clique para expandir">
                            <button type="button" class="remove-image-btn" (click)="removeImage('cnhImageBase64')" title="Remover imagem">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </nz-form-control>
            </nz-form-item>

            <!-- Qualifications Image -->
            <nz-form-item>
                <nz-form-label [nzSpan]="24" nzFor="qualificationsImage" nzRequired>Foto das Qualificações</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Foto das qualificações é obrigatória">
                    <div class="upload-section">
                        <div class="upload-item">
                            <div class="upload-input-container">
                                <input type="text"
                                       class="upload-input"
                                       [value]="getFileName('qualificationsImageBase64')"
                                       placeholder="Selecione a foto das qualificações (JPG/PNG)"
                                       readonly>
                                <i class="fa fa-check-circle upload-success-icon" *ngIf="form.get('qualificationsImageBase64')?.value"></i>
                            </div>
                            <label class="upload-btn">
                                UPLOAD
                                <input type="file" (change)="onQualificationsImageSelected($event)" style="display: none;" accept=".jpg,.jpeg,.png">
                            </label>
                        </div>
                        <!-- Preview da imagem Qualificações -->
                        <div class="image-preview" *ngIf="form.get('qualificationsImageBase64')?.value">
                            <img [src]="form.get('qualificationsImageBase64')?.value" alt="Preview Qualificações" class="preview-image" 
                                 (click)="expandImage(form.get('qualificationsImageBase64')?.value, 'Qualificações')" 
                                 title="Clique para expandir">
                            <button type="button" class="remove-image-btn" (click)="removeImage('qualificationsImageBase64')" title="Remover imagem">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </nz-form-control>
            </nz-form-item>

            <!-- Status -->
            <nz-divider nzText="Status" nzOrientation="left"></nz-divider>

            <nz-form-item>
                <nz-form-label [nzSpan]="6" nzFor="isAprove">Aprovado</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <nz-switch formControlName="isAprove" id="isAprove"></nz-switch>
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label [nzSpan]="6" nzFor="origem">Origem Site</nz-form-label>
                <nz-form-control [nzSpan]="14">
                    <nz-switch formControlName="origem" id="origem"></nz-switch>
                </nz-form-control>
            </nz-form-item>
        </form>
    </ng-container>
    <div *nzModalFooter>
        <button nz-button nzType="default" (click)="handleCancel()">Cancelar</button>
        <button nz-button nzType="primary" (click)="save()">Salvar</button>
    </div>
</nz-modal>

<!-- Modal de Expansão de Imagem -->
<nz-modal
    [(nzVisible)]="isImageExpanded"
    [nzTitle]="expandedImageTitle"
    (nzOnCancel)="closeExpandedImage()"
    [nzFooter]="null"
    nzWidth="80%"
    nzCentered
    nzClosable>
    <ng-container *nzModalContent>
        <div class="expanded-image-container">
            <img [src]="expandedImageSrc" [alt]="expandedImageTitle" class="expanded-image">
        </div>
    </ng-container>
</nz-modal>

<!-- Modal de Expansão de PDF -->
<nz-modal
    [(nzVisible)]="isPdfExpanded"
    [nzTitle]="expandedPdfTitle"
    (nzOnCancel)="closeExpandedPdf()"
    [nzFooter]="null"
    nzWidth="90%"
    nzCentered
    nzClosable>
    <ng-container *nzModalContent>
        <div class="expanded-pdf-container">
            <iframe [src]="expandedPdfSrc" class="expanded-pdf" title="Visualização do PDF">
                <p>Seu navegador não suporta visualização de PDFs. 
                   <a [href]="expandedPdfSrc" download>Clique aqui para baixar o arquivo</a>
                </p>
            </iframe>
        </div>
    </ng-container>
</nz-modal>