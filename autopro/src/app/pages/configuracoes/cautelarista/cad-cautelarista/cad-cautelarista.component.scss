/* Estilos específicos para o componente de cadastro de cautelaristas */

// Modal mais larga para acomodar previews
::ng-deep .ant-modal {
  .ant-modal-content {
    max-height: 90vh;
    overflow-y: auto;
  }
  
  .ant-modal-body {
    max-height: calc(90vh - 120px);
    overflow-y: auto;
    padding: 24px;
  }
}

.upload-section {
  .upload-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;

    .upload-input-container {
      flex: 1;
      position: relative;

      .upload-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #f5f5f5;
        cursor: default;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }

      .upload-success-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #52c41a;
        font-size: 16px;
      }
    }

    .upload-btn {
      padding: 8px 16px;
      background-color: #1890ff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.3s;
      display: inline-block;

      &:hover {
        background-color: #40a9ff;
      }

      &:active {
        background-color: #096dd9;
      }

      input[type="file"] {
        display: none;
      }
    }
  }
}

// Seções organizadas
.form-section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }
}

// Melhor espaçamento entre os campos
nz-form-item {
  margin-bottom: 16px;
}

// Estilo para dividers
nz-divider {
  margin: 24px 0 16px 0;
  
  .ant-divider-inner-text {
    font-weight: 600;
    color: #262626;
  }
}

// Indicadores de arquivo carregado
.file-loaded {
  .upload-input {
    border-color: #52c41a;
    background-color: #f6ffed;
  }
}

// Preview de imagens
.image-preview {
  margin-top: 12px;
  position: relative;
  display: inline-block;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  }

  .preview-image {
    max-width: 200px;
    max-height: 150px;
    width: auto;
    height: auto;
    display: block;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.3s ease, opacity 0.3s ease;

    &:hover {
      transform: scale(1.05);
      opacity: 0.9;
    }
  }

  .remove-image-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    background-color: rgba(255, 77, 79, 0.9);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
    opacity: 0;

    &:hover {
      background-color: #ff4d4f;
      transform: scale(1.1);
    }
  }

  &:hover .remove-image-btn {
    opacity: 1;
  }
}

// Preview de PDFs
.pdf-preview {
  margin-top: 12px;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  }

  .pdf-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 6px 6px 0 0;

    .pdf-preview-title {
      font-weight: 600;
      color: #262626;
      font-size: 14px;
    }

    .pdf-preview-actions {
      display: flex;
      gap: 8px;

      .preview-btn {
        padding: 4px 12px;
        background-color: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 500;
        transition: background-color 0.3s;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          background-color: #40a9ff;
        }

        &:active {
          background-color: #096dd9;
        }

        i {
          font-size: 12px;
        }
      }

      .remove-pdf-btn {
        width: 24px;
        height: 24px;
        border: none;
        border-radius: 50%;
        background-color: rgba(255, 77, 79, 0.9);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        transition: all 0.3s ease;

        &:hover {
          background-color: #ff4d4f;
          transform: scale(1.1);
        }
      }
    }
  }

  .pdf-preview-info {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;

    i {
      font-size: 20px;
      color: #ff4d4f;
    }

    span {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// Melhorar espaçamento para labels de span completo
nz-form-label[nzSpan="24"] {
  margin-bottom: 8px;
  font-weight: 600;
  
  &.ant-form-item-required::before {
    color: #ff4d4f;
  }
}

// Ajustar espaçamento dos controles de span completo
nz-form-control[nzSpan="24"] {
  padding-left: 0 !important;
}

// Melhorar responsividade
// Modal de expansão de imagem
.expanded-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;

  .expanded-image {
    max-width: 100%;
    max-height: 70vh;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

// Modal de expansão de PDF
.expanded-pdf-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;

  .expanded-pdf {
    width: 100%;
    height: 75vh;
    min-height: 600px;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    background-color: white;

    p {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 16px;

      a {
        color: #1890ff;
        text-decoration: none;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .image-preview {
    .preview-image {
      max-width: 150px;
      max-height: 100px;
    }
  }

  .pdf-preview {
    .pdf-preview-header {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;

      .pdf-preview-actions {
        align-self: flex-end;
      }
    }
  }
  
  .upload-item {
    flex-direction: column;
    gap: 12px;
    
    .upload-btn {
      align-self: flex-start;
    }
  }

  .expanded-image-container {
    min-height: 300px;
    padding: 12px;

    .expanded-image {
      max-height: 60vh;
    }
  }

  .expanded-pdf-container {
    min-height: 400px;
    padding: 12px;

    .expanded-pdf {
      height: 60vh;
      min-height: 400px;
    }
  }
}