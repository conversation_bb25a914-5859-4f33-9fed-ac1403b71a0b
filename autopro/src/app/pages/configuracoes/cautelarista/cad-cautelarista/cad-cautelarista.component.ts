import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { GlobalService } from '../../../../../services/global.service';
import { CautelaristaModel } from '../model/cautelarista.model';

@Component({
  selector: 'app-cad-cautelarista',
  imports: [
    CommonModule,
    NzModalModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzSwitchModule,
    NzSelectModule,
    NzUploadModule,
    NzDividerModule,
    ReactiveFormsModule
  ],
  templateUrl: './cad-cautelarista.component.html',
  styleUrl: './cad-cautelarista.component.scss',
  host: { ngSkipHydration: 'true' }
})
export class CadCautelaristaComponent {
  @Input() isVisible: boolean = false;
  @Output() modalClose = new EventEmitter();
  @Input() model: CautelaristaModel | null = null;
  
  titlePage: string = "Cautelarista";
  form!: FormGroup;
  
  // Controle de arquivos
  cnhDocumentFile: File | null = null;
  proofOfResidenceFile: File | null = null;
  qualificationsDocumentFile: File | null = null;
  cnhImageFile: File | null = null;
  qualificationsImageFile: File | null = null;

  // Controle da modal de expansão de imagem
  isImageExpanded: boolean = false;
  expandedImageSrc: string = '';
  expandedImageTitle: string = '';

  // Controle da modal de expansão de PDF
  isPdfExpanded: boolean = false;
  expandedPdfSrc: SafeResourceUrl = '';
  expandedPdfTitle: string = '';

  tiposContato = [
    { label: 'WhatsApp', value: 'WhatsApp' },
    { label: 'Telefone', value: 'Telefone' },
    { label: 'Email', value: 'Email' }
  ];

  constructor(
    private fb: FormBuilder,
    private globalService: GlobalService,
    private notificationService: NzNotificationService,
    private sanitizer: DomSanitizer
  ) {
  }
    
  initializeForm() {
    this.form = this.fb.group({
      id: [0],
      name: [null, [Validators.required, Validators.maxLength(200)]],
      cellphone: [null, [Validators.required, Validators.maxLength(20)]],
      typeOfContact: [null, [Validators.required]],
      email: [null, [Validators.email, Validators.maxLength(255)]],
      observacoes: [null, [Validators.maxLength(1000)]],
      isAprove: [false],
      origem: [false],
      cnhDocumentBase64: [null, [Validators.required]],
      proofOfResidenceBase64: [null, [Validators.required]],
      qualificationsDocumentBase64: [null, [Validators.required]],
      cnhImageBase64: [null, [Validators.required]],
      qualificationsImageBase64: [null, [Validators.required]]
    });
  }

  ngOnInit() {
  }

  ngOnChanges() {
    this.initializeForm();
    
    if (this.model) {
      this.form.patchValue(this.model);
      this.titlePage = "Editar Cautelarista";
    } else {
      this.titlePage = "Novo Cautelarista";
    }
  }

  save() {
    if (this.form.valid && this.form.controls['id'].value == 0) {
      this.cautelaristaSave();
    }
    else if (this.form.valid && this.form.controls['id'].value > 0) {
      this.cautelaristaUpdate();
    }
    else {
      Object.values(this.form.controls).forEach(control => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  handleCancel() {
    this.isVisible = false;
    this.modalClose.emit(false);
    this.resetFiles();
  }

  // Funções para upload de arquivos
  onCnhDocumentSelected(event: any): void {
    this.handleFileUpload(event, 'cnhDocumentBase64', ['application/pdf'], 5);
  }

  onProofOfResidenceSelected(event: any): void {
    this.handleFileUpload(event, 'proofOfResidenceBase64', ['application/pdf'], 5);
  }

  onQualificationsDocumentSelected(event: any): void {
    this.handleFileUpload(event, 'qualificationsDocumentBase64', ['application/pdf'], 5);
  }

  onCnhImageSelected(event: any): void {
    this.handleFileUpload(event, 'cnhImageBase64', ['image/jpeg', 'image/png', 'image/jpg'], 2);
  }

  onQualificationsImageSelected(event: any): void {
    this.handleFileUpload(event, 'qualificationsImageBase64', ['image/jpeg', 'image/png', 'image/jpg'], 2);
  }

  private handleFileUpload(event: any, fieldName: string, allowedTypes: string[], maxSizeMB: number): void {
    const file = event.target.files[0];
    if (file) {
      // Validar tipo de arquivo
      if (!allowedTypes.includes(file.type)) {
        this.notificationService.error(
          'Erro',
          `Tipo de arquivo não permitido. Use apenas: ${allowedTypes.join(', ')}`,
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
        return;
      }

      // Validar tamanho do arquivo
      const maxSize = maxSizeMB * 1024 * 1024;
      if (file.size > maxSize) {
        this.notificationService.error(
          'Erro',
          `Arquivo muito grande. O tamanho máximo é ${maxSizeMB}MB.`,
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
        return;
      }

      // Converter para Base64
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const base64 = e.target.result;
        this.form.patchValue({
          [fieldName]: base64
        });
        
        this.notificationService.success(
          'Sucesso',
          'Arquivo carregado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
      };
      reader.readAsDataURL(file);
    }
  }

  getFileName(fieldName: string): string {
    const value = this.form.get(fieldName)?.value;
    if (value) {
      // Para imagens, mostrar preview, para PDFs mostrar texto
      if (fieldName.includes('Image')) {
        return 'Imagem carregada - veja o preview abaixo';
      }
      return 'Arquivo carregado';
    }
    return 'Nenhum arquivo selecionado';
  }

  removeImage(fieldName: string): void {
    this.removeFile(fieldName);
  }

  expandImage(imageSrc: string, imageTitle: string): void {
    this.expandedImageSrc = imageSrc;
    this.expandedImageTitle = `Visualização - ${imageTitle}`;
    this.isImageExpanded = true;
  }

  closeExpandedImage(): void {
    this.isImageExpanded = false;
    this.expandedImageSrc = '';
    this.expandedImageTitle = '';
  }

  expandPdf(pdfSrc: string, pdfTitle: string): void {
    this.expandedPdfSrc = this.sanitizer.bypassSecurityTrustResourceUrl(pdfSrc);
    this.expandedPdfTitle = `Visualização - ${pdfTitle}`;
    this.isPdfExpanded = true;
  }

  closeExpandedPdf(): void {
    this.isPdfExpanded = false;
    this.expandedPdfSrc = '';
    this.expandedPdfTitle = '';
  }

  isImage(fieldName: string): boolean {
    return fieldName.includes('Image');
  }

  isPdf(fieldName: string): boolean {
    return fieldName.includes('Document');
  }

  canPreview(fieldName: string): boolean {
    const value = this.form.get(fieldName)?.value;
    return !!value;
  }

  removeFile(fieldName: string): void {
    this.form.patchValue({
      [fieldName]: null
    });
    
    const isImageFile = this.isImage(fieldName);
    const fileName = isImageFile ? 'imagem' : 'arquivo';
    
    this.notificationService.info(
      `${isImageFile ? 'Imagem' : 'Arquivo'} removido`,
      `A ${fileName} foi removida com sucesso.`,
      {
        nzDuration: 2000,
        nzClass: 'ant-notification-notice.info'
      }
    );
  }

  resetFiles(): void {
    this.cnhDocumentFile = null;
    this.proofOfResidenceFile = null;
    this.qualificationsDocumentFile = null;
    this.cnhImageFile = null;
    this.qualificationsImageFile = null;
    
    // Limpar campos do formulário de arquivos ao cancelar
    this.form.patchValue({
      cnhDocumentBase64: null,
      proofOfResidenceBase64: null,
      qualificationsDocumentBase64: null,
      cnhImageBase64: null,
      qualificationsImageBase64: null
    });
  }

  cautelaristaSave() {
    this.globalService.post('cautelarist/add', this.form.value).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Cautelarista cadastrado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.handleCancel();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao cadastrar cautelarista!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  cautelaristaUpdate() {
    const id = this.form.get('id')?.value;
    this.globalService.put(`cautelarist/${id}`, this.form.value).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Cautelarista atualizado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.handleCancel();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao atualizar cautelarista!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }
}