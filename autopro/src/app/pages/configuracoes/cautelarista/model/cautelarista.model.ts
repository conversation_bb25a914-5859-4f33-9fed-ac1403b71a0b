export interface CautelaristaModel {
    id: number;
    name: string;
    cellphone: string;
    typeOfContact: string;
    cnhDocumentBase64: string;
    proofOfResidenceBase64: string;
    qualificationsDocumentBase64: string;
    cnhImageBase64: string;
    qualificationsImageBase64: string;
    isAprove: boolean;
    email?: string;
    observacoes?: string;
    origem: boolean;
}

export interface ApprovalStatusModel {
    isAprove: boolean;
}

// Modelo otimizado para listagem (sem campos Base64 pesados)
export interface CautelaristaListModel {
    id: number;
    name: string;
    cellphone: string;
    email?: string;
    typeOfContact: string;
    isAprove: boolean;
    origem: boolean;
    observacoes?: string;
}

// Interface para requisição paginada
export interface PagedRequest {
    pageNumber: number;
    pageSize: number;
    searchTerm?: string;
    sortBy?: string;
    sortDescending: boolean;
}

// Interface para resposta paginada
export interface PagedResult<T> {
    items: T[];
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
}