import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { GlobalService } from '../../../../services/global.service';
import { TituloComponent } from '../../../components/titulo/titulo.component';
import { CautelaristaListModel } from '../cautelarista/model/cautelarista.model';

@Component({
  selector: 'app-cauteralist-fee',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzInputNumberModule,
    NzSelectModule,
    NzSpinModule,
    NzCardModule,
    TituloComponent
  ],
  templateUrl: './cauteralist-fee.component.html',
  styleUrl: './cauteralist-fee.component.scss',
  host: { ngSkipHydration: 'true' }
})
export class CauteralistFeeComponent implements OnInit {
  titlePage: string = "Comissão dos Cautelaristas";
  firstLevel: string = "Configurações";
  secondLevel: string = "Cautelaristas";
  subtitle: string = "Configure a comissão dos cautelaristas";

  form!: FormGroup;
  isLoading: boolean = false;
  isLoadingCurrentFee: boolean = false;
  cautelaristas: CautelaristaListModel[] = [];
  currentFee: number | null = null;

  constructor(
    private fb: FormBuilder,
    private globalService: GlobalService,
    private notificationService: NzNotificationService
  ) {
    this.initializeForm();
    this.loadCautelaristas();
  }

  ngOnInit() {
    this.loadCurrentFee();
  }
  initializeForm() {
    this.form = this.fb.group({
      Fee: [null, [Validators.required, Validators.min(0), Validators.max(100)]]
    });
  }

  loadCautelaristas() {
    this.globalService.get('cautelarist').subscribe({
      next: (response: any) => {
        this.cautelaristas = response.filter((c: CautelaristaListModel) => c.isAprove);
      },
      error: () => {
        this.notificationService.error(
          'Erro',
          'Erro ao carregar cautelaristas!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  loadCurrentFee() {
    this.isLoadingCurrentFee = true;
    this.globalService.get('cautelaristFee').subscribe({
      next: (response: any) => {
        if (response && response.length > 0) {
          // Pega a comissão mais recente (assumindo que vem ordenada ou pega a primeira)
          this.currentFee = response[0].fee || response.fee;
        } else {
          this.currentFee = null;
        }
        this.isLoadingCurrentFee = false;
      },
      error: (error: any) => {
        this.isLoadingCurrentFee = false;
        // Se for erro 404 ou similar, significa que não há comissão configurada ainda
        if (error.status === 404) {
          this.currentFee = null;
        } else {
          this.notificationService.error(
            'Erro',
            'Erro ao carregar comissão atual!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice.error'
            }
          );
        }
      }
    });
  }

  save() {
    if (this.form.valid) {
      this.isLoading = true;
      this.globalService.post('cautelaristFee/add', this.form.value).subscribe({
        next: (response: any) => {
        },
        error: (error: any) => {
          this.isLoading = false;
          this.notificationService.error(
            'Erro ao criar a comissão do cautelarista',
            `Erro: ${error.message}`,
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice.error'
            }
          );
        },
        complete: () => {
          this.isLoading = false;
          this.notificationService.success(
            'Sucesso',
            'Comissão do cautelarista criada com sucesso!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice.success'
            }
          );
          this.form.reset();
          // Recarrega a comissão atual após salvar
          this.loadCurrentFee();
        }
      });
    } else {
      // Marcar todos os campos como touched para mostrar erros de validação
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  cancel() {
    this.form.reset();
  }
}
