<app-titulo 
  [title]="titlePage" 
  [firstLevel]="firstLevel" 
  [secondLevel]="secondLevel"
  [subtitle]="subtitle">
</app-titulo>

<div class="page-content">
  <!-- Seção de Comissão Atual -->
  <div class="current-fee-section">
    <nz-card nzTitle="Comissão Atual Configurada" [nzLoading]="isLoadingCurrentFee">
      <div class="current-fee-content">
        <div *ngIf="currentFee !== null; else noFeeTemplate" class="fee-display">
          <span class="fee-label">Comissão Ativa:</span>
          <span class="fee-value">{{ currentFee }}%</span>
        </div>
        <ng-template #noFeeTemplate>
          <div class="no-fee-display">
            <span class="no-fee-message">Nenhuma comissão configurada ainda</span>
          </div>
        </ng-template>
      </div>
    </nz-card>
  </div>

  <div class="fee-section">
    <h3>Informe a Comissão (%)</h3>
    
    <form nz-form [formGroup]="form" class="fee-form">
      <nz-form-item>
        <nz-form-control nzErrorTip="Comissão é obrigatória e deve estar entre 0 e 100">
          <nz-input-number
            formControlName="Fee"
            [nzMin]="0"
            [nzMax]="100"
            [nzStep]="0.01"
            nzPlaceHolder="Digite a comissão (%)"
            class="commission-input">
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </form>
  </div>

  <div class="action-buttons">
    <button 
      nz-button 
      nzType="default" 
      (click)="cancel()"
      [disabled]="isLoading">
      CANCELAR
    </button>
    <button 
      nz-button 
      nzType="primary" 
      (click)="save()"
      [nzLoading]="isLoading">
      SALVAR
    </button>
  </div>
</div>
