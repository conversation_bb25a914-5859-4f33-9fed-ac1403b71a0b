
.page-content {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 60vh;
  display: flex;
  flex-direction: column;

  .current-fee-section {
    margin-bottom: 32px;

    ::ng-deep .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid #f0f0f0;

      .ant-card-head {
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;

        .ant-card-head-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .current-fee-content {
      .fee-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16px;
        padding: 16px 0;

        .fee-label {
          font-size: 16px;
          font-weight: 500;
          color: #595959;
        }

        .fee-value {
          font-size: 24px;
          font-weight: 700;
          color: #1890ff;
          background: #e6f7ff;
          padding: 8px 16px;
          border-radius: 6px;
          border: 1px solid #91d5ff;
        }
      }

      .no-fee-display {
        display: flex;
        justify-content: center;
        padding: 16px 0;

        .no-fee-message {
          font-size: 16px;
          color: #8c8c8c;
          font-style: italic;
          background: #f5f5f5;
          padding: 12px 20px;
          border-radius: 6px;
          border: 1px dashed #d9d9d9;
        }
      }
    }
  }

  .fee-section {
    flex: 1;
    margin-bottom: 32px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 32px;
      color: #262626;
      text-align: center;
    }

    .fee-form {
      max-width: 600px;
      margin: 0 auto;

      nz-form-item {
        margin-bottom: 24px;

        nz-form-label {
          font-weight: 600;
          font-size: 14px;
          color: #262626;

          &::before {
            color: #ff4d4f;
          }
        }

        nz-form-control {
          .ant-form-item-explain {
            margin-top: 8px;
            font-size: 12px;
          }
        }
      }

      // Estilo específico para o select de cautelaristas
      nz-select {
        width: 100%;
        height: 40px;

        ::ng-deep .ant-select-selector {
          height: 40px;
          padding: 8px 12px;
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          transition: all 0.3s;

          .ant-select-selection-search-input {
            height: 24px;
            line-height: 24px;
          }

          .ant-select-selection-placeholder {
            line-height: 24px;
            color: #bfbfbf;
          }

          .ant-select-selection-item {
            line-height: 24px;
            font-weight: 500;
          }
        }

        &:hover ::ng-deep .ant-select-selector {
          border-color: #40a9ff;
        }

        &.ant-select-focused ::ng-deep .ant-select-selector {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }

      // Estilo específico para o input de comissão
      .commission-input {
        width: 100%;
        height: 40px;

        ::ng-deep .ant-input-number-input {
          font-size: 16px;
          font-weight: 500;
          text-align: center;
          height: 38px;
          line-height: 38px;
          border-radius: 6px;
        }

        ::ng-deep .ant-input-number-handler-wrap {
          opacity: 1;
          border-radius: 0 6px 6px 0;
        }

        ::ng-deep .ant-input-number-handler {
          height: 19px;

          &:hover {
            background-color: #f5f5f5;
          }
        }

        ::ng-deep .ant-input-number:hover {
          border-color: #40a9ff;
        }

        ::ng-deep .ant-input-number:focus-within {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: auto;
    padding-top: 32px;
    border-top: 1px solid #f0f0f0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    button {
      min-width: 140px;
      height: 44px;
      font-weight: 600;
      font-size: 14px;
      border-radius: 6px;
      transition: all 0.3s;

      &[nzType="default"] {
        border: 1px solid #d9d9d9;
        color: #595959;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }

      &[nzType="primary"] {
        background: #1890ff;
        border-color: #1890ff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .page-content {
    padding: 16px;

    .current-fee-section {
      margin-bottom: 24px;

      ::ng-deep .ant-card {
        .ant-card-head {
          .ant-card-head-title {
            font-size: 14px;
          }
        }

        .ant-card-body {
          padding: 16px;
        }
      }

      .current-fee-content {
        .fee-display {
          flex-direction: column;
          gap: 12px;
          padding: 12px 0;

          .fee-label {
            font-size: 14px;
            text-align: center;
          }

          .fee-value {
            font-size: 20px;
            padding: 6px 12px;
          }
        }

        .no-fee-display {
          padding: 12px 0;

          .no-fee-message {
            font-size: 14px;
            padding: 10px 16px;
            text-align: center;
          }
        }
      }
    }

    .fee-section {
      h3 {
        font-size: 16px;
        margin-bottom: 24px;
      }

      .fee-form {
        max-width: 100%;
        padding: 0 8px;

        nz-form-item {
          margin-bottom: 20px;
        }

        nz-select {
          height: 44px;

          ::ng-deep .ant-select-selector {
            height: 44px;
            padding: 10px 12px;

            .ant-select-selection-search-input,
            .ant-select-selection-placeholder,
            .ant-select-selection-item {
              line-height: 24px;
            }
          }
        }

        .commission-input {
          height: 44px;

          ::ng-deep .ant-input-number-input {
            font-size: 18px;
            height: 42px;
            line-height: 42px;
          }

          ::ng-deep .ant-input-number-handler {
            height: 21px;
          }
        }
      }
    }

    .action-buttons {
      flex-direction: column;
      padding-top: 24px;
      max-width: 100%;

      button {
        width: 100%;
        height: 48px;
        font-size: 16px;
      }
    }
  }
}

// Responsividade para tablets
@media (max-width: 1024px) and (min-width: 769px) {
  .page-content {
    .current-fee-section {
      .current-fee-content {
        .fee-display {
          .fee-value {
            font-size: 22px;
          }
        }
      }
    }

    .fee-section {
      .fee-form {
        max-width: 500px;
      }
    }

    .action-buttons {
      max-width: 500px;
    }
  }
}

