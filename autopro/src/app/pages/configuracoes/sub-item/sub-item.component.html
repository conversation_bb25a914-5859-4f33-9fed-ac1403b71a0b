<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="model ? 'Editar Sub-Item' : 'Novo Sub-Item'"
  [nzFooter]="null"
  [nzWidth]="600"
  (nzOnCancel)="cancel()">

  <ng-container *nzModalContent>
    <form nz-form [formGroup]="form" nzLayout="vertical">
      <nz-form-item>
        <nz-form-label nzFor="itemModelId" nzRequired>Item</nz-form-label>
        <nz-form-control nzErrorTip="Selecione um item">
          <nz-select
            formControlName="itemModelId"
            id="itemModelId"
            nzPlaceHolder="Selecione um item">
            <nz-option
              *ngFor="let item of items"
              [nzValue]="item.id"
              [nzLabel]="item.name">
            </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFor="name" nzRequired>Nome</nz-form-label>
        <nz-form-control nzErrorTip="Nome é obrigatório">
          <input nz-input formControlName="name" id="name" placeholder="Digite o nome do sub-item" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFor="description" nzRequired>Descrição</nz-form-label>
        <nz-form-control nzErrorTip="Descrição é obrigatória">
          <textarea nz-input formControlName="description" id="description" rows="3" placeholder="Digite a descrição do sub-item"></textarea>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFor="isActive">Ativo</nz-form-label>
        <nz-form-control>
          <nz-switch
            formControlName="isActive"
            id="isActive"
            nzCheckedChildren="Sim"
            nzUnCheckedChildren="Não">
          </nz-switch>
        </nz-form-control>
      </nz-form-item>

      <div class="action-buttons">
        <button
          nz-button
          nzType="default"
          (click)="cancel()"
          [disabled]="isLoading">
          Cancelar
        </button>
        <button
          nz-button
          nzType="primary"
          (click)="save()"
          [nzLoading]="isLoading">
          {{ model ? 'Atualizar' : 'Salvar' }}
        </button>
      </div>
    </form>
  </ng-container>
</nz-modal>
