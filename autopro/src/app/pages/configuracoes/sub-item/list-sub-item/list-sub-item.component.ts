import { Component, ChangeDetectionStrategy, ChangeDetectorRef, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { TituloComponent } from '../../../../components/titulo/titulo.component';
import { SubItemComponent } from '../sub-item.component';
import { GlobalService } from '../../../../../services/global.service';
import { ToastService } from '../../../../shared/toast/toast.service';
import { SubItemModel } from '../model/sub-itemModel';
import { ItemModel } from '../../itemcomponent/model/ItemModel';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

// Interfaces para paginação
interface PagedRequest {
  page: number;
  pageSize: number;
  searchTerm?: string;
  sortBy?: string;
  sortDescending?: boolean;
}

interface PagedResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
}

// Interface estendida para exibição
interface SubItemListModel extends SubItemModel {
  itemName?: string;
}

@Component({
  selector: 'app-list-sub-item',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzButtonModule,
    NzInputModule,
    NzIconModule,
    NzTableModule,
    NzTagModule,
    NzPaginationModule,
    NzPopconfirmModule,
    NzToolTipModule,
    TituloComponent,
    SubItemComponent
  ],
  templateUrl: './list-sub-item.component.html',
  styleUrl: './list-sub-item.component.scss',
  host: { ngSkipHydration: 'true' },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListSubItemComponent implements OnInit, OnDestroy {
  
  tituloPagina: string = "Lista de Sub-Itens";
  firstLevel: string = "Configurações";
  secondLevel: string = "Sub-Itens";
  subtitle: string = "Listagem de Sub-Itens";
  
  // Loading states
  isLoading: boolean = false;
  isLoadingNew: boolean = false;
  isLoadingTable: boolean = false;
  
  // Data properties
  subItemList: SubItemListModel[] = [];
  subItemModel: SubItemModel | null = null;
  items: ItemModel[] = [];
  
  // Modal properties
  showModalNovo: boolean = false;

  // Paginação
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  pageSizeOptions = [10, 20, 50, 100];

  // Busca e filtro
  searchTerm = '';
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Ordenação
  sortBy: string = 'name';
  sortDescending: boolean = false;

  // Cache simples
  private cache = new Map<string, { data: PagedResult<SubItemListModel>, timestamp: number }>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutos

  constructor(
    private globalService: GlobalService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef
  ) {
    // Configurar debounce para busca
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.currentPage = 1; // Reset para primeira página na busca
      this.clearCache(); // Limpar cache na busca
      this.loadSubItemsPaged();
    });
  }

  ngOnInit() {
    this.loadItems();
    this.loadSubItemsPaged();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadItems() {
    this.globalService.get('item').subscribe({
      next: (response: any) => {
        this.items = response;
        this.cdr.markForCheck();
      },
      error: () => {
        this.toastService.error(
          'Erro ao carregar itens!',
          'Erro',
          { duration: 3000 }
        );
      }
    });
  }

  onModalClosed() {
    this.showModalNovo = false;
    this.subItemModel = null;
    this.clearCache(); // Limpar cache após operação
    this.loadSubItemsPaged();
  }

  onEdit(item: SubItemListModel) {
    this.globalService.getbyid('subitem', item.id).subscribe({
      next: (response: any) => {
        this.subItemModel = response as SubItemModel;
        this.showModalNovo = true;
        this.cdr.markForCheck();
      },
      error: () => {
        this.toastService.error(
          'Erro ao carregar dados do sub-item!',
          'Erro',
          { duration: 3000 }
        );
      }
    });
  }

  onNew() {
    this.showModalNovo = true;
    this.subItemModel = null;
  }

  onDelete(id: number): void {
    this.globalService.delete(`subitem/delete/${id}`).subscribe({
      next: () => {
        this.toastService.success(
          'Sub-item excluído com sucesso!',
          'Operação Concluída'
        );
        this.clearCache(); // Limpar cache após operação
        this.loadSubItemsPaged();
      },
      error: () => {
        this.toastService.error(
          'Erro ao excluir sub-item. Tente novamente.',
          'Erro na Operação',
          { duration: 5000 }
        );
      }
    });
  }

  cancel(): void {
    this.toastService.info('Operação cancelada', 'Cancelado');
  }

  confirm(id: number): void {
    this.onDelete(id);
  }

  // Métodos de ordenação
  onSort(field: string): void {
    if (this.sortBy === field) {
      this.sortDescending = !this.sortDescending;
    } else {
      this.sortBy = field;
      this.sortDescending = false;
    }
    this.clearCache(); // Limpar cache ao ordenar
    this.loadSubItemsPaged();
  }

  getSortDirection(field: string): 'asc' | 'desc' | null {
    if (this.sortBy !== field) return null;
    return this.sortDescending ? 'desc' : 'asc';
  }

  // Cache methods
  private clearCache(): void {
    this.cache.clear();
  }

  private getCacheKey(): string {
    return `${this.currentPage}-${this.pageSize}-${this.searchTerm}-${this.sortBy}-${this.sortDescending}`;
  }

  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheExpiry;
  }

  loadSubItemsPaged(): void {
    const cacheKey = this.getCacheKey();
    const cached = this.cache.get(cacheKey);
    
    if (cached && this.isCacheValid(cached.timestamp)) {
      this.subItemList = cached.data.items;
      this.totalItems = cached.data.totalCount;
      this.isLoadingNew = false;
      this.isLoadingTable = false;
      this.cdr.markForCheck();
      return;
    }

    this.isLoadingNew = true;
    this.isLoadingTable = true;
    this.cdr.markForCheck();

    const params = new URLSearchParams({
      page: this.currentPage.toString(),
      pageSize: this.pageSize.toString(),
      sortBy: this.sortBy,
      sortDescending: this.sortDescending.toString()
    });

    if (this.searchTerm) {
      params.append('searchTerm', this.searchTerm);
    }

    this.globalService.get(`subitem/paged?${params.toString()}`).subscribe({
      next: (response: any) => {
        const pagedResult = response as PagedResult<SubItemModel>;
        
        // Enriquecer dados com nome do item
        this.subItemList = pagedResult.items.map(subItem => ({
          ...subItem,
          itemName: this.items.find(item => item.id === subItem.itemModelId)?.name || 'N/A'
        }));
        
        this.totalItems = pagedResult.totalCount;
        
        // Armazenar no cache
        this.cache.set(cacheKey, {
          data: { ...pagedResult, items: this.subItemList },
          timestamp: Date.now()
        });
        
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.cdr.markForCheck();
      },
      error: () => {
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.toastService.error(
          'Erro ao carregar sub-itens. Tente novamente.',
          'Erro',
          { duration: 5000 }
        );
        this.cdr.markForCheck();
      }
    });
  }

  // Método de busca com debounce
  onSearch(): void {
    this.searchSubject.next(this.searchTerm);
  }

  // Limpar busca
  clearSearch(): void {
    this.searchTerm = '';
    this.onSearch();
  }

  // Métodos de paginação
  onPageIndexChange(pageIndex: number): void {
    this.currentPage = pageIndex;
    this.loadSubItemsPaged();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1; // Reset para primeira página
    this.clearCache(); // Limpar cache ao mudar tamanho da página
    this.loadSubItemsPaged();
  }

  // TrackBy function para melhor performance
  trackByFn(index: number, item: SubItemListModel): number {
    return item.id;
  }
}
