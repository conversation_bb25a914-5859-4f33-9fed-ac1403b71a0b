.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.btnNovo {
  display: flex;
  justify-content: flex-end;
}

.page-content {
  background: white;
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-section {
  margin-bottom: 16px;
}

.subitem-description {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Estilo para botões de ação
nz-table {
  td {
    button, a {
      margin-right: 8px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

// Estilo para o botão de exclusão
a[nz-popconfirm] {
  cursor: pointer;
  padding: 4px 15px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.85);
  
  &:hover {
    color: #ff4d4f;
  }
}

// Responsividade
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .btnNovo {
    justify-content: center;
  }
  
  .search-section {
    nz-input-group {
      width: 100% !important;
    }
  }
  
  .subitem-description {
    max-width: 200px;
  }
}

.page-footer {
  margin-top: 16px;
}
