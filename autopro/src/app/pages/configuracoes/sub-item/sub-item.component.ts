import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { GlobalService } from '../../../../services/global.service';
import { ItemModel } from '../itemcomponent/model/ItemModel';
import { SubItemModel } from './model/sub-itemModel';

@Component({
  selector: 'app-sub-item',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzModalModule,
    NzSwitchModule
  ],
  providers: [NzNotificationService],
  templateUrl: './sub-item.component.html',
  styleUrl: './sub-item.component.scss'
})
export class SubItemComponent implements OnChanges {
  @Input() isVisible: boolean = false;
  @Input() model: SubItemModel | null = null;
  @Output() modalClose = new EventEmitter<void>();

  titlePage: string = "Sub-Item";
  firstLevel: string = "Configurações";
  secondLevel: string = "Sub-Item";
  subtitle: string = "Cadastro de Sub-Item";

  form!: FormGroup;
  isLoading: boolean = false;
  items: ItemModel[] = [];

  constructor(
    private fb: FormBuilder,
    private notificationService: NzNotificationService,
    private globalService: GlobalService
  ) {
    this.initializeForm();
    this.loadItems();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['model'] && this.model) {
      this.loadModelData();
    } else if (changes['isVisible'] && this.isVisible && !this.model) {
      this.form.reset();
      this.initializeForm();
    }
  }

  loadModelData() {
    if (this.model) {
      this.form.patchValue({
        id: this.model.id,
        name: this.model.name,
        description: this.model.description,
        itemModelId: this.model.itemModelId,
        isActive: this.model.isActive ?? true
      });
    }
  }

  initializeForm() {
    this.form = this.fb.group({
      id: [0],
      name: [null, [Validators.required]],
      description: [null, [Validators.required]],
      itemModelId: [null, [Validators.required]],
      isActive: [true]
    });
  }

  loadItems() {
    this.globalService.get('item').subscribe({
      next: (response: any) => {
        this.items = response;
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao carregar itens!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice-error'
          }
        );
      }
    });
  }

  save() {
    if (this.form.valid) {
      this.isLoading = true;

      const endpoint = this.model ? `subitem/update/${this.model.id}` : 'subitem/add';
      const method = this.model ? 'put' : 'post';

      this.globalService[method](endpoint, this.form.value).subscribe({
        next: () => {
          this.notificationService.success(
            'Sucesso',
            this.model ? 'Sub-item atualizado com sucesso!' : 'Sub-item salvo com sucesso!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice-success'
            }
          );
          this.closeModal();
        },
        error: () => {
          this.notificationService.error(
            'Erro',
            this.model ? 'Erro ao atualizar sub-item!' : 'Erro ao salvar sub-item!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice-error'
            }
          );
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    } else {
      Object.values(this.form.controls).forEach(control => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  cancel() {
    this.closeModal();
  }

  closeModal() {
    this.form.reset();
    this.initializeForm();
    this.modalClose.emit();
  }
}
