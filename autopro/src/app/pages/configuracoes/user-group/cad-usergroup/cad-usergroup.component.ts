import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { UserGroupModel } from '../model/usergroup.model';
import { GlobalService } from '../../../../../services/global.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { ConfigService } from '../../../../../services/config.service';


@Component({
  selector: 'app-cad-usergroup',
  imports: [
    NzModalModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzSwitchModule,
    ReactiveFormsModule
  ],
  templateUrl: './cad-usergroup.component.html',
  styleUrl: './cad-usergroup.component.scss',
  host: { ngSkipHydration: 'true' }

})
export class CadUsergroupComponent {
@Input() isVisible : boolean = false;
@Output() modalClose = new EventEmitter();
@Input() model: any;
  titlePage: string = "Grupo de Usuário";

  form!: FormGroup;

  constructor(
    private fb: FormBuilder,
    private globalService: GlobalService,
    private notificationService: NzNotificationService,
    private configService: ConfigService
  ) {
 
  }
    
initializeForm() {
  this.form = this.fb.group({
    id: [0],
    guid: [this.configService.generateGuid(), [Validators.required]],
    nomeGrupoUser: [null, [Validators.required]],
    active: [false],
    deleted: [false]
  });
}
  ngOnInit() {
  }

  ngOnChanges() {
    this.initializeForm();
    
    if (this.model) {
      this.form.patchValue(this.model);
    } else {
      // Quando não há modelo (novo grupo), garante que o formulário seja limpo
      this.form.reset();
      this.initializeForm();
    }
  }

  save() {
    if (this.form.valid && this.form.controls['id'].value == 0 ) {
      this.UserGroupSave();
    }
    else if(this.form.valid && this.form.controls['id'].value > 0){
      this.UserGroupUpdate();
    }
    else {
      Object.values(this.form.controls).forEach(control => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  handleCancel() {
    this.isVisible = false;
    this.form.reset();
    this.modalClose.emit(false);
  }

  public UserGroupSave() {
    this.globalService.post('usergroup/add', this.form.value).subscribe({
      next: (response: any) => {
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro ao criar grupo de usuário',
          `Erro:${error.message}`,
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
      },
      complete: () => {
        this.notificationService.success(
          'Criação',
          'Grupo de usuário criado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.modalClose.emit(true);
        this.isVisible= false;
        this.form.reset();
        this.form.controls['id'].setValue(0);
      }
    });
  }       

  public UserGroupUpdate() {
    this.globalService.put('usergroup/update', this.form.value).subscribe({
      next: (response: any) => {
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro ao criar grupo de usuário',
          `Erro:${error.message}`,
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
      },
      complete: () => {
        this.notificationService.success(
          'Alteração',
          'Grupo de usuario alterado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.modalClose.emit(true);
        this.isVisible= false;
        this.form.reset();
      }
    });

  }
} 
