<nz-modal
    ngSkipHydration="true"
      nzDraggable
      nzCentered
      [(nzVisible)]="isVisible"
      [nzTitle]="titlePage"
      (nzOnCancel)="handleCancel()"
      nzWidth="820px"
    >
      <ng-container *nzModalContent>
          <form nz-form [formGroup]="form" skipHydration="true">
            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="nomeGrupoUser" nzRequired>Nome Grupo</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Nome do grupo é obrigatório">
                <input type="hidden" formControlName="id" id="id" />
                <input nz-input formControlName="nomeGrupoUser" id="nomeGrupoUser" />
              </nz-form-control>
            </nz-form-item>
          
            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="ativo">Ativo</nz-form-label>
              <nz-form-control [nzSpan]="14">
                <nz-switch nz-switch formControlName="active"></nz-switch>
              </nz-form-control>
            </nz-form-item>
          </form>
      </ng-container>
      <div *nzModalFooter>
        <button nz-button (click)="handleCancel()">Cancelar</button>
        <button nz-button nzType="primary" (click)="save()">Salvar</button>
      </div>
    </nz-modal>
