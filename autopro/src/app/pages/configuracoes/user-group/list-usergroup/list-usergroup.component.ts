import { UserGroupModel, UserGroupListDTO, PagedRequest, PagedResult } from '../model/usergroup.model';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { TituloComponent } from '../../../../components/titulo/titulo.component';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { GlobalService } from '../../../../../services/global.service';
import { CommonModule } from '@angular/common';
import { CadUsergroupComponent } from '../cad-usergroup/cad-usergroup.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { ToastService } from '../../../../shared/toast/toast.service';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-list-usergroup',
  imports: [
    TituloComponent,
    FormsModule, 
    NzButtonModule, 
    NzInputModule, 
    NzIconModule,
    NzGridModule,
    NzDescriptionsModule,
    NzTableModule,
    NzTagModule,
    NzPaginationModule,
    CommonModule,
    CadUsergroupComponent,
    NzPopconfirmModule,
    NzToolTipModule
  ],
  templateUrl: './list-usergroup.component.html',
  styleUrl: './list-usergroup.component.scss',
  host: { ngSkipHydration: 'true' },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListUsergroupComponent {

  tituloPagina: string = "Lista Grupos de Usuário";
  firstLevel: string = "Configurações";
  secondLevel: string = "Grupo de Usuário";
  subtitle: string = "Listagem de Grupos de Usuário";
  isLoadingNew: boolean = false;
  isLoadingTable: boolean = false;
  showModalNovo: boolean = false; 
  userGroupList: UserGroupListDTO[] = [];
  userGroupModel: UserGroupModel | null = null;

  // Paginação
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  pageSizeOptions = [10, 20, 50, 100];

  // Busca e filtro
  searchTerm = '';
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Ordenação
  sortBy: string = 'nomegrupouser';
  sortDescending: boolean = false;

  // Cache simples
  private cache = new Map<string, { data: PagedResult<UserGroupListDTO>, timestamp: number }>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutos

  constructor(
    private globalService: GlobalService,
    private modal: NzModalService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef
  ) {
    // Configurar debounce para busca
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.currentPage = 1; // Reset para primeira página na busca
      this.clearCache(); // Limpar cache na busca
      this.loadUserGroupsPaged();
    });
  }

  ngOnInit() {
    this.loadUserGroupsPaged();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onModalClosed() {
    this.showModalNovo = false;
    this.userGroupModel = null;
    this.clearCache(); // Limpar cache após modificação
    this.loadUserGroupsPaged();
  }

  onEdit(item: UserGroupListDTO) {
    // Debug logs para verificar o problema
    
    // Verificar se guid existe e não está vazio
    if (!item.guid) {
      // Se não tiver GUID, usar ID temporariamente para debug
      this.globalService.getbyid('usergroup', item.id).subscribe({
        next: (response: any) => {
          this.userGroupModel = response as UserGroupModel;
          this.showModalNovo = true;
          this.cdr.markForCheck();
        },
        error: (error: any) => {
        }
      });
      return;
    }
    
    // Primeiro carregar dados básicos usando GUID
    this.globalService.getByGuid('usergroup', item.guid).subscribe({
      next: (response: any) => {
        this.userGroupModel = response as UserGroupModel;
        this.showModalNovo = true;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.toastService.error(
          'Erro ao carregar dados do grupo de usuário!',
          'Erro',
          { duration: 3000 }
        );
      }
    });
  }

  onNew() {
    this.showModalNovo = true;
    this.userGroupModel = null;
  }

  onDelete(id: number): void {
    this.globalService.delete(`usergroup/delete/${id}`).subscribe({
      next: (response: any) => {
        this.toastService.success(
          'Grupo de usuário excluído com sucesso!',
          'Operação Concluída'
        );
        this.clearCache(); // Limpar cache após operação
        this.loadUserGroupsPaged();
      },
      error: (error: any) => {
        this.toastService.error(
          'Erro ao excluir grupo de usuário. Tente novamente.',
          'Erro na Operação',
          { duration: 5000 }
        );
      }
    });
  }

  cancel(): void {
    this.toastService.info('Operação cancelada', 'Cancelado');
  }

  confirm(id: number): void {
    this.onDelete(id);
  }

  loadUserGroupsPaged() {
    const cacheKey = `${this.currentPage}_${this.pageSize}_${this.searchTerm}_${this.sortBy}_${this.sortDescending}`;
    
    // Verificar cache primeiro
    const cached = this.cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      this.userGroupList = cached.data.items;
      this.totalItems = cached.data.totalCount;
      this.cdr.markForCheck();
      return;
    }

    this.isLoadingNew = true;
    this.isLoadingTable = true;
    this.cdr.markForCheck();

    const request: PagedRequest = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm.trim() || undefined,
      sortBy: this.sortBy,
      sortDescending: this.sortDescending
    };

    const params = new URLSearchParams();
    params.append('pageNumber', request.pageNumber.toString());
    params.append('pageSize', request.pageSize.toString());
    if (request.searchTerm) {
      params.append('searchTerm', request.searchTerm);
    }
    if (request.sortBy) {
      params.append('sortBy', request.sortBy);
    }
    params.append('sortDescending', request.sortDescending.toString());

    this.globalService.get(`usergroup/paged?${params.toString()}`).subscribe({
      next: (response: any) => {
        const pagedResult = response as PagedResult<UserGroupListDTO>;
        this.userGroupList = pagedResult.items;
        this.totalItems = pagedResult.totalCount;
        
        // Debug log para verificar se os GUIDs estão chegando
        this.userGroupList.forEach((item, index) => {
        });
        
        // Armazenar no cache
        this.cache.set(cacheKey, {
          data: pagedResult,
          timestamp: Date.now()
        });
        
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.toastService.error(
          'Erro ao carregar grupos de usuário. Tente novamente.',
          'Erro',
          { duration: 5000 }
        );
        this.cdr.markForCheck();
      }
    });
  }

  // Método de busca com debounce
  onSearch(): void {
    this.searchSubject.next(this.searchTerm);
  }

  // Limpar busca
  clearSearch(): void {
    this.searchTerm = '';
    this.onSearch();
  }

  // Métodos de paginação
  onPageIndexChange(pageIndex: number): void {
    this.currentPage = pageIndex;
    this.loadUserGroupsPaged();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1; // Reset para primeira página
    this.clearCache(); // Limpar cache ao mudar tamanho da página
    this.loadUserGroupsPaged();
  }

  // Limpar cache
  private clearCache(): void {
    this.cache.clear();
  }

  // Método para ordenação
  onSort(field: string): void {
    if (this.sortBy === field) {
      // Se já está ordenando por este campo, inverte a direção
      this.sortDescending = !this.sortDescending;
    } else {
      // Se é um novo campo, define como ascendente
      this.sortBy = field;
      this.sortDescending = false;
    }
    
    // Reset para primeira página ao ordenar
    this.currentPage = 1;
    this.clearCache(); // Limpar cache ao ordenar
    this.loadUserGroupsPaged();
  }

  // Método para obter o tipo de seta de ordenação
  getSortDirection(field: string): 'asc' | 'desc' | null {
    if (this.sortBy !== field) {
      return null;
    }
    return this.sortDescending ? 'desc' : 'asc';
  }

  // TrackBy function para melhor performance do *ngFor
  trackByFn(index: number, item: UserGroupListDTO): number {
    return item.id;
  }

}
