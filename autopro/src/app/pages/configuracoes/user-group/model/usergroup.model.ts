export interface UserGroupModel {
    id: number;
    guid: string;
    nomeGrupoUser: string;
    active: boolean;
    deleted: boolean;
    createdOn?: Date;
    updatedOn?: Date;
}

export interface UserGroupListDTO {
    id: number;
    guid: string;
    nomeGrupoUser: string;
    active: boolean;
    createdOn: Date;
    updatedOn?: Date;
}

export interface PagedRequest {
    pageNumber: number;
    pageSize: number;
    searchTerm?: string;
    sortBy?: string;
    sortDescending: boolean;
}

export interface PagedResult<T> {
    items: T[];
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
}