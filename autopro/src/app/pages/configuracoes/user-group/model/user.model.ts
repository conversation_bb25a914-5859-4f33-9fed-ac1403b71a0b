export interface UserModel {
  id: number;
  guid: string;
  email: string;
  password: string;
  userGroupId: number;
  active: boolean;
  deleted: boolean;
  createdOn?: Date;
  updatedOn?: Date;
}

export interface UserListDTO {
  id: number;
  guid: string;
  email: string;
  userGroupId: number;
  userGroupName?: string;
  active: boolean;
  createdOn?: Date;
  updatedOn?: Date;
}

export interface PagedRequest {
  pageNumber: number;
  pageSize: number;
  searchTerm?: string;
  sortBy?: string;
  sortDescending: boolean;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}