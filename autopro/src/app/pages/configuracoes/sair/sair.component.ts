import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LocalStorageService } from 'ngx-webstorage';

@Component({
  selector: 'app-sair',
  imports: 
  [
    CommonModule
  ],
  templateUrl: './sair.component.html',
  styleUrl: './sair.component.scss'
})
export class SairComponent implements OnInit {
  
  constructor(
    private route: Router
  ) {
    
  }

  ngOnInit() {
    if (typeof window !== 'undefined') {
      localStorage.clear();
      this.route.navigateByUrl('/login');
    }
  }
}
