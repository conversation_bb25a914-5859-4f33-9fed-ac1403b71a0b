import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { GlobalService } from '../../../../../services/global.service';
import { ConfigService } from '../../../../../services/config.service';


@Component({
  selector: 'app-cad-service',
  imports: [
    CommonModule,
    NzModalModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzSwitchModule,
    NzInputNumberModule,
    ReactiveFormsModule
  ],
  templateUrl: './cad-service.component.html',
  styleUrl: './cad-service.component.scss',
  host: { ngSkipHydration: 'true' }

})
export class CadServiceComponent {
@Input() isVisible : boolean = false;
@Output() modalClose = new EventEmitter();
@Input() model: any;
@ViewChild('iconFile') iconFileInput!: ElementRef;
  titlePage: string = "Serviço";

  form!: FormGroup;
  uploadedIcon: File | null = null;
  iconPreviewUrl: string | null = null;

  constructor(
    private fb: FormBuilder,
    private globalService: GlobalService,
    private notificationService: NzNotificationService,
    private configService: ConfigService
  ) {
 
  }
    
initializeForm() {
  this.form = this.fb.group({
    id: [0],
    guid: [this.configService.generateGuid(), [Validators.required]],
    name: [null, [Validators.required]],
    icon: [null, [Validators.required]],
    description: [null, [Validators.required]],
    valueAmount: [0, [Validators.required, Validators.min(0)]],
    active: [false],
    deleted: [false]
  });
}
  ngOnInit() {
  }

  ngOnChanges() {
    this.initializeForm();
    
    if (this.model) {
      this.form.patchValue(this.model);
    }
  }

  save() {
    if (this.form.valid && this.form.controls['id'].value == 0 ) {
      this.ServiceSave();
    }
    else if(this.form.valid && this.form.controls['id'].value > 0){
      this.ServiceUpdate();
    }
    else {
      Object.values(this.form.controls).forEach(control => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  handleCancel() {
    this.isVisible = false;
    this.modalClose.emit(false);
    this.resetIconUpload();
  }

  triggerIconUpload(): void {
    this.iconFileInput.nativeElement.click();
  }

  onIconSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validar tipo de arquivo
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        this.notificationService.error(
          'Erro',
          'Tipo de arquivo não permitido. Use apenas JPG, PNG ou SVG.',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
        return;
      }

      // Validar tamanho do arquivo (máximo 2MB)
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSize) {
        this.notificationService.error(
          'Erro',
          'Arquivo muito grande. O tamanho máximo é 2MB.',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
        return;
      }

      this.uploadedIcon = file;

      // Criar preview da imagem
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.iconPreviewUrl = e.target.result;
      };
      reader.readAsDataURL(file);

      // Upload da imagem para o servidor
      this.uploadIconToServer(file);
    }
  }

  uploadIconToServer(file: File): void {
    const formData = new FormData();
    formData.append('file', file);

    this.globalService.postFile('upload/service-icon', formData).subscribe({
      next: (response: any) => {
        // Atualizar o form control com o nome do arquivo retornado pelo servidor
        this.form.patchValue({
          icon: response.fileName
        });

        this.notificationService.success(
          'Sucesso',
          'Ícone carregado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );

      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          error.error?.message || 'Erro ao carregar ícone!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );

        // Limpar o upload em caso de erro
        this.resetIconUpload();
      }
    });
  }

  getIconFileName(): string {
    return this.uploadedIcon ? this.uploadedIcon.name : '';
  }

  resetIconUpload(): void {
    this.uploadedIcon = null;
    this.iconPreviewUrl = null;
    if (this.iconFileInput) {
      this.iconFileInput.nativeElement.value = '';
    }
  }

  ServiceSave() {
    this.globalService.post('service/add', this.form.value).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Serviço cadastrado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.handleCancel();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao cadastrar serviço!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  ServiceUpdate() {
    this.globalService.put('service/update', this.form.value).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Serviço atualizado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.handleCancel();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao atualizar serviço!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }
}
