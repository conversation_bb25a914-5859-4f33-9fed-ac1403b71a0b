import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { TituloComponent } from '../../../../components/titulo/titulo.component';
import { CadServiceComponent } from '../cad-service/cad-service.component';
import { GlobalService } from '../../../../../services/global.service';
import { ServiceModel, ServiceListDTO, PagedRequestDTO, PagedResultDTO } from '../model/service.model';
import { ToastService } from '../../../../shared/toast/toast.service';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-list-service',
  imports: [
    TituloComponent,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    CommonModule,
    NzIconModule,
    CadServiceComponent,
    NzPopconfirmModule,
    NzToolTipModule,
    FormsModule,
    NzInputModule,
    NzSpinModule,
    NzEmptyModule,
    NzPaginationModule
  ],
  providers: [
    NzMessageService,
    NzNotificationService
  ],
  templateUrl: './list-service.component.html',
  styleUrl: './list-service.component.scss',
  host: { ngSkipHydration: 'true' },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListServiceComponent {

  tituloPagina: string = "Lista de Serviços";
  firstLevel: string = "Configurações";
  secondLevel: string = "Serviços";
  subtitle: string = "Listagem de Serviços";
  
  // Loading states
  isLoading: boolean = false;
  isLoadingNew: boolean = false;
  isLoadingTable: boolean = false;
  
  // Data properties
  serviceList: ServiceListDTO[] = [];
  serviceModel: ServiceModel | null = null;
  
  // Modal properties
  showModalNovo: boolean = false;

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  pageSizeOptions = [10, 20, 50, 100];

  // Search and filter
  searchTerm = '';
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Sorting
  sortBy: string = 'name';
  sortDescending: boolean = false;

  // Simple cache
  private cache = new Map<string, { data: PagedResultDTO<ServiceListDTO>, timestamp: number }>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  constructor(
    private messageService: NzMessageService,
    private notificationService: NzNotificationService,
    private globalService: GlobalService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef
  ) {
    // Configure debounce for search
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.currentPage = 1; // Reset to first page on search
      this.clearCache(); // Clear cache on search
      this.loadServicesPaged();
    });
  }

  ngOnInit() {
    this.loadServicesPaged();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onModalClosed() {
    this.showModalNovo = false;
    this.serviceModel = null;
    this.clearCache(); // Clear cache after modification
    this.loadServicesPaged();
  }

  onEdit(item: ServiceListDTO) {
    // Debug logs para verificar o problema
    
    // Verificar se guid existe e não está vazio
    if (!item.guid) {
      // Se não tiver GUID, usar ID temporariamente para debug
      this.globalService.getbyid('service', item.id).subscribe({
        next: (response: any) => {
          this.serviceModel = response as ServiceModel;
          this.showModalNovo = true;
          this.cdr.markForCheck();
        },
        error: (error: any) => {
        }
      });
      return;
    }
    
    // First load basic data using GUID
    this.globalService.getByGuid('service', item.guid).subscribe({
      next: (response: any) => {
        this.serviceModel = response as ServiceModel;
        this.showModalNovo = true;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.toastService.error(
          'Erro ao carregar dados do serviço!',
          'Erro',
          { duration: 3000 }
        );
      }
    });
  }

  onNew() {
    this.showModalNovo = true;
    this.serviceModel = null;
  }

  onDelete(id: number): void {
    this.globalService.delete(`service/delete/${id}`).subscribe({
      next: (response: any) => {
        this.toastService.success(
          'Serviço excluído com sucesso!',
          'Operação Concluída'
        );
        this.clearCache(); // Clear cache after operation
        this.loadServicesPaged();
      },
      error: (error: any) => {
        this.toastService.error(
          'Erro ao excluir serviço. Tente novamente.',
          'Erro na Operação',
          { duration: 5000 }
        );
      }
    });
  }

  cancel(): void {
    this.toastService.info('Operação cancelada', 'Cancelado');
  }

  confirm(id: number): void {
    this.onDelete(id);
  }

  loadServicesPaged() {
    const cacheKey = `${this.currentPage}_${this.pageSize}_${this.searchTerm}_${this.sortBy}_${this.sortDescending}`;
    
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      this.serviceList = cached.data.items;
      this.totalItems = cached.data.totalCount;
      this.cdr.markForCheck();
      return;
    }

    this.isLoading = true;
    this.isLoadingTable = true;
    this.cdr.markForCheck();

    const request: PagedRequestDTO = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm.trim() || undefined,
      sortBy: this.sortBy,
      sortDescending: this.sortDescending
    };

    this.globalService.post('service/paged', request).subscribe({
      next: (response: any) => {
        const pagedResult = response as PagedResultDTO<ServiceListDTO>;
        this.serviceList = pagedResult.items;
        this.totalItems = pagedResult.totalCount;
        
        // Debug log para verificar se os GUIDs estão chegando
        this.serviceList.forEach((item, index) => {
        });
        
        // Store in cache
        this.cache.set(cacheKey, {
          data: pagedResult,
          timestamp: Date.now()
        });
        
        this.isLoading = false;
        this.isLoadingTable = false;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.isLoading = false;
        this.isLoadingTable = false;
        this.toastService.error(
          'Erro ao carregar serviços. Tente novamente.',
          'Erro',
          { duration: 5000 }
        );
        this.cdr.markForCheck();
      }
    });
  }

  // Search method with debounce
  onSearch(): void {
    this.searchSubject.next(this.searchTerm);
  }

  // Clear search
  clearSearch(): void {
    this.searchTerm = '';
    this.onSearch();
  }

  // Pagination methods
  onPageIndexChange(pageIndex: number): void {
    this.currentPage = pageIndex;
    this.loadServicesPaged();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1; // Reset to first page
    this.clearCache(); // Clear cache when changing page size
    this.loadServicesPaged();
  }

  // Clear cache
  private clearCache(): void {
    this.cache.clear();
  }

  // Method for sorting
  onSort(field: string): void {
    if (this.sortBy === field) {
      // If already sorting by this field, reverse direction
      this.sortDescending = !this.sortDescending;
    } else {
      // If it's a new field, set as ascending
      this.sortBy = field;
      this.sortDescending = false;
    }
    
    // Reset to first page when sorting
    this.currentPage = 1;
    this.clearCache(); // Clear cache when sorting
    this.loadServicesPaged();
  }

  // Method to get sort arrow type
  getSortDirection(field: string): 'asc' | 'desc' | null {
    if (this.sortBy !== field) {
      return null;
    }
    return this.sortDescending ? 'desc' : 'asc';
  }

  // Utility methods
  isImageFile(icon: string): boolean {
    if (!icon) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.svg'];
    return imageExtensions.some(ext => icon.toLowerCase().endsWith(ext));
  }

  getImagePath(filename: string): string {
    return `http://localhost:5090/uploads/service-icons/${filename}`;
  }

  // Getter for empty state
  get hasNoResults(): boolean {
    return this.serviceList.length === 0 && !this.isLoading;
  }

  get hasNoData(): boolean {
    return this.totalItems === 0 && !this.isLoading;
  }

  // TrackBy function for better performance
  trackByFn(index: number, item: ServiceListDTO): number {
    return item.id;
  }

}
