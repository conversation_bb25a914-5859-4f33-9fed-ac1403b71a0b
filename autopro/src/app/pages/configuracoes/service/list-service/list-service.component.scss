.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: space-between;
    
    .search-container {
      flex: 1;
      max-width: 400px;
      
      nz-input-group {
        .ant-input {
          border-radius: 6px;
        }
      }
    }
    
    .btnNovo {
      display: flex;
      justify-content: flex-end;
      
      button {
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }
}

.page-content {
  background: white;
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  // Loading state
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    
    .loading-content {
      width: 100%;
      height: 200px;
    }
  }
  
  // Empty states
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    
    nz-empty {
      .ant-empty-description {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  // Table improvements
  nz-table {
    .ant-table {
      border-radius: 6px;
      
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        border-bottom: 2px solid #f0f0f0;
        font-weight: 600;
        color: #262626;
      }
      
      .ant-table-tbody > tr > td {
        padding: 12px 16px;
        
        // Service ID styling
        .service-id {
          color: #8c8c8c;
          font-family: 'Courier New', monospace;
          font-size: 12px;
        }
        
        // Service name styling
        .service-name {
          strong {
            color: #262626;
            font-size: 14px;
          }
        }
        
        // Icon display improvements
        .icon-display {
          display: flex;
          align-items: center;
          
          .service-icon-image {
            width: 100%;
            min-width: 32px;
            min-height: 32px;
            max-width: 120px !important;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            display: block;
          }
          
          .fa-icon-display {
            display: flex;
            align-items: center;
            
            .service-icon-fa {
              font-size: 16px;
              color: #1890ff;
            }
            
            .icon-name {
              margin-left: 8px;
              font-size: 12px;
              color: #8c8c8c;
            }
          }
          
          .image-name {
            margin-left: 8px;
            font-size: 12px;
            color: #8c8c8c;
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        // Service description
        .service-description {
          color: #595959;
          font-size: 13px;
          line-height: 1.4;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        // Service value
        .service-value {
          strong {
            color: #52c41a;
            font-size: 14px;
          }
        }
        
        // Status tag improvements
        .status-tag {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          
          i {
            font-size: 12px;
          }
        }
        
        // Action buttons
        .action-buttons {
          display: flex;
          justify-content: center;
          gap: 4px;
          
          .edit-btn {
            color: #1890ff;
            
            &:hover {
              background-color: #e6f7ff;
              border-color: #91d5ff;
            }
          }
          
          .delete-btn {
            color: #ff4d4f;
            
            &:hover {
              background-color: #fff2f0;
              border-color: #ffccc7;
            }
          }
        }
      }
      
      // Row hover effects
      .ant-table-tbody > tr:hover > td {
        background-color: #fafafa;
      }
    }
  }
}

// Responsive design
// Force table cell styles
::ng-deep {
  .ant-table-wrapper {
    .ant-table {
      .ant-table-tbody > tr > td {
        .icon-display {
          .service-icon-image {
            width: 100%;
            min-width: 32px;
            min-height: 32px;
            max-width: 132px !important;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .page-header {
    .header-controls {
      flex-direction: column;
      gap: 12px;
      
      .search-container {
        max-width: 100%;
      }
    }
  }
  
  .page-content {
    padding: 12px;
    
    nz-table {
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
        
        .service-description {
          max-width: 150px;
        }
      }
    }
  }
}
