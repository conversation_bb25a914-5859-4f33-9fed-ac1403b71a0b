<app-titulo 
    [title]="tituloPagina" 
    [firstLevel]="firstLevel" 
    [secondLevel]="secondLevel"
    [subtitle]="subtitle"
    >
</app-titulo>

<!-- HEADER WITH SEARCH AND NEW BUTTON -->
<div class="page-header">
    <app-cad-service 
        [isVisible]="showModalNovo" 
        [model]="serviceModel"
        (modalClose)="onModalClosed()">
    </app-cad-service>
    
    <div class="btnNovo">
        <button nz-button nzType="primary" (click)="onNew()">+ Novo</button>
    </div>
</div> 

<!-- PAGE CONTENT WITH SEARCH, LOADING AND TABLE -->
<div class="page-content">
  <!-- Search Section -->
  <div class="search-section" style="margin-bottom: 16px;">
    <nz-input-group [nzSuffix]="suffixIconButton" style="width: 300px;">
      <input 
        type="text" 
        nz-input 
        placeholder="Buscar por nome, descrição..." 
        [(ngModel)]="searchTerm"
        (input)="onSearch()"
        (keyup.enter)="onSearch()">
    </nz-input-group>
    <ng-template #suffixIconButton>
      <button 
        nz-button 
        nzType="text" 
        nzSize="small" 
        (click)="clearSearch()" 
        *ngIf="searchTerm"
        nz-tooltip="Limpar busca">
        <i nz-icon nzType="close" nzTheme="outline"></i>
      </button>
      <i nz-icon nzType="search" nzTheme="outline" *ngIf="!searchTerm"></i>
    </ng-template>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <nz-spin>
      <div class="loading-content"></div>
    </nz-spin>
  </div>

  <!-- Empty State - No Data -->
  <div *ngIf="hasNoData" class="empty-state">
    <nz-empty 
      nzNotFoundImage="simple" 
      nzNotFoundContent="Nenhum serviço encontrado">
      <span nz-typography nzType="secondary">
        Não há serviços cadastrados no sistema.
      </span>
      <br>
      <button nz-button nzType="primary" (click)="onNew()" style="margin-top: 16px;">
        + Cadastrar Primeiro Serviço
      </button>
    </nz-empty>
  </div>

  <!-- Empty State - No Search Results -->
  <div *ngIf="hasNoResults && !hasNoData" class="empty-state">
    <nz-empty 
      nzNotFoundImage="simple" 
      [nzNotFoundContent]="'Nenhum resultado encontrado' + (searchTerm ? ' para: ' + searchTerm : '')">
      <span nz-typography nzType="secondary">
        Tente ajustar os termos da sua busca.
      </span>
    </nz-empty>
  </div>

  <!-- Services Table with Server-side Pagination -->
  <nz-table 
    *ngIf="!isLoading && !hasNoData && !hasNoResults"
    #basicTable 
    [nzLoading]="isLoadingTable" 
    ngSkipHydration="true" 
    [nzData]="serviceList" 
    [nzBordered]="true"
    [nzSize]="'middle'"
    [nzFrontPagination]="false"
    [nzShowSizeChanger]="true"
    [nzPageSizeOptions]="pageSizeOptions"
    [nzPageSize]="pageSize"
    [nzPageIndex]="currentPage"
    [nzTotal]="totalItems"
    (nzPageIndexChange)="onPageIndexChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    
    <thead>
      <tr>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('id')" nzWidth="80px">
          ID
          <i *ngIf="getSortDirection('id') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('id') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('id') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('name')" nzWidth="200px">
          Nome
          <i *ngIf="getSortDirection('name') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('name') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('name') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th nzWidth="150px">Ícone</th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('description')">
          Descrição
          <i *ngIf="getSortDirection('description') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('description') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('description') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('valueAmount')" nzWidth="120px">
          Valor (R$)
          <i *ngIf="getSortDirection('valueAmount') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('valueAmount') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('valueAmount') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('active')" nzWidth="100px">
          Status
          <i *ngIf="getSortDirection('active') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('active') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('active') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('createdOn')" nzWidth="150px">
          Data Criação
          <i *ngIf="getSortDirection('createdOn') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('createdOn') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('createdOn') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th nzWidth="120px" nzAlign="center">Ações</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let item of basicTable.data; trackBy: trackByFn">
        <td>
          <span class="service-id">#{{ item.id }}</span>
        </td>
        <td>
          <div class="service-name">
            <strong>{{ item.name }}</strong>
          </div>
        </td>
        <td>
          <div class="icon-display">
            <!-- Display image if it's an image file -->
            <img *ngIf="isImageFile(item.icon)"
                 [src]="getImagePath(item.icon)"
                 [alt]="item.name"
                 class="service-icon-image">
          </div>
        </td>
        <td>
          <div class="service-description">
            {{ item.description }}
          </div>
        </td>
        <td>
          <div class="service-value">
            <strong>R$ {{ item.valueAmount | number:'1.2-2' }}</strong>
          </div>
        </td>
        <td>
          <nz-tag 
            [nzColor]="item.active ? 'success' : 'error'"
            class="status-tag">
            <i nz-icon [nzType]="item.active ? 'check-circle' : 'close-circle'"></i>
            {{ item.active ? 'Ativo' : 'Inativo' }}
          </nz-tag>
        </td>
        <td>{{ item.createdOn | date:'dd/MM/yyyy HH:mm' }}</td>
        <td nzAlign="center">
          <div class="action-buttons">
            <button 
              nz-button 
              nzType="text" 
              nzSize="small"
              nz-tooltip="Editar" 
              (click)="onEdit(item)"
              class="edit-btn">
              <i nz-icon nzType="edit" nzTheme="outline"></i>
            </button>
            
            <button 
              nz-button 
              nzType="text" 
              nzSize="small"
              nz-tooltip="Excluir"
              nz-popconfirm
              nzPopconfirmTitle="Deseja excluir este serviço?"
              nzOkText="Sim"
              nzCancelText="Cancelar"
              (nzOnConfirm)="confirm(item.id)"
              (nzOnCancel)="cancel()"
              nzDanger
              class="delete-btn">
              <i nz-icon nzType="delete" nzTheme="outline"></i>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <!-- Total Records Display -->
  <div *ngIf="!isLoading && totalItems > 0" class="total-records" style="margin-top: 16px; text-align: center; color: #666;">
    Total de {{ totalItems }} serviço{{ totalItems > 1 ? 's' : '' }} encontrado{{ totalItems > 1 ? 's' : '' }}
  </div>
</div>
