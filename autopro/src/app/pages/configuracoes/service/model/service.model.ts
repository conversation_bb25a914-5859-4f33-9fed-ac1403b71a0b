export interface ServiceModel{
    id: number;
    guid: string;
    name: string;
    icon: string;
    description: string;
    valueAmount: number;
    active: boolean;
    deleted: boolean;
}

export interface ServiceListDTO {
    id: number;
    guid: string;
    name: string;
    icon: string;
    description: string;
    valueAmount: number;
    active: boolean;
    createdOn: Date;
    updatedOn?: Date;
}

export interface PagedRequestDTO {
    pageNumber: number;
    pageSize: number;
    searchTerm?: string;
    sortBy?: string;
    sortDescending: boolean;
}

export interface PagedResultDTO<T> {
    items: T[];
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
}
