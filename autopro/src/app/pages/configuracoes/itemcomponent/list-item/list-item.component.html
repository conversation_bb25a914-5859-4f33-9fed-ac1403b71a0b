<app-titulo 
    [title]="tituloPagina" 
    [firstLevel]="firstLevel" 
    [secondLevel]="secondLevel"
    [subtitle]="subtitle">
</app-titulo>

<!-- NOVO -->
<div class="page-header">
    <app-itemcomponent 
        [isVisible]="showModalNovo" 
        [model]="itemModel"
        (modalClose)="onModalClosed()">
    </app-itemcomponent> 
    <div class="btnNovo">
        <button nz-button nzType="primary" (click)="onNew()">+ Novo Item</button>
    </div>
</div> 

<div class="page-content">
  <!-- Busca -->
  <div class="search-section" style="margin-bottom: 16px;">
    <nz-input-group [nzSuffix]="suffixIconButton" style="width: 300px;">
      <input 
        type="text" 
        nz-input 
        placeholder="Buscar por nome do item..." 
        [(ngModel)]="searchTerm"
        (input)="onSearch()"
        (keyup.enter)="onSearch()">
    </nz-input-group>
    <ng-template #suffixIconButton>
      <button 
        nz-button 
        nzType="text" 
        nzSize="small" 
        (click)="clearSearch()" 
        *ngIf="searchTerm"
        nz-tooltip="Limpar busca">
        <i nz-icon nzType="close" nzTheme="outline"></i>
      </button>
      <i nz-icon nzType="search" nzTheme="outline" *ngIf="!searchTerm"></i>
    </ng-template>
  </div>

  <!-- Tabela -->
  <nz-table 
    #basicTable 
    [nzLoading]="isLoadingTable" 
    ngSkipHydration="true" 
    [nzData]="itemList" 
    [nzBordered]="true"
    [nzSize]="'middle'"
    [nzFrontPagination]="false"
    [nzShowSizeChanger]="true"
    [nzPageSizeOptions]="pageSizeOptions"
    [nzPageSize]="pageSize"
    [nzPageIndex]="currentPage"
    [nzTotal]="totalItems"
    (nzPageIndexChange)="onPageIndexChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    <thead>
      <tr>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('id')">
          ID
          <i *ngIf="getSortDirection('id') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('id') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('id') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('name')">
          Nome
          <i *ngIf="getSortDirection('name') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('name') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('name') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th>Descrição</th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('isActive')">
          Ativo
          <i *ngIf="getSortDirection('isActive') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('isActive') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('isActive') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th>Ações</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of basicTable.data; trackBy: trackByFn">
        <td>{{ item.id }}</td>
        <td>
          <strong>{{ item.name }}</strong>
        </td>
        <td>
          <div class="item-description">
            {{ item.description }}
          </div>
        </td>
        <td>
          <nz-tag [nzColor]="item.isActive ? 'green' : 'red'">
            {{ item.isActive ? 'Sim' : 'Não' }}
          </nz-tag>
        </td>
        <td>
          <button nz-button nzTooltipTitle="Editar" nzType="text" nz-tooltip="Editar" (click)="onEdit(item)">
            <i nz-icon nzType="edit" nzTheme="outline"></i>
          </button>
          <a
            nz-popconfirm
            nzPopconfirmTitle="Deseja excluir esse item?"
            nzOkText="Sim"
            nzDanger
            nzCancelText="Cancelar"
            (nzOnConfirm)="confirm(item.id)"
            (nzOnCancel)="cancel()"
            nzTooltipTitle="Excluir Item">
            <i nz-icon nzType="delete" nzTheme="outline"></i>
          </a>
        </td>
      </tr>
    </tbody>
  </nz-table>

</div>

<div class="page-footer">

</div>
