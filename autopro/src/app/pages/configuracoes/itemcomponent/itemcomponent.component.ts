import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { GlobalService } from '../../../../services/global.service';
import { ItemModel } from './model/ItemModel';

@Component({
  selector: 'app-itemcomponent',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzModalModule,
    NzSwitchModule
  ],
  providers: [NzNotificationService],
  templateUrl: './itemcomponent.component.html',
  styleUrl: './itemcomponent.component.scss'
})
export class ItemcomponentComponent implements OnChanges {
  @Input() isVisible: boolean = false;
  @Input() model: ItemModel | null = null;
  @Output() modalClose = new EventEmitter<void>();

  titlePage: string = "Item";
  firstLevel: string = "Configurações";
  secondLevel: string = "Item";
  subtitle: string = "Cadastro de Item";

  form!: FormGroup;
  isLoading: boolean = false;

  constructor(
    private fb: FormBuilder,
    private notificationService: NzNotificationService,
    private globalService: GlobalService
  ) {
    this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['model'] && this.model) {
      this.loadModelData();
    } else if (changes['isVisible'] && this.isVisible && !this.model) {
      this.form.reset();
      this.initializeForm();
    }
  }

  loadModelData() {
    if (this.model) {
      this.form.patchValue({
        id: this.model.id,
        name: this.model.name,
        description: this.model.description,
        isActive: this.model.isActive ?? true
      });
    }
  }

  initializeForm() {
    this.form = this.fb.group({
      id: [0],
      name: [null, [Validators.required]],
      description: [null, [Validators.required]],
      isActive: [true]
    });
  }

  save() {
    if (this.form.valid) {
      this.isLoading = true;

      const endpoint = this.model ? `item/update/${this.model.id}` : 'item/add';
      const method = this.model ? 'put' : 'post';

      this.globalService[method](endpoint, this.form.value).subscribe({
        next: () => {
          this.notificationService.success(
            'Sucesso',
            this.model ? 'Item atualizado com sucesso!' : 'Item salvo com sucesso!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice-success'
            }
          );
          this.closeModal();
        },
        error: () => {
          this.notificationService.error(
            'Erro',
            this.model ? 'Erro ao atualizar item!' : 'Erro ao salvar item!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice-error'
            }
          );
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    } else {
      Object.values(this.form.controls).forEach(control => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  cancel() {
    this.closeModal();
  }

  closeModal() {
    this.form.reset();
    this.initializeForm();
    this.modalClose.emit();
  }
}
