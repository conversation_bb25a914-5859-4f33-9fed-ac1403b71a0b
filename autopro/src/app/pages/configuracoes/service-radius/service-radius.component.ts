import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { GlobalService } from '../../../../services/global.service';
import { RadiusModel } from './model/radiusModel';
import { TituloComponent } from '../../../components/titulo/titulo.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-service-radius',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzFormModule,
    NzInputModule,
    NzInputNumberModule,
    NzIconModule,
    NzSpinModule,
    NzDividerModule,
    NzPopconfirmModule,
    TituloComponent
  ],
  providers: [
        NzModalService,
        NzMessageService,
        NzNotificationService
      ],
  templateUrl: './service-radius.component.html',
  styleUrl: './service-radius.component.scss'
})
export class ServiceRadiusComponent implements OnInit {
  titlePage: string = "Escala de Raio de Atendimento";
  firstLevel: string = "Configurações";
  secondLevel: string = "Raio de Atendimento";
  subtitle: string = "Gerencie as escalas de raio de atendimento";

  form!: FormGroup;
  radiusList: RadiusModel[] = [];
  isLoading: boolean = false;

  constructor(
    private fb: FormBuilder,
    private globalService: GlobalService,
    private notificationService: NzNotificationService,
    private modal: NzModalService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    this.loadRadius();
  }

  initializeForm() {
    this.form = this.fb.group({
      distance: [null, [Validators.required, Validators.min(1)]]
    });
  }

  loadRadius() {
    this.isLoading = true;
    this.globalService.get('radius').subscribe({
      next: (response: any) => {
        this.radiusList = response.sort((a: { distance: number; }, b: { distance: number; }) => a.distance - b.distance);
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao carregar escalas de raio!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      },
      complete: () => {
        this.isLoading = false;
      }
    });
  }

  addRadius() {
    if (this.form.valid) {
      const newRadius = {
        id: 0,
        raioAtendimento: this.form.value.distance,
        active: true
      };

      this.globalService.post('radius/add', newRadius).subscribe({
        next: (response: any) => {
          this.notificationService.success(
            'Sucesso',
            'Escala de raio adicionada com sucesso!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice.success'
            }
          );
          this.form.reset();
          this.loadRadius();
        },
        error: (error: any) => {
          this.notificationService.error(
            'Erro',
            'Não é permitido adicionar uma escala de raio existente!',
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice.error'
            }
          );
        }
      });
    } else {
      Object.values(this.form.controls).forEach(control => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  deleteRadius(id: number) {
    this.modal.confirm({
    nzTitle: 'Tem certeza que deseja excluir este raio de atendimento?',
    nzContent: 'Essa ação não poderá ser desfeita.',
    nzOkText: 'Sim',
    nzCancelText: 'Não',
    nzOnOk: () =>
        this.globalService.delete(`radius/delete/${id}`).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Escala de raio excluída com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.loadRadius();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao excluir escala de raio!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    })
    })
    
  }

  save() {
    this.notificationService.success(
      'Sucesso',
      'Configurações salvas com sucesso!',
      {
        nzDuration: 3000,
        nzClass: 'ant-notification-notice.success'
      }
    );
  }

  cancel() {
    this.form.reset();
  }
}
function provideNzModal(): import("@angular/core").Provider {
  throw new Error('Function not implemented.');
}

