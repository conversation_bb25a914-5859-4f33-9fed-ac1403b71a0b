.page-content {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 70vh;
  display: flex;
  flex-direction: column;

  .insert-section {
    margin-bottom: 32px;

    h3 {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #262626;
    }

    .insert-form {
      .input-group {
        display: flex;
        gap: 12px;
        align-items: center;

        .distance-input {
          width: 300px;
          height: 40px;
        }

        button {
          color: white;  
          background-color: rgb(29, 179, 29);
          border: none;
          width: 100px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  .radius-list {
    margin: 32px 0;
    flex: 1;

    .radius-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;
      width: 100%;

      &:last-child {
        border-bottom: none;
      }

      &.inactive {
        opacity: 0.6;
      }

      .distance-label {
        font-size: 16px;
        color: #262626;
        font-weight: 500;
        flex: 1;
        text-align: left;
      }

      button {
        color: #ff4d4f;
        border: none;
        margin-right: 30px;
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: auto;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;

    button {
      min-width: 100px;
      height: 40px;
      font-weight: 500;
    }
  }
}

