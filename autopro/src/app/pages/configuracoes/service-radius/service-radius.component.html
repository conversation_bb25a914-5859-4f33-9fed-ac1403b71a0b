<app-titulo 
  [title]="titlePage" 
  [firstLevel]="firstLevel" 
  [secondLevel]="secondLevel"
  [subtitle]="subtitle">
</app-titulo>

<div class="page-content">

  <div class="insert-section">
    <h3>Insira a Escala de Atendimento</h3>
    <form nz-form [formGroup]="form" class="insert-form">
      <div class="input-group">
        <nz-input-number 
          formControlName="distance"
          [nzMin]="1"
          [nzStep]="1"
          nzPlaceHolder="Digite a distância em KM"
          class="distance-input">
        </nz-input-number>
        <button 
          nzType="primary" 
          nzShape="circle"
          (click)="addRadius()"
          [disabled]="form.invalid">
          Adicionar
        </button>
      </div>
    </form>
  </div>

  <nz-divider nzText="Escala em KM" nzOrientation="center"></nz-divider>


  <nz-spin [nzSpinning]="isLoading">
    <div class="radius-list">
      <div 
        *ngFor="let radius of radiusList" 
        class="radius-item">
        <span class="distance-label">- {{ radius.raioAtendimento }} km</span>
        <button 
          nz-button 
          (click)="deleteRadius(radius.id)">
          <i nz-icon nzType="delete" nzTheme="outline"></i>
        </button>
      </div>
    </div>
  </nz-spin>
</div>
