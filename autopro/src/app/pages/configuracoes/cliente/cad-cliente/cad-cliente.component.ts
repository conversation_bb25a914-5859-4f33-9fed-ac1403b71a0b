import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { GlobalService } from '../../../../../services/global.service';
import { ClienteModel, ClienteFormModel, StateModel, CityModel, DistrictModel } from '../model/cliente.model';

@Component({
  selector: 'app-cad-cliente',
  imports: [
    CommonModule,
    NzModalModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzSwitchModule,
    NzSelectModule,
    NzDividerModule,
    NzDatePickerModule,
    ReactiveFormsModule
  ],
  templateUrl: './cad-cliente.component.html',
  styleUrl: './cad-cliente.component.scss',
  host: { ngSkipHydration: 'true' }
})
export class CadClienteComponent {
  @Input() isVisible: boolean = false;
  @Output() modalClose = new EventEmitter();
  @Input() model: ClienteModel | null = null;
  
  titlePage: string = "Cliente";
  form!: FormGroup;
  isLoading: boolean = false;
  
  // Dados para selects
  estados: StateModel[] = [];
  cidades: CityModel[] = [];
  bairros: DistrictModel[] = [];

  tiposCliente = [
    { label: 'Pessoa Física', value: 'PF' },
    { label: 'Pessoa Jurídica', value: 'PJ' }
  ];

  constructor(
    private fb: FormBuilder,
    private globalService: GlobalService,
    private notificationService: NzNotificationService
  ) {
  }
    
  initializeForm() {
    this.form = this.fb.group({
      id: [0],
      guid: [null],
      name: [null, [Validators.required, Validators.maxLength(255)]],
      type: [null, [Validators.required]],
      fiscalNumber: [null, [Validators.required, Validators.maxLength(20)]],
      birthDate: [null],
      address: [null, [Validators.required, Validators.maxLength(255)]],
      number: [null, [Validators.required, Validators.maxLength(20)]],
      addressComplement: [null, [Validators.maxLength(255)]],
      stateId: [null, [Validators.required]],
      cityId: [null, [Validators.required]],
      districtId: [null, [Validators.required]],
      active: [true]
    });

    // Observar mudanças no tipo para validar CPF/CNPJ
    this.form.get('type')?.valueChanges.subscribe((type) => {
      this.updateFiscalNumberValidation(type);
    });

  }

  ngOnInit() {
    this.loadEstados();
    this.loadCidades();
    this.loadBairros();
  }

  ngOnChanges() {
    this.initializeForm();
    
    if (this.model) {
      this.form.patchValue(this.model);
      this.titlePage = "Editar Cliente";
    } else {
      this.titlePage = "Novo Cliente";
    }
  }

  updateFiscalNumberValidation(type: string) {
    const fiscalControl = this.form.get('fiscalNumber');
    if (!fiscalControl) return;

    if (type === 'PF') {
      fiscalControl.setValidators([
        Validators.required,
        Validators.pattern(/^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$/)
      ]);
    } else if (type === 'PJ') {
      fiscalControl.setValidators([
        Validators.required,
        Validators.pattern(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$|^\d{14}$/)
      ]);
    }
    
    fiscalControl.updateValueAndValidity();
  }

  loadEstados() {
    this.globalService.get('state/list').subscribe({
      next: (response: any) => {
        this.estados = response as StateModel[];
      },
      error: (error) => {
      }
    });
  }

  loadCidades() {
    this.globalService.get('city/list').subscribe({
      next: (response: any) => {
        this.cidades = response as CityModel[];
      },
      error: (error) => {
      }
    });
  }

  loadBairros() {
    this.globalService.get('district/list').subscribe({
      next: (response: any) => {
        this.bairros = response as DistrictModel[];
      },
      error: (error) => {
      }
    });
  }

  onSubmit() {
    if (this.form.valid) {
      this.isLoading = true;
      const formData = this.form.value;
      
      // Limpar CPF/CNPJ mantendo apenas números
      if (formData.fiscalNumber) {
        formData.fiscalNumber = formData.fiscalNumber.replace(/\D/g, '');
      }

      // Gerar GUID se não existir
      if (!formData.guid) {
        formData.guid = '00000000-0000-0000-0000-000000000000';
      }

      // Garantir que addressComplement seja string vazia se for null
      if (!formData.addressComplement) {
        formData.addressComplement = '';
      }

      const isEdit = formData.id && formData.id > 0;
      const endpoint = isEdit ? 'customer/update' : 'customer/add';
      const method = isEdit ? 'put' : 'post';

      this.globalService[method](endpoint, formData).subscribe({
        next: (response: any) => {
          this.notificationService.success(
            'Sucesso',
            `Cliente ${isEdit ? 'atualizado' : 'cadastrado'} com sucesso!`,
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice.success'
            }
          );
          this.isLoading = false;
          this.closeModal();
        },
        error: (error: any) => {
          this.isLoading = false;
          this.notificationService.error(
            'Erro',
            `Erro ao ${isEdit ? 'atualizar' : 'cadastrar'} cliente!`,
            {
              nzDuration: 3000,
              nzClass: 'ant-notification-notice.error'
            }
          );
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  closeModal() {
    this.isVisible = false;
    this.modalClose.emit();
    this.form.reset();
  }

  markFormGroupTouched() {
    Object.keys(this.form.controls).forEach(key => {
      const control = this.form.get(key);
      control?.markAsTouched();
      control?.updateValueAndValidity();
    });
  }

  // Formatação de CPF/CNPJ
  onFiscalNumberChange(event: any) {
    let value = event.target.value.replace(/\D/g, '');
    const type = this.form.get('type')?.value;
    
    if (type === 'PF' && value.length <= 11) {
      value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    } else if (type === 'PJ' && value.length <= 14) {
      value = value.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    }
    
    this.form.patchValue({ fiscalNumber: value });
  }
  
  // Validações de exibição
  isFieldInvalid(fieldName: string): boolean {
    const field = this.form.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field?.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return 'Este campo é obrigatório';
      }
      if (field.errors['email']) {
        return 'Email inválido';
      }
      if (field.errors['pattern']) {
        const type = this.form.get('type')?.value;
        if (fieldName === 'fiscalNumber') {
          return type === 'PF' ? 'CPF inválido' : 'CNPJ inválido';
        }
      }
      if (field.errors['maxlength']) {
        return `Máximo de ${field.errors['maxlength'].requiredLength} caracteres`;
      }
    }
    return '';
  }
}