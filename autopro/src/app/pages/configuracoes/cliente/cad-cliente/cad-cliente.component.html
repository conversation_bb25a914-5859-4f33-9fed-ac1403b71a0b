<nz-modal 
  [(nzVisible)]="isVisible" 
  [nzTitle]="titlePage" 
  [nzClosable]="true"
  [nzMaskClosable]="false"
  [nzWidth]="800"
  (nzOnCancel)="closeModal()"
  [nzFooter]="null">
  
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="onSubmit()" nzLayout="vertical">
      
      <!-- Dados Básicos -->
      <nz-divider nzText="Dados Básicos" nzOrientation="left"></nz-divider>
      
      <div nz-row [nzGutter]="16">
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired>Nome</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('name')">
              <input nz-input 
                     formControlName="name" 
                     placeholder="Nome completo"
                     [nzStatus]="isFieldInvalid('name') ? 'error' : ''" />
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired>Tipo</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('type')">
              <nz-select formControlName="type" 
                         nzPlaceHolder="Selecione o tipo"
                         [nzStatus]="isFieldInvalid('type') ? 'error' : ''">
                <nz-option 
                  *ngFor="let tipo of tiposCliente" 
                  [nzValue]="tipo.value" 
                  [nzLabel]="tipo.label">
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div nz-row [nzGutter]="16">
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired>
              {{ form.get('type')?.value === 'PF' ? 'CPF' : 'CNPJ' }}
            </nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('fiscalNumber')">
              <input nz-input 
                     formControlName="fiscalNumber" 
                     [placeholder]="form.get('type')?.value === 'PF' ? '000.000.000-00' : '00.000.000/0000-00'"
                     (input)="onFiscalNumberChange($event)"
                     [nzStatus]="isFieldInvalid('fiscalNumber') ? 'error' : ''" />
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col [nzSpan]="12" *ngIf="form.get('type')?.value === 'PF'">
          <nz-form-item>
            <nz-form-label>Data de Nascimento</nz-form-label>
            <nz-form-control>
              <nz-date-picker 
                formControlName="birthDate"
                nzPlaceHolder="Selecione a data"
                nzFormat="dd/MM/yyyy"
                style="width: 100%;">
              </nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Endereço -->
      <nz-divider nzText="Endereço" nzOrientation="left"></nz-divider>
      
      <!-- Primeira linha: Estado, Cidade, Bairro -->
      <div nz-row [nzGutter]="16">
        <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
          <nz-form-item>
            <nz-form-label nzRequired>Estado</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('stateId')">
              <nz-select formControlName="stateId" 
                         nzPlaceHolder="Selecione o estado"
                         [nzStatus]="isFieldInvalid('stateId') ? 'error' : ''">
                <nz-option 
                  *ngFor="let estado of estados" 
                  [nzValue]="estado.id" 
                  [nzLabel]="estado.state">
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
          <nz-form-item>
            <nz-form-label nzRequired>Cidade</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('cityId')">
              <nz-select formControlName="cityId" 
                         nzPlaceHolder="Selecione a cidade"
                         [nzStatus]="isFieldInvalid('cityId') ? 'error' : ''">
                <nz-option 
                  *ngFor="let cidade of cidades" 
                  [nzValue]="cidade.id" 
                  [nzLabel]="cidade.city">
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
          <nz-form-item>
            <nz-form-label nzRequired>Bairro</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('districtId')">
              <nz-select formControlName="districtId" 
                         nzPlaceHolder="Selecione o bairro"
                         [nzStatus]="isFieldInvalid('districtId') ? 'error' : ''">
                <nz-option 
                  *ngFor="let bairro of bairros" 
                  [nzValue]="bairro.id" 
                  [nzLabel]="bairro.district">
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Segunda linha: Endereço, Número, CEP -->
      <div nz-row [nzGutter]="16">
        <div nz-col [nzXs]="24" [nzSm]="24" [nzMd]="12">
          <nz-form-item>
            <nz-form-label nzRequired>Endereço</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('address')">
              <input nz-input 
                     formControlName="address" 
                     placeholder="Rua, avenida, etc."
                     [nzStatus]="isFieldInvalid('address') ? 'error' : ''" />
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col [nzXs]="12" [nzSm]="12" [nzMd]="6">
          <nz-form-item>
            <nz-form-label nzRequired>Número</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('number')">
              <input nz-input 
                     formControlName="number" 
                     placeholder="Número"
                     [nzStatus]="isFieldInvalid('number') ? 'error' : ''" />
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col [nzXs]="12" [nzSm]="12" [nzMd]="6">
          <nz-form-item>
            <nz-form-label>CEP</nz-form-label>
            <nz-form-control>
              <input nz-input 
                     formControlName="cep" 
                     placeholder="00000-000"
                     maxlength="9" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Terceira linha: Complemento -->
      <div nz-row [nzGutter]="16">
        <div nz-col [nzSpan]="24">
          <nz-form-item>
            <nz-form-label>Complemento</nz-form-label>
            <nz-form-control>
              <input nz-input 
                     formControlName="addressComplement" 
                     placeholder="Apartamento, bloco, etc. (opcional)" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Status -->
      <nz-divider nzText="Status" nzOrientation="left"></nz-divider>
      
      <div nz-row [nzGutter]="16">
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label>Cliente Ativo</nz-form-label>
            <nz-form-control>
              <nz-switch 
                formControlName="active"
                nzCheckedChildren="Ativo"
                nzUnCheckedChildren="Inativo">
              </nz-switch>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Botões -->
      <div nz-row [nzGutter]="16" style="margin-top: 24px;">
        <div nz-col [nzSpan]="24" style="text-align: right;">
          <button nz-button nzType="default" (click)="closeModal()" [disabled]="isLoading">
            Cancelar
          </button>
          <button nz-button 
                  nzType="primary" 
                  type="submit" 
                  [nzLoading]="isLoading"
                  style="margin-left: 8px;">
            {{ model ? 'Atualizar' : 'Salvar' }}
          </button>
        </div>
      </div>
      
    </form>
  </ng-container>
</nz-modal>