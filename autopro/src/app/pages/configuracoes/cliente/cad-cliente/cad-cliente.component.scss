// Modal customization
::ng-deep .ant-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
    
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

// Form styling
.ant-form-item {
  margin-bottom: 16px;

  .ant-form-item-label {
    padding-bottom: 4px;
    
    label {
      font-weight: 500;
      color: #262626;
    }
  }
}

// Divider customization
::ng-deep .ant-divider {
  margin: 24px 0 16px 0;
  
  .ant-divider-inner-text {
    font-weight: 600;
    color: #1890ff;
  }
}

// Input and select styling
.ant-input,
.ant-select-selector {
  border-radius: 4px;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &:focus,
  &.ant-select-focused .ant-select-selector {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// Error state styling
.ant-input.ant-input-status-error,
.ant-select-status-error .ant-select-selector {
  border-color: #ff4d4f;
  
  &:hover {
    border-color: #ff4d4f;
  }
  
  &:focus {
    border-color: #ff4d4f;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
}

// Switch styling
::ng-deep .ant-switch {
  &.ant-switch-checked {
    background-color: #52c41a;
  }
}

// Date picker styling
::ng-deep .ant-picker {
  width: 100%;
  border-radius: 4px;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &.ant-picker-focused {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// Button styling
.ant-btn {
  border-radius: 4px;
  font-weight: 500;
  
  &.ant-btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
    
    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }
  }
  
  &.ant-btn-default {
    &:hover {
      color: #40a9ff;
      border-color: #40a9ff;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  ::ng-deep .ant-modal {
    width: 95% !important;
    margin: 10px;
    
    .ant-modal-body {
      padding: 16px;
    }
  }
  
  // Stack form items on mobile
  [nz-row] {
    [nz-col] {
      margin-bottom: 8px;
    }
  }
}

// Loading state
.ant-btn-loading {
  pointer-events: none;
}

// Required field indicator
.ant-form-item-required {
  &::before {
    content: '*';
    color: #ff4d4f;
    margin-right: 4px;
  }
}

// Help text styling
.ant-form-item-explain {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
}

// Focus management
.ant-input:focus,
.ant-select-selector:focus,
.ant-picker-input input:focus {
  outline: none;
}

// Disabled state
.ant-input[disabled],
.ant-select-disabled .ant-select-selector {
  background-color: #f5f5f5;
  color: #00000040;
  cursor: not-allowed;
}