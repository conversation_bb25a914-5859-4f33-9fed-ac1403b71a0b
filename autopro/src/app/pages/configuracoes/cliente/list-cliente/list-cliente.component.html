<app-titulo 
    [title]="tituloPagina" 
    [firstLevel]="firstLevel" 
    [secondLevel]="secondLevel"
    [subtitle]="subtitle">
</app-titulo>

<!-- NOVO -->
<div class="page-header">
    <app-cad-cliente 
        [isVisible]="showModalNovo" 
        [model]="clienteModel"
        (modalClose)="onModalClosed()">
    </app-cad-cliente> 
    <div class="btnNovo">
        <button nz-button nzType="primary" (click)="onNew()">+ Novo Cliente</button>
    </div>
</div> 

<div class="page-content">
    <!-- Busca e Filtros -->
    <div class="search-section" style="margin-bottom: 16px; display: flex; gap: 16px; align-items: center;">
        <!-- Campo de busca -->
        <nz-input-group [nzSuffix]="suffixIconButton" style="width: 350px;">
            <input 
                type="text" 
                nz-input 
                placeholder="Buscar por nome, CPF/CNPJ ou cidade..." 
                [(ngModel)]="searchTerm"
                (input)="onSearch()"
                (keyup.enter)="onSearch()">
        </nz-input-group>
        <ng-template #suffixIconButton>
            <button 
                nz-button 
                nzType="text" 
                nzSize="small" 
                (click)="clearSearch()" 
                *ngIf="searchTerm"
                nz-tooltip="Limpar busca">
                <i nz-icon nzType="close" nzTheme="outline"></i>
            </button>
            <i nz-icon nzType="search" nzTheme="outline" *ngIf="!searchTerm"></i>
        </ng-template>

        <!-- Filtro de Status -->
        <nz-select 
            [(ngModel)]="activeFilter" 
            (ngModelChange)="onStatusFilterChange()" 
            nzPlaceHolder="Status"
            style="width: 120px;">
            <nz-option 
                *ngFor="let option of statusOptions" 
                [nzValue]="option.value" 
                [nzLabel]="option.label">
            </nz-option>
        </nz-select>
    </div>

    <!-- Tabela -->
    <nz-table 
        #basicTable 
        [nzLoading]="isLoadingTable" 
        ngSkipHydration="true" 
        [nzData]="lstCliente" 
        [nzBordered]="true" 
        [nzSize]="'middle'"
        [nzFrontPagination]="false"
        [nzShowSizeChanger]="true"
        [nzPageSizeOptions]="pageSizeOptions"
        [nzPageSize]="pageSize"
        [nzPageIndex]="currentPage"
        [nzTotal]="totalItems"
        (nzPageIndexChange)="onPageIndexChange($event)"
        (nzPageSizeChange)="onPageSizeChange($event)">
        <thead>
          <tr>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('id')">
              ID
              <i *ngIf="getSortDirection('id') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('id') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('id') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('name')">
              Nome
              <i *ngIf="getSortDirection('name') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('name') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('name') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th>Tipo</th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('fiscalNumber')">
              CPF/CNPJ
              <i *ngIf="getSortDirection('fiscalNumber') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('fiscalNumber') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('fiscalNumber') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th>Cidade</th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('active')">
              Status
              <i *ngIf="getSortDirection('active') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('active') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('active') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th style="cursor: pointer; user-select: none;" (click)="onSort('createdOn')">
              Data Cadastro
              <i *ngIf="getSortDirection('createdOn') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('createdOn') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
              <i *ngIf="getSortDirection('createdOn') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
            </th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of basicTable.data; trackBy: trackByFn">
            <td>{{ item.id }}</td>
            <td>{{ item.name }}</td>
            <td>
              <nz-tag [nzColor]="item.type === 'PF' ? 'blue' : 'green'">
                {{ item.type === 'PF' ? 'Pessoa Física' : 'Pessoa Jurídica' }}
              </nz-tag>
            </td>
            <td>{{ formatFiscalNumber(item.fiscalNumber, item.type) }}</td>
            <td>{{ item.city?.city || 'N/A' }}</td>
            <td>
              <nz-tag [nzColor]="item.active ? 'green' : 'red'">
                {{ item.active ? 'Ativo' : 'Inativo' }}
              </nz-tag>
            </td>
            <td>{{ formatDate(item.createdOn) }}</td>
            <td>
                <button nz-button nzTooltipTitle="Editar" nzType="text" nz-tooltip="Editar" (click)="onEdit(item)">
                    <i nz-icon nzType="edit" nzTheme="outline"></i>
                </button>
                
                <button 
                    nz-button 
                    [nzTooltipTitle]="item.active ? 'Desativar' : 'Ativar'" 
                    nzType="text" 
                    [nz-tooltip]="item.active ? 'Desativar' : 'Ativar'"
                    (click)="onToggleActive(item)"
                    [style.color]="item.active ? '#ff4d4f' : '#52c41a'">
                    <i nz-icon [nzType]="item.active ? 'stop' : 'play-circle'" nzTheme="outline"></i>
                </button>
                
                <a
                    nz-popconfirm
                    nzPopconfirmTitle="Deseja excluir esse cliente?"
                    nzOkText="Sim"
                    nzCancelText="Cancelar"
                    (nzOnConfirm)="onDelete(item.id)"
                    (nzOnCancel)="cancel()">
                    <button nz-button nzTooltipTitle="Excluir" nzType="text" nz-tooltip="Excluir">
                        <i nz-icon nzType="delete" nzTheme="outline"></i>
                    </button>
                </a>
            </td>
          </tr>
        </tbody>
    </nz-table>
</div>