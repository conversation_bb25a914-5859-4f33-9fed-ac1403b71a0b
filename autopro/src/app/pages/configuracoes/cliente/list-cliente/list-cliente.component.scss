.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .btnNovo {
    display: flex;
    align-items: center;
  }
}

.page-content {
  background: white;
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;

    nz-input-group,
    nz-select {
      width: 100% !important;
    }
  }
}

// Melhorar a aparência das tags
nz-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

// Melhorar aparência dos botões de ação
td button {
  margin-right: 4px;

  &:last-child {
    margin-right: 0;
  }
}

// Responsividade da tabela
@media (max-width: 1200px) {
  ::ng-deep .ant-table {
    .ant-table-container {
      overflow-x: auto;
    }
  }
}

// Loading state
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

// Estados hover para as linhas da tabela
::ng-deep .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

// Personalização das colunas ordenáveis
th[style*="cursor: pointer"] {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }
}

// Melhorar aparência dos ícones de ordenação
.ant-table-thead > tr > th i[nz-icon] {
  transition: color 0.2s ease;
}

// Estilos para tags de status
.status-tag {
  &.active {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }

  &.inactive {
    background-color: #fff2e8;
    border-color: #ffbb96;
    color: #fa8c16;
  }
}

// Estilos para tags de tipo de cliente
.client-type-tag {
  &.pf {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }

  &.pj {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
}