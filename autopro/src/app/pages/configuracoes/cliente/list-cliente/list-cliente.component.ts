import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';
import { TituloComponent } from '../../../../components/titulo/titulo.component';
import { CadClienteComponent } from '../cad-cliente/cad-cliente.component';
import { GlobalService } from '../../../../../services/global.service';
import { ClienteModel, ClienteListModel, PagedRequest, PagedResult } from '../model/cliente.model';

@Component({
  selector: 'app-list-cliente',
  imports: [
    TituloComponent,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    CommonModule,
    NzIconModule,
    CadClienteComponent,
    NzPopconfirmModule,
    NzToolTipModule,
    NzPaginationModule,
    NzInputModule,
    NzSelectModule,
    FormsModule
  ],
  providers: [
    NzMessageService,
    NzNotificationService
  ],
  templateUrl: './list-cliente.component.html',
  styleUrl: './list-cliente.component.scss',
  host: { ngSkipHydration: 'true' },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListClienteComponent {

  tituloPagina: string = "Lista de Clientes";
  firstLevel: string = "Configurações";
  secondLevel: string = "Clientes";
  subtitle: string = "Listagem de Clientes cadastrados no sistema";
  isLoadingNew: boolean = false;
  isLoadingTable: boolean = false;
  lstCliente: ClienteListModel[] = [];
  showModalNovo: boolean = false; 
  clienteModel: ClienteModel | null = null;

  // Paginação
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  pageSizeOptions = [10, 20, 50, 100];

  // Busca e filtro
  searchTerm = '';
  activeFilter: boolean | null = null;
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Ordenação
  sortBy: string = 'name';
  sortDescending: boolean = false;

  // Cache simples
  private cache = new Map<string, { data: PagedResult<ClienteListModel>, timestamp: number }>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutos

  // Opções de filtro de status
  statusOptions = [
    { label: 'Todos', value: null },
    { label: 'Ativos', value: true },
    { label: 'Inativos', value: false }
  ];

  constructor(
    private messageService: NzMessageService,
    private notificationService: NzNotificationService,
    private globalService: GlobalService,
    private cdr: ChangeDetectorRef
  ) {
    // Configurar debounce para busca
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.currentPage = 1; // Reset para primeira página na busca
      this.clearCache(); // Limpar cache na busca
      this.loadClientes();
    });
  }

  ngOnInit() {
    this.loadClientes();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onModalClosed() {
    this.showModalNovo = false;
    this.clearCache(); // Limpar cache após modificação
    this.loadClientes();
  }

  onEdit(item: ClienteListModel) {
    this.globalService.getByGuid('customer', item.guid).subscribe({
      next: (response: any) => {
        this.clienteModel = response;
        this.showModalNovo = true;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao carregar dados do cliente!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  onDelete(id: number) {
    this.globalService.delete(`customer/delete/${id}`).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          'Cliente excluído com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.clearCache(); // Limpar cache após operação
        this.loadClientes();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao excluir cliente!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  onToggleActive(item: ClienteListModel) {
    const newStatus = !item.active;
    
    // Atualizar o objeto cliente
    const updatedClient = { ...item, active: newStatus };
    
    this.globalService.put('customer/update', updatedClient).subscribe({
      next: (response: any) => {
        this.notificationService.success(
          'Sucesso',
          `Cliente ${newStatus ? 'ativado' : 'desativado'} com sucesso!`,
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.success'
          }
        );
        this.clearCache(); // Limpar cache após operação
        this.loadClientes();
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro',
          'Erro ao atualizar status do cliente!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
      }
    });
  }

  onNew() {
    this.showModalNovo = true;
    this.clienteModel = null;
  }

  cancel(): void {
    this.messageService.info('Operação cancelada');
  }

  loadClientes() {
    const cacheKey = `${this.currentPage}_${this.pageSize}_${this.searchTerm}_${this.activeFilter}_${this.sortBy}_${this.sortDescending}`;
    
    // Verificar cache primeiro
    const cached = this.cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      this.lstCliente = cached.data.items;
      this.totalItems = cached.data.totalCount;
      this.cdr.markForCheck();
      return;
    }

    this.isLoadingNew = true;
    this.isLoadingTable = true;
    this.cdr.markForCheck();

    const params = new URLSearchParams();
    params.append('page', this.currentPage.toString());
    params.append('pageSize', this.pageSize.toString());
    
    if (this.searchTerm.trim()) {
      params.append('search', this.searchTerm.trim());
    }
    
    if (this.activeFilter !== null) {
      params.append('active', this.activeFilter.toString());
    }

    this.globalService.get(`customer/paginated?${params.toString()}`).subscribe({
      next: (response: any) => {
        this.lstCliente = response.items;
        this.totalItems = response.totalCount;
        
        // Armazenar no cache
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now()
        });
        
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.notificationService.error(
          'Erro',
          'Erro ao carregar clientes!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice.error'
          }
        );
        this.cdr.markForCheck();
      }
    });
  }

  // Métodos de paginação
  onPageIndexChange(pageIndex: number): void {
    this.currentPage = pageIndex;
    this.loadClientes();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1; // Reset para primeira página
    this.clearCache(); // Limpar cache ao mudar tamanho da página
    this.loadClientes();
  }

  // Método de busca com debounce
  onSearch(): void {
    this.searchSubject.next(this.searchTerm);
  }

  // Limpar busca
  clearSearch(): void {
    this.searchTerm = '';
    this.onSearch();
  }

  // Filtro por status
  onStatusFilterChange(): void {
    this.currentPage = 1;
    this.clearCache();
    this.loadClientes();
  }

  // Limpar cache
  private clearCache(): void {
    this.cache.clear();
  }

  // Método para ordenação
  onSort(field: string): void {
    if (this.sortBy === field) {
      // Se já está ordenando por este campo, inverte a direção
      this.sortDescending = !this.sortDescending;
    } else {
      // Se é um novo campo, define como ascendente
      this.sortBy = field;
      this.sortDescending = false;
    }
    
    // Reset para primeira página ao ordenar
    this.currentPage = 1;
    this.clearCache(); // Limpar cache ao ordenar
    this.loadClientes();
  }

  // Método para obter o tipo de seta de ordenação
  getSortDirection(field: string): 'asc' | 'desc' | null {
    if (this.sortBy !== field) {
      return null;
    }
    return this.sortDescending ? 'desc' : 'asc';
  }

  // TrackBy function para melhor performance do *ngFor
  trackByFn(index: number, item: ClienteListModel): number {
    return item.id;
  }

  // Formatar CPF/CNPJ para exibição
  formatFiscalNumber(fiscalNumber: string, type: string): string {
    if (!fiscalNumber) return '';
    
    const clean = fiscalNumber.replace(/\D/g, '');
    
    if (type === 'PF' && clean.length === 11) {
      return clean.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    } else if (type === 'PJ' && clean.length === 14) {
      return clean.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    }
    
    return fiscalNumber;
  }

  // Formatar data para exibição
  formatDate(date: Date): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('pt-BR');
  }
}