export interface ClienteModel {
  id: number;
  guid: string;
  name: string;
  type: 'PF' | 'PJ';
  fiscalNumber: string;
  birthDate: Date | string | null;
  address: string;
  number: string;
  addressComplement: string;
  districtId: number;
  cityId: number;
  stateId: number;
  active: boolean;
  deleted: boolean;
  createdOn: Date;
  updatedOn: Date;
  
  // Navigation properties
  city?: CityModel;
  district?: DistrictModel;
  state?: StateModel;
}

export interface ClienteListModel {
  id: number;
  guid: string;
  name: string;
  type: 'PF' | 'PJ';
  fiscalNumber: string;
  active: boolean;
  createdOn: Date;
  city?: CityModel;
  state?: StateModel;
}

export interface CityModel {
  id: number;
  city: string;
}

export interface DistrictModel {
  id: number;
  district: string;
  code: string;
}

export interface StateModel {
  id: number;
  state: string;
}

export interface PagedRequest {
  page: number;
  pageSize: number;
  search?: string;
  active?: boolean;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface ClienteFormModel {
  id?: number;
  guid?: string;
  name: string;
  type: 'PF' | 'PJ';
  fiscalNumber: string;
  birthDate?: Date | string | null;
  address: string;
  number: string;
  addressComplement?: string;
  districtId: number;
  cityId: number;
  stateId: number;
  active: boolean;
}