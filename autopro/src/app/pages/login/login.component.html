<div class="login-container" *ngIf="isLogin">
    <nz-card class="login-card">
      <div class="login-header">
            <img src="assets/logo_modificada.png" alt="Logo" width="150" height="50">
      </div>
  
      <form nz-form [formGroup]="loginForm" (ngSubmit)="onSubmit()" skypHydration="true">
        <nz-form-item>
          <nz-form-label [nzSpan]="6" nzFor="username">Login</nz-form-label>
          <nz-form-control [nzSpan]="18">
            <input nz-input formControlName="username" id="username" placeholder="Enter your username" />
          </nz-form-control>
        </nz-form-item>
  
        <nz-form-item>
          <nz-form-label [nzSpan]="6" nzFor="password">Senha</nz-form-label>
          <nz-form-control [nzSpan]="18">
            <input nz-input type="password" formControlName="password" id="password" placeholder="Enter your password" />
          </nz-form-control>
        </nz-form-item>
  
        <div class="login-actions">
          <a class="forgot-link" (click)="onForgotPassword()">Esqueceu a senha?</a>
        </div>
  
        <button type="submit" nz-button nzType="primary" class="btnLogar" nzblock>Login</button>
      </form>
    </nz-card>
  </div>

  <div class="login-container" *ngIf="isForgotPassword">
    <nz-card class="login-card">
      <div class="login-header">
        <img src="assets/logo_modificada.png" alt="Logo" width="150" height="50">
        <h6>Recuperar Senha</h6>
      </div>
      <form nz-form  skypHydration="true" nzLayout="vertical">
        <nz-form-item>
          <nz-form-label nzFor="username">Informe seu email {{email}}</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <input nz-input name="email" id="email" [(ngModel)]="email" placeholder="Informe seu email" />
          </nz-form-control>
        </nz-form-item>
        <button type="submit" nz-button nzType="primary" (click)="forgotPassword()" class="btnLogar" nzblock>Recuperar Senha</button>
      </form>
    </nz-card>
  </div>


