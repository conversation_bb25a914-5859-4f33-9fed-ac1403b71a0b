import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { GlobalService } from '../../../services/global.service';
import { LoginModel, LoginResponse } from './model/login.model';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
  selector: 'app-login',
  imports: [
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCardModule,
    RouterModule,
    CommonModule,
    FormsModule
  ],
  providers: [NzNotificationService],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  loginForm!: FormGroup;
  isAuthenticated: boolean = false;
  isForgotPassword: boolean = false;
  isLogin: boolean = true;
  email:string = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private service: GlobalService,
    private notification : NzNotificationService
  ) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      username: new FormControl('', Validators.required),
      password: new FormControl('', Validators.required),
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      const loginDTO = {
        email: this.loginForm.value.username,
        password: this.loginForm.value.password,
      };

      this.service.post('login/logar', loginDTO).subscribe({
        next: (response: LoginResponse) => {  
          let loginModel : LoginModel = {
            email: response.email,
            usergroupGuid: response.usergroupId,
            token: response.token,
            guid: response.guid,
          };
          localStorage.setItem('objLogin', JSON.stringify(loginModel));
        },
        error: (error: Error) => {
          this.notification.error(
            'Erro',
            'Erro ao efetuar login!',
            {
              nzDuration:7000,
              nzClass: 'ant-notification-notice.error'  
            })
        },
        complete: () => {
          this.router.navigateByUrl('/principal');
        },
      });
    } else {
      Object.values(this.loginForm.controls).forEach((control) => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  onForgotPassword(): void {
    this.isForgotPassword = true;
    this.isLogin = false;
  }

  onSignUp(): void {
    // TODO: Implementar cadastro
  }

  forgotPassword() : void {
    let email : string = this.email;
    // TODO: Implementar recuperação de senha
    this.isForgotPassword = false;
    this.isLogin = true;
  }
}
