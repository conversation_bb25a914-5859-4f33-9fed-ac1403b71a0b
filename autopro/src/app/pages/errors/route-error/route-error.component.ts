import { Component } from '@angular/core';
import { Router } from '@angular/router';
import {  NzButtonModule } from 'ng-zorro-antd/button';
import { NzResultModule } from 'ng-zorro-antd/result';

@Component({
  selector: 'app-route-error',
  imports: [
    NzResultModule,
    NzButtonModule
  ],
  templateUrl: './route-error.component.html',
  styleUrl: './route-error.component.scss'
})
export class RouteErrorComponent {

  constructor(
    private route: Router
  ) {

  }

  goHome() : void {
    this.route.navigateByUrl('/login');
  }
}
