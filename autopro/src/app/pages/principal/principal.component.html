<nz-layout class="app-layout">
    <nz-sider class="menu-sidebar"
      nzCollapsible
      nzWidth="256px"
      nzBreakpoint="md"
      [(nzCollapsed)]="isCollapsed"
      [nzTrigger]="null"
    >
      <div class="sidebar-logo">
        <a href="https://ng.ant.design/" target="_blank">
          <!-- <img src="https://ng.ant.design/assets/img/logo.svg" alt="logo"> -->
          <h1>AutoVPro</h1>
        </a>
      </div>
      <ul nz-menu nzTheme="dark" nzMode="inline" [nzInlineCollapsed]="isCollapsed">
        <li nz-submenu nzOpen nzTitle="Configurações" nzIcon="setting">
          <ul>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/welcome">Pagina Modelo</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/user-group-list">Grupos de Usuário</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/user-list">Usuário</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/permissions">Permissionamento</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a>Log de Operações</a>
            </li>
          </ul>
        </li>
        <li nz-submenu nzOpen nzTitle="Cadastros" nzIcon="form">
          <ul>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/service-list">Serviços</a>
            </li>

            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/service-radius">Raio de Atendimento</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/item">Itens Laudo</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/sub-item">Sub-itens Laudo</a>
            </li>
          </ul>
        </li>
        <li nz-submenu nzOpen nzTitle="Operações" nzIcon="form">
          <ul>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/cautelaristafee-list">Comissão Cautelaristas</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/cliente-list">Clientes</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/cautelarista-list">Cautelaristas</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a routerLink="/principal/attendance-list">Gestão de Solicitações</a>
            </li>
          </ul>
        </li>
        <li nz-submenu nzOpen nzTitle="Relatórios" nzIcon="form">
          <ul>
            <li nz-menu-item nzMatchRouter>
              <a>Contas a Receber</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a>Contas a Pagar</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a>Vistorias Pendentes</a>
            </li>
            <li nz-menu-item nzMatchRouter>
              <a>Pagamentos Cautelaristas</a>
            </li>
          </ul>
        </li>
      </ul>
    </nz-sider>
    <nz-layout>
      <nz-header>
        <div class="app-header">
          <div nz-flex [nzVertical]="false">
          <div class="flex-item">
            <span class="header-trigger" (click)="isCollapsed = !isCollapsed">
              <nz-icon class="trigger" [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'" />
            </span>
          </div>
          <div class="flex-item even">Cautelares Recusados  <nz-badge nzStandalone [nzCount]="99" [nzStyle]="{ backgroundColor: '#DC143C' }"></nz-badge></div>
          <div class="flex-item">Cautelares em Andamento <nz-badge nzStandalone [nzCount]="50" [nzStyle]="{ backgroundColor: '#F4A460' }"></nz-badge></div>
          <div class="flex-item even">Cautelares Finalizados <nz-badge nzStandalone [nzCount]="50" [nzStyle]="{ backgroundColor: '#52c41a' }"></nz-badge></div> 
          <div class="flex-item even"> <a nz-dropdown [nzDropdownMenu]="menu">
            {{loggerUser}} <nz-icon nzType="user" nzTheme="outline" />
            <nz-icon nzType="down" />
          </a>
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu nzSelectable>
              <li nz-menu-item>Meu Perfil</li>
              <li nz-menu-item>Alterar Senha</li>
              <li nz-menu-item>
                <a [routerLink]="sair">Sair</a>
            </li>
            </ul>
          </nz-dropdown-menu></div> 
        </div>
        </div>
      
      </nz-header>
      <nz-content>
        <div class="inner-content">
          <router-outlet></router-outlet>
        </div>
      </nz-content>
    </nz-layout>
  </nz-layout>
  
