import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { RouterLink, RouterOutlet } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { SharedModule } from '../../modules/shared/shared.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { LoginModel } from '../login/model/login.model';
@Component({
  selector: 'app-principal',
  imports: [
    RouterLink, 
    RouterOutlet,
    NzIconModule, 
    NzLayoutModule, 
    NzMenuModule, 
    NzFormModule, 
    SharedModule, 
    NzBadgeModule, 
    NzFlexModule,
    NzBadgeModule,
    NzDropDownModule
  ],
  templateUrl: './principal.component.html',
  styleUrl: './principal.component.scss'
})
export class PrincipalComponent {
  isCollapsed: boolean = false;
  loggerUser:string = "";
  sair : string = '/sair'

  
  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      let objLogin : LoginModel = JSON.parse(localStorage.getItem('objLogin') || '{}');
      this.loggerUser = objLogin.email;
    } else {
      this.loggerUser = 'Admin';
    }
  }
  
  

}
