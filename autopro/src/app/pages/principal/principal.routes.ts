import { PrincipalComponent } from "./principal.component";

export const PRINCIPAL_ROUTES = [
    { path: '', 
      component: PrincipalComponent,
      children: [
        { path: 'welcome', loadChildren: () => import('../welcome/welcome.routes').then(m => m.WELCOME_ROUTES) },
        { path: 'user-list', loadChildren: () => import('../user/user-component/user.routes').then(m => m.USERS_ROUTES) },
        { path: 'user-group-list', loadChildren: () => import('../configuracoes/user-group/user-group.routes').then(m => m.USERGROUP_ROUTES) },
        { path: 'service-list', loadChildren: () => import('../configuracoes/service/service.routes').then(m => m.SERVICE_ROUTES) },
        { path: 'cautelarista-list', loadChildren: () => import('../configuracoes/cautelarista/cautelarista.routes').then(m => m.CAUTELARISTA_ROUTES) },
        { path: 'cliente-list', loadChildren: () => import('../configuracoes/cliente/cliente.routes').then(m => m.CLIENTE_ROUTES) },
        { path: 'cautelaristafee-list', loadChildren: () => import('../configuracoes/cauteralist-fee/cautelarista-fee.routes').then(m => m.CAUTELARISTA_ROUTES) },
        { path: 'permissions', loadChildren: () => import('../system/permissions/permissions.routes').then(m => m.PERMISSIONS_ROUTES) },
        { path: 'service-radius', loadChildren: () => import('../configuracoes/service-radius/service-radius.routes').then(m => m.SERVICE_RADIUS_ROUTE)},
        { path: 'attendance-list', loadChildren: () => import('../configuracoes/attendance/attendance.routes').then(m => m.ATTENDANCE_ROUTES) },
        { path: 'item', loadChildren: () => import('../configuracoes/itemcomponent/itemComponent.route').then(m => m.ITEM_ROUTES) },
        { path: 'sub-item', loadChildren: () => import('../configuracoes/sub-item/sub-item.routes').then(m => m.SUBITEM_ROUTES) }
      ] 
    }
]
