<!-- Debug: isVisible = {{ isVisible }} -->
<nz-modal
    ngSkipHydration="true"
      nzDraggable
      nzCentered
      [(nzVisible)]="isVisible"
      [nzTitle]="titlePage"
      (nzOnCancel)="handleCancel()"
      nzWidth="820px"
    >
      <ng-container *nzModalContent>
          <form nz-form [formGroup]="form" skipHydration="true">
            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="email" nzRequired>Email</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Email é obrigatório e deve ser válido">
                <input type="hidden" formControlName="id" id="id" />
                <input nz-input type="email" formControlName="email" id="email" placeholder="<EMAIL>" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item *ngIf="form.controls['id'].value == 0">
              <nz-form-label [nzSpan]="6" nzFor="password" nzRequired>Senha</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Senha é obrigatória e deve ter no mínimo 6 caracteres">
                <input nz-input type="password" formControlName="password" id="password" placeholder="Mínimo 6 caracteres" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="userGroupId" nzRequired>Grupo de Usuário</nz-form-label>
              <nz-form-control [nzSpan]="14" nzErrorTip="Grupo de usuário é obrigatório">
                <nz-select formControlName="userGroupId" id="userGroupId" nzPlaceHolder="Selecione um grupo">
                  <nz-option *ngFor="let group of userGroups" [nzValue]="group.id" [nzLabel]="group.nomeGrupoUser"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          
            <nz-form-item>
              <nz-form-label [nzSpan]="6" nzFor="active">Ativo</nz-form-label>
              <nz-form-control [nzSpan]="14">
                <nz-switch formControlName="active"></nz-switch>
              </nz-form-control>
            </nz-form-item>
          </form>
      </ng-container>
      <div *nzModalFooter>
        <button nz-button (click)="handleCancel()">Cancelar</button>
        <button nz-button nzType="primary" (click)="save()">Salvar</button>
      </div>
    </nz-modal>