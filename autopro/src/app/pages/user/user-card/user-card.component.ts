import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { GlobalService } from '../../../../services/global.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { CommonModule } from '@angular/common';
import { UserGroupModel } from '../../configuracoes/user-group/model/usergroup.model';

@Component({
  selector: 'app-user-card',
  imports: [
    NzModalModule,
    NzFormModule,
    NzButtonModule,
    NzInputModule,
    NzSwitchModule,
    NzSelectModule,
    ReactiveFormsModule,
    CommonModule
  ],
  templateUrl: './user-card.component.html',
  styleUrl: './user-card.component.scss'
})
export class UserCardComponent {

  private _isVisible: boolean = false;
  
  @Input() 
  get isVisible(): boolean {
    return this._isVisible;
  }
  set isVisible(value: boolean) {
    this._isVisible = value;
  }
  
  @Output() modalClose = new EventEmitter();
  @Input() model: any;
  titlePage: string = "Usuário";
  userGroups: UserGroupModel[] = [];

  form!: FormGroup;

  constructor(
    private fb: FormBuilder,
    private globalService: GlobalService,
    private notificationService: NzNotificationService
  ) {
 
  }

   ngOnInit() {
    this.loadUserGroups();
  }

  ngOnChanges() {
    
    this.initializeForm();
    
    if (this.model) {
      const modelData = { ...this.model };
      
      // Garante que sempre tenha um GUID válido
      if (!modelData.guid || modelData.guid === '00000000-0000-0000-0000-000000000000') {
        modelData.guid = this.generateGuid();
      }
      
      this.form.patchValue(modelData);
      
      if (this.model.id > 0) {
        this.form.controls['password'].clearValidators();
        this.form.controls['password'].updateValueAndValidity();
      }
    } else {
      // Quando não há modelo (novo usuário), garante que o formulário seja limpo
      this.form.reset();
      this.initializeForm();
    }
  }

  initializeForm() {
    const newGuid = this.generateGuid();
    
    this.form = this.fb.group({
      id: [0],
      guid: [newGuid, [Validators.required]],
      email: [null, [Validators.required, Validators.email]],
      userGroupId: [null, [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      active: [true],
      deleted: [false]
    });
    
  }

  save() {
    if (this.form.valid && this.form.controls['id'].value == 0 ) {
      this.UserSave();
    }
    else if(this.form.valid && this.form.controls['id'].value > 0){
      this.UserUpdate();
    }
    else {
      Object.values(this.form.controls).forEach(control => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }

  

  handleCancel() {
    this.isVisible = false;
    this.form.reset();
    this.modalClose.emit(false);
  }

  private generateGuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  public UserSave() {
    const userData = {
      ...this.form.value,
      guid: this.form.value.guid || this.generateGuid()
    };
    
    this.globalService.post('user/add', userData).subscribe({
      next: (response: any) => {
      },
      error: (error: any) => {
        this.notificationService.error(
          'Erro ao criar usuário',
          `Erro: ${error.error?.message || error.message}`,
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice-error'
          }
        );
      },
      complete: () => {
        this.notificationService.success(
          'Criação',
          'Usuário criado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice-success'
          }
        );
        this.modalClose.emit(true);
        this.isVisible = false;
        this.form.reset();
        this.initializeForm();
      }
    });
  }       


  public UserUpdate() {
    const updateData = { ...this.form.value };
    
    // Garante GUID válido na atualização
    if (!updateData.guid || updateData.guid === '00000000-0000-0000-0000-000000000000') {
      updateData.guid = this.generateGuid();
    }
    
    if (!updateData.password || updateData.password.trim() === '') {
      delete updateData.password;
    }
    
    this.globalService.put('user/update', updateData).subscribe({
      error: (error: any) => {
        this.notificationService.error(
          'Erro ao alterar usuário',
          `Erro: ${error.error?.message || error.message}`,
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice-error'
          }
        );
      },
      complete: () => {
        this.notificationService.success(
          'Alteração',
          'Usuário alterado com sucesso!',
          {
            nzDuration: 3000,
            nzClass: 'ant-notification-notice-success'
          }
        );
        this.modalClose.emit(true);
        this.isVisible = false;
        this.form.reset();
        this.initializeForm();
      }
    });
  }

  private loadUserGroups() {
    this.globalService.get('usergroup/list').subscribe({
      next: (response: any) => {
        this.userGroups = response;
      },
      error: (error: any) => {
      }
    });
  }

}
