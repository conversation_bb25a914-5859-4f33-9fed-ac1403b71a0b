<app-titulo [title]="titlePage" [firstLevel]="firstLevel" [secondLevel]="secondLevel" [subtitle]="subtitle">
</app-titulo> 

<div class="page-header">
    <app-user-card 
        [isVisible]="showModalNovo" 
        [model]="userModel"
        (modalClose)="onModalClosed()">
    </app-user-card> 
    <div class="btnNovo">
        <button nz-button nzType="primary" (click)="onNew()">+ Novo</button>
    </div>
 </div> 
<div class="page-content">
  <!-- Busca -->
  <div class="search-section" style="margin-bottom: 16px;">
    <nz-input-group [nzSuffix]="suffixIconButton" style="width: 300px;">
      <input 
        type="text" 
        nz-input 
        placeholder="Buscar por email ou grupo..." 
        [(ngModel)]="searchTerm"
        (input)="onSearch()"
        (keyup.enter)="onSearch()">
    </nz-input-group>
    <ng-template #suffixIconButton>
      <button 
        nz-button 
        nzType="text" 
        nzSize="small" 
        (click)="clearSearch()" 
        *ngIf="searchTerm"
        nz-tooltip="Limpar busca">
        <i nz-icon nzType="close" nzTheme="outline"></i>
      </button>
      <i nz-icon nzType="search" nzTheme="outline" *ngIf="!searchTerm"></i>
    </ng-template>
  </div>

  <!-- Tabela -->
  <nz-table 
    #basicTable 
    [nzLoading]="isLoadingTable" 
    ngSkipHydration="true" 
    [nzData]="userList" 
    [nzBordered]="true"
    [nzSize]="'middle'"
    [nzFrontPagination]="false"
    [nzShowSizeChanger]="true"
    [nzPageSizeOptions]="pageSizeOptions"
    [nzPageSize]="pageSize"
    [nzPageIndex]="currentPage"
    [nzTotal]="totalItems"
    (nzPageIndexChange)="onPageIndexChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    <thead>
      <tr>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('id')">
          ID
          <i *ngIf="getSortDirection('id') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('id') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('id') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th>Guid</th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('email')">
          Email
          <i *ngIf="getSortDirection('email') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('email') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('email') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('usergroup')">
          Grupo de Usuário
          <i *ngIf="getSortDirection('usergroup') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('usergroup') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('usergroup') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('active')">
          Ativo
          <i *ngIf="getSortDirection('active') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('active') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('active') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th style="cursor: pointer; user-select: none;" (click)="onSort('createdon')">
          Data de Criação
          <i *ngIf="getSortDirection('createdon') === 'asc'" nz-icon nzType="caret-up" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('createdon') === 'desc'" nz-icon nzType="caret-down" style="margin-left: 4px; color: #1890ff;"></i>
          <i *ngIf="getSortDirection('createdon') === null" nz-icon nzType="swap" style="margin-left: 4px; color: #d9d9d9;"></i>
        </th>
        <th>Ações</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of basicTable.data; trackBy: trackByFn">
        <td>{{ item.id }}</td>
        <td>{{ item.guid || 'Não definido' }}</td>
        <td>{{ item.email }}</td>
        <td>{{ item.userGroupName }}</td>
        <td>
          <nz-tag [nzColor]="item.active ? 'green' : 'red'">
            {{ item.active? 'Sim' : 'Não' }}
          </nz-tag>
        </td>
        <td>{{ item.createdOn | date:'dd/MM/yyyy HH:mm' }}</td>
        <td>
          <button nz-button nzTooltipTitle="Editar" nzType="text" nz-tooltip="Editar" (click)="onEdit(item)">
            <i nz-icon nzType="edit" nzTheme="outline"></i>
          </button>
              <a
      nz-popconfirm
      nzPopconfirmTitle="Deseja excluir esse registro?"
      nzOkText="ok"
      nzDanger
      nzCancelText="cancel"
      (nzOnConfirm)="confirm(item.id)"
      (nzOnCancel)="cancel()"
      nzTooltipTitle="Excluir Registro"
    >
      <i nz-icon nzType="delete" nzTheme="outline"></i>
    </a>
        </td>
      </tr>
    </tbody>
  </nz-table>

</div>