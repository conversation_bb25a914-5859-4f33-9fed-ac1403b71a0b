import { UserModel, UserListDTO, PagedRequest, PagedResult } from '../../configuracoes/user-group/model/user.model';
import { UserGroupModel } from '../../configuracoes/user-group/model/usergroup.model';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { TituloComponent } from '../../../components/titulo/titulo.component';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { GlobalService } from '../../../../services/global.service';
import { CommonModule } from '@angular/common';
import { UserCardComponent } from '../user-card/user-card.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { ToastService } from '../../../shared/toast/toast.service';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-users',
  imports: [
    TituloComponent,
    FormsModule, 
    NzButtonModule, 
    NzInputModule, 
    NzIconModule,
    NzGridModule,
    NzDescriptionsModule,
    NzTableModule,
    NzTagModule,
    NzPaginationModule,
    CommonModule,
    UserCardComponent,
    NzPopconfirmModule,
    NzToolTipModule
  ],
  templateUrl: './user.component.html',
  styleUrl: './user.component.scss',
  host: { ngSkipHydration: 'true' },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserComponent {

  titlePage: string = "Lista de Usuário";
  firstLevel: string = "Configurações";
  secondLevel: string = "Usuário";
  subtitle: string = "Listagem de Usuário";
  isLoadingNew: boolean = false;
  isLoadingTable: boolean = false;
  showModalNovo: boolean = false; 
  userList: UserListDTO[] = [];
  userModel: UserModel | null = null;
  userGroups: UserGroupModel[] = [];

  // Paginação
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  pageSizeOptions = [10, 20, 50, 100];

  // Busca e filtro
  searchTerm = '';
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Ordenação
  sortBy: string = 'email';
  sortDescending: boolean = false;

  // Cache simples
  private cache = new Map<string, { data: PagedResult<UserListDTO>, timestamp: number }>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutos

  constructor(
    private globalService: GlobalService,
    private modal: NzModalService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef
  ) {
    // Configurar debounce para busca
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.currentPage = 1; // Reset para primeira página na busca
      this.clearCache(); // Limpar cache na busca
      this.loadUsersPaged();
    });
  }

  ngOnInit() {
    this.loadUserGroups();
    this.loadUsersPaged();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }


  loadUsersPaged() {
    const cacheKey = `${this.currentPage}_${this.pageSize}_${this.searchTerm}_${this.sortBy}_${this.sortDescending}`;
    
    // Verificar cache primeiro
    const cached = this.cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      this.userList = cached.data.items;
      this.totalItems = cached.data.totalCount;
      this.cdr.markForCheck();
      return;
    }

    this.isLoadingNew = true;
    this.isLoadingTable = true;
    this.cdr.markForCheck();

    const request: PagedRequest = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm.trim() || undefined,
      sortBy: this.sortBy,
      sortDescending: this.sortDescending
    };

    const params = new URLSearchParams();
    params.append('pageNumber', request.pageNumber.toString());
    params.append('pageSize', request.pageSize.toString());
    if (request.searchTerm) {
      params.append('searchTerm', request.searchTerm);
    }
    if (request.sortBy) {
      params.append('sortBy', request.sortBy);
    }
    params.append('sortDescending', request.sortDescending.toString());

    this.globalService.get(`user/paged?${params.toString()}`).subscribe({
      next: (response: any) => {
        const pagedResult = response as PagedResult<UserListDTO>;
        this.userList = pagedResult.items.map(user => ({
          ...user,
          userGroupName: user.userGroupName || this.getUserGroupName(user.userGroupId)
        }));
        this.totalItems = pagedResult.totalCount;
        
        // Debug log para verificar se os GUIDs estão chegando
        this.userList.forEach((item, index) => {
        });
        
        // Armazenar no cache
        this.cache.set(cacheKey, {
          data: pagedResult,
          timestamp: Date.now()
        });
        
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.cdr.markForCheck();
      },
      error: (error: any) => {
        this.isLoadingNew = false;
        this.isLoadingTable = false;
        this.toastService.error(
          'Erro ao carregar usuários. Tente novamente.',
          'Erro',
          { duration: 5000 }
        );
        this.cdr.markForCheck();
      }
    });
  }


  // Método de busca com debounce
  onSearch(): void {
    this.searchSubject.next(this.searchTerm);
  }

  // Limpar busca
  clearSearch(): void {
    this.searchTerm = '';
    this.onSearch();
  }

  // Métodos de paginação
  onPageIndexChange(pageIndex: number): void {
    this.currentPage = pageIndex;
    this.loadUsersPaged();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1; // Reset para primeira página
    this.clearCache(); // Limpar cache ao mudar tamanho da página
    this.loadUsersPaged();
  }

  // Limpar cache
  private clearCache(): void {
    this.cache.clear();
  }

  private loadUserGroups() {
    this.globalService.get('usergroup/list').subscribe({
      next: (response: any) => {
        this.userGroups = response;
      },
      error: (error: any) => {
      }
    });
  }

  private getUserGroupName(userGroupId: number): string {
    const group = this.userGroups.find(g => g.id === userGroupId);
    return group ? group.nomeGrupoUser : 'Não definido';
  }

  onDelete(id: number): void {
    this.globalService.delete(`user/${id}`).subscribe({
      next: (response: any) => {
        this.toastService.success(
          'Usuário excluído com sucesso!',
          'Operação Concluída'
        );
        this.clearCache(); // Limpar cache após operação
        this.loadUsersPaged();
      },
      error: (error: any) => {
        this.toastService.error(
          'Erro ao excluir usuário. Tente novamente.',
          'Erro na Operação',
          { duration: 5000 }
        );
      }
    });
  }

  cancel(): void {
    this.toastService.info('Operação cancelada', 'Cancelado');
  }

  confirm(id: number): void {
    this.onDelete(id);
  }

  onEdit(item: UserListDTO): void {
    // Debug logs para verificar o problema
    
    // TESTE SIMPLES: Definir dados mock e abrir modal diretamente
    this.userModel = {
      id: item.id,
      guid: item.guid || 'test-guid',
      email: item.email,
      password: '',
      userGroupId: item.userGroupId,
      active: item.active,
      deleted: false
    };
    
    this.showModalNovo = true;
    this.cdr.markForCheck();
    
    // TODO: Remover este teste e implementar a busca real após confirmar que o modal abre
  }

  onNew(): void {
    this.showModalNovo = true;
    this.userModel = null;
    this.cdr.markForCheck();
  }
  
  onModalClosed(): void {
    this.showModalNovo = false;
    this.userModel = null;
    this.clearCache(); // Limpar cache após modificação
    this.loadUsersPaged();
    this.cdr.markForCheck();
  }

  // Método para ordenação
  onSort(field: string): void {
    if (this.sortBy === field) {
      // Se já está ordenando por este campo, inverte a direção
      this.sortDescending = !this.sortDescending;
    } else {
      // Se é um novo campo, define como ascendente
      this.sortBy = field;
      this.sortDescending = false;
    }
    
    // Reset para primeira página ao ordenar
    this.currentPage = 1;
    this.clearCache(); // Limpar cache ao ordenar
    this.loadUsersPaged();
  }

  // Método para obter o tipo de seta de ordenação
  getSortDirection(field: string): 'asc' | 'desc' | null {
    if (this.sortBy !== field) {
      return null;
    }
    return this.sortDescending ? 'desc' : 'asc';
  }

  // TrackBy function para melhor performance do *ngFor
  trackByFn(index: number, item: UserListDTO): number {
    return item.id;
  }
}

 


  
