import { Component } from '@angular/core';
import { UserGroupModel } from '../../configuracoes/user-group/model/usergroup.model';
import { GlobalService } from '../../../../services/global.service';
import { TituloComponent } from '../../../components/titulo/titulo.component';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzFormModule } from 'ng-zorro-antd/form';
import { ScreenModel } from '../screens/screen.model';
import { NzCardModule } from 'ng-zorro-antd/card';
import { ScreenOptionModel } from '../screens/screenoptions.model';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { FilterByScreenIdPipe } from '../screens/screen.pipe';
import {  NzDividerModule } from 'ng-zorro-antd/divider';
import { NzAlertModule } from 'ng-zorro-antd/alert';

@Component({
  selector: 'app-permissions',
  imports: [
    TituloComponent,
    NzSelectModule,
    NzButtonModule,
    ReactiveFormsModule,
    CommonModule,
    NzFormModule,
    FormsModule,
    NzCardModule,
    NzSwitchModule,
    FilterByScreenIdPipe,
    NzDividerModule,
    NzAlertModule
  ],
  templateUrl: './permissions.component.html',
  styleUrl: './permissions.component.scss',
})
export class PermissionsComponent {
  tituloPagina: string = 'Permissões';
  firstLevel: string = 'Sistema';
  secondLevel: string = 'Permissões';
  subtitle: string = 'Listagem de Permissões';
  screenModelOptions: any[] = []; // Declare screenModelOptions property
  userGroupModel: UserGroupModel[] = [];
  screenModel: ScreenModel[] = [];
  screenModelAll: ScreenModel[] = [];
  screenModelFiltered: ScreenModel[] = [];
  screenOptionModel: ScreenOptionModel[] = [];
  selectedGroup: any | null = null;
  selectedScreen: any | null = null;
  isVisibleNoPermission: boolean = false;

  constructor(private globalService: GlobalService) {}

  async ngOnInit(): Promise<void> {
    await this.getUserGroups();
    await this.getScreens();
  }

  async getUserGroups(): Promise<UserGroupModel[]> {
    return new Promise((resolve, reject) => {
      this.globalService.get('usergroup/list').subscribe({
        next: (response: any) => {
          this.userGroupModel = response;
          resolve(this.userGroupModel);
        },
        error: (error: any) => {
          reject(error); // Reject the promise with the error
        },
        complete: () => {
        },
      });
    });
  }

  async getScreens(): Promise<ScreenModel[]> {
    return new Promise(async (resolve, reject) => {
      this.globalService.get('screen').subscribe({
        next: (response: any) => {
          this.screenModel = response;
          this.screenModelAll = response; // Store the original screen model
          resolve(this.screenModel);

          // Fetch screen options for each screen)
        },
        error: (error: any) => {
          reject(error); // Reject the promise with the error
        },
        complete: () => {
        },
      });
    });
  }

  async getScreenOptionById(screenId: number | null) : Promise<ScreenOptionModel[]> {
    let endpoint = 'screenoptions/list/';

    if (screenId !== null) 
      endpoint += `?screenId=${screenId}`;

    await this.globalService.get(endpoint).subscribe({ 
      next: (response: any) => {
        this.screenOptionModel = response;
        if(this.screenModelOptions.length = 0)
          this.isVisibleNoPermission = true; 
        else
          this.isVisibleNoPermission = false;
        
      },
      error: (error: any) => {
      },
      complete: () => {
      }
    }
    );
    return Promise.resolve(this.screenOptionModel);
  }

  async getPermissionByScreenGroup(screenId: number, userGroupId: number): Promise<any[]> {
      return new Promise((resolve, reject) => {
        this.globalService.get(`screenoptionpermission/list-permissions/${screenId}/${userGroupId}`).subscribe({
          next: (response: any) => {
           if(response.length > 0) {
            debugger;
            this.screenOptionModel.forEach((option, index) => {
              debugger;
              let filter = response.filter((item: any) => item.screenOptionId === option.id);
              if(filter.length > 0) {
                option.allow = filter[0].allow;
              }
              else {
                option.allow = false; // Default to true if no permission found
              }
            })
          }
          else {
            this.isVisibleNoPermission = true;
          }
            resolve(response); // Resolve the promise with the permissions
          },
          error: (error: any) => {
            reject(error); // Reject the promise with the error
          },
          complete: () => {
          },
        });
      });
    }

async onGroupChange(groupId: number) {
  this.selectedGroup = groupId;
  // Initialize with an empty screen option model
}

async onScreenChange(screenId: number) {
  this.selectedScreen = screenId;
  this.screenModelAll = this.screenModel; // Store the original screen model
  let filterScreen = this.screenModel.filter(screen => screen.id === screenId);
  this.screenModelFiltered = filterScreen;
  
  

  await this.getScreenOptionById(null); 
  
  if (this.selectedGroup && this.selectedScreen) {
        await this.getPermissionByScreenGroup(this.selectedScreen, this.selectedGroup).then(
      (permissions: any[]) => {
       
        this.screenModelOptions.forEach((option) => {
          option.allow = true;
        });
      },
      (error) => {
      }
    );
  } else {
  }
}

onOptionChange(option: ScreenOptionModel) {
  let screenId = this.selectedScreen;
  let userGroupId = this.selectedGroup;
  let allow = !option.allow;

  let obj: any = {
    screenOptionId: option.id,
    userGroupId: userGroupId,
    allow: allow
  };

  this.globalService.post('screenoptionpermission/add', obj).subscribe({
    next: (response: any) => {
    },
    error: (error: any) => {
    },
    complete: () => {
    }
  });


}


  onSubmit() {}

  onClear() {}
}
