<app-titulo 
    [title]="tituloPagina" 
    [firstLevel]="firstLevel" 
    [secondLevel]="secondLevel"
    [subtitle]="subtitle"
    >
</app-titulo>
<div class="container">
    <form nz-form [nzLayout]="'inline'" skypHydration="true">
      <nz-form-item>
        <nz-form-label>Grupo</nz-form-label>
        <nz-form-control>
          <nz-select
            name="grupo"
            nzShowSearch
            nzAllowClear
            nzPlaceHolder="Selecione o Grupo"
            [(ngModel)]="selectedGroup"
            (ngModelChange)="onGroupChange($event)"
          >
            <nz-option
              *ngFor="let group of userGroupModel"
              [nzLabel]="group.nomeGrupoUser"
              [nzValue]="group.id"
            ></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
  
      <nz-form-item>
        <nz-form-label>Tela:</nz-form-label>
        <nz-form-control>
          <nz-select
            name="tela"
            nzShowSearch
            nzAllowClear
            nzPlaceHolder="Selecione a Tela"
            [(ngModel)]="selectedScreen"
            (ngModelChange)="onScreenChange($event)"
          >
            <nz-option
              *ngFor="let tela of screenModelAll"
              [nzLabel]="tela.screenName"
              [nzValue]="tela.id"
            ></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </form>
  </div>

  <div *ngIf="selectedGroup && selectedScreen" class="container">
    <nz-divider></nz-divider>
    <div *ngFor="let screen of screenModelFiltered;">
        <nz-card
            [nzTitle]="screen.screenName"
            [nzBordered]="true"
            class="card"
        >
        <ul>
            <li *ngFor="let option of screenOptionModel | filterByScreenId: screen.id">
              <span style="display: inline-block; width: 200px;">{{ option.option }}</span>
              <nz-switch 
                [(ngModel)]="option.allow"
              style="display: inline-block;"
              (click)="onOptionChange(option)"
              >
              </nz-switch>
            </li>
          </ul>

          <div class="warning" *ngIf="isVisibleNoPermission">
            <nz-alert
            nzType="warning"
            nzMessage="Atenção"
            nzDescription="Não foram encontradas permissões para o grupo e a tela selecionados."
            nzShowIcon
          ></nz-alert>
      </div>
        </nz-card>
    </div>

 
  
