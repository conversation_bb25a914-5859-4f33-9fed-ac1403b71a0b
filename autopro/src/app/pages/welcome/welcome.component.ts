import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup,Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { SharedModule, } from '../../modules/shared/shared.module';
import { ReactiveFormsModule } from '@angular/forms';
import { TituloComponent } from '../../components/titulo/titulo.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule, NzButtonSize } from 'ng-zorro-antd/button';
import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from 'ngx-mask';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzBadgeModule } from 'ng-zorro-antd/badge';

@Component({
  selector: 'app-welcome',
  templateUrl: './welcome.component.html',
  styleUrl: './welcome.component.scss',
  imports: [
    NzFormModule,
    SharedModule,
    TituloComponent,
    ReactiveFormsModule,
    NzIconModule,
    NzButtonModule,
    NgxMaskDirective,
    NzTableModule,
    NzBadgeModule
  ],
  providers:[
    provideNgxMask()
  ]

})
export class WelcomeComponent {
  form!: FormGroup;
  titlePage: string = "Grupo de Usuário";
  size: NzButtonSize = 'large';

  companies = [
    { id: 1, cnpj: '12.345.678/0001-01', nomeFantasia: 'Empresa A', estado: 'SP', cidade: 'São Paulo' },
    { id: 2, cnpj: '23.456.789/0001-02', nomeFantasia: 'Empresa B', estado: 'RJ', cidade: 'Rio de Janeiro' },
    { id: 3, cnpj: '34.567.890/0001-03', nomeFantasia: 'Empresa C', estado: 'MG', cidade: 'Belo Horizonte' },
    { id: 4, cnpj: '45.678.901/0001-04', nomeFantasia: 'Empresa D', estado: 'PR', cidade: 'Curitiba' },
    { id: 5, cnpj: '56.789.012/0001-05', nomeFantasia: 'Empresa E', estado: 'RS', cidade: 'Porto Alegre' },
    { id: 6, cnpj: '67.890.123/0001-06', nomeFantasia: 'Empresa F', estado: 'SC', cidade: 'Florianópolis' },
    { id: 7, cnpj: '78.901.234/0001-07', nomeFantasia: 'Empresa G', estado: 'BA', cidade: 'Salvador' },
    { id: 8, cnpj: '89.012.345/0001-08', nomeFantasia: 'Empresa H', estado: 'PE', cidade: 'Recife' },
    { id: 9, cnpj: '90.123.456/0001-09', nomeFantasia: 'Empresa I', estado: 'CE', cidade: 'Fortaleza' },
    { id: 10, cnpj: '01.234.567/0001-10', nomeFantasia: 'Empresa J', estado: 'DF', cidade: 'Brasília' },
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.form = this.fb.group({
      cnpj: ['', Validators.required],
      nome: ['', Validators.required],
      razaoSocial: ['', Validators.required],
      cep: ['', Validators.required],
      endereco: ['', Validators.required],
      numero: ['', Validators.required],
      complemento: [''],
      bairro: ['', Validators.required],
      cidade: ['', Validators.required],
      estado: ['', Validators.required],
    });
  }

  onSubmit(): void {
    if (this.form.valid) {
    }
  }

  onCancel(): void {
    this.form.reset();
  }

  editCompany(company: any): void {
  }

  deleteCompany(company: any): void {
  }
}
