import { Component } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { SharedModule } from './modules/shared/shared.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { LocalStorageService } from 'ngx-webstorage';
import { ToastContainerComponent } from './shared/toast/toast-container.component';
@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    NzIconModule, 
    NzLayoutModule, 
    NzMenuModule, 
    NzFormModule, 
    SharedModule, 
    NzBadgeModule, 
    NzFlexModule,
    NzBadgeModule,
    NzDropDownModule,
    ToastContainerComponent
  ],
  providers: [LocalStorageService],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  isCollapsed = false;
}
