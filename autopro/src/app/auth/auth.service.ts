import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Observable, of } from 'rxjs';
import { LoginModel } from '../pages/login/model/login.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  isLoggedIn$(): Observable<boolean> {
    if (isPlatformBrowser(this.platformId)) {
      let objLogin : LoginModel = JSON.parse(localStorage.getItem('objLogin') || '{}');
      return of(!!objLogin.token);
    }
    return of(false);
  }
}
