import { Routes } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from './auth/auth.service';
import { Router } from '@angular/router';
import { map } from 'rxjs';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full'
  },
  {path: 'login', loadChildren: () => import('./pages/login/login.routes').then(m => m.LOGIN_ROUTES) },
  {path: 'welcome', loadChildren: () => import('./pages/welcome/welcome.routes').then(m => m.WELCOME_ROUTES) },
  {path: 'user', loadChildren:() => import('./pages/user/user-component/user.routes').then(m=>m.USERS_ROUTES)},
  {path: 'usergroup', loadChildren:() => import('./pages/configuracoes/user-group/user-group.routes').then(m=>m.USERGROUP_ROUTES)},
  {path: 'service', loadChildren:() => import('./pages/configuracoes/service/service.routes').then(m=>m.SERVICE_ROUTES)},
  {path: 'attendance', loadChildren:() => import('./pages/configuracoes/attendance/attendance.routes').then(m=>m.ATTENDANCE_ROUTES)},
  {path: 'cautelarista', loadChildren:() => import('./pages/configuracoes/cautelarista/cautelarista.routes').then(m=>m.CAUTELARISTA_ROUTES)},
  {path: 'cliente', loadChildren:() => import('./pages/configuracoes/cliente/cliente.routes').then(m=>m.CLIENTE_ROUTES)},
  {
    path: 'principal', 
    loadChildren:() => import('./pages/principal/principal.routes').then(m=>m.PRINCIPAL_ROUTES),
    canActivate: [() => {
      const auth = inject(AuthService);
      const router = inject(Router);
      
      return auth.isLoggedIn$().pipe(
        map(loggedIn => {
          if (loggedIn) {
            return true;
          } else {
            return router.createUrlTree(['/login']);
          }
        })
      );
    }]
  },
  {path: 'sair', loadChildren:() => import('./pages/configuracoes/sair/sair.routes').then(m=>m.SAIR_ROUTES)},
  {path: 'route-error', loadChildren:() => import('./pages/errors/route-error/route-error.routes').then(m=>m.ROUTE_ERROR_ROUTES)},
  {path: 'permission', loadChildren:() => import('./pages/system/permissions/permissions.routes').then(m=>m.PERMISSIONS_ROUTES)},
  {path: 'cautelaristafee', loadChildren:() => import('./pages/configuracoes/cauteralist-fee/cautelarista-fee.routes').then(m=>m.CAUTELARISTA_ROUTES)},
  {path: 'service-radius', loadChildren: () => import('./pages/configuracoes/service-radius/service-radius.routes').then(m => m.SERVICE_RADIUS_ROUTE)}
];
