.attendance-details-container {
  padding: 16px 0;

  .info-item {
    margin-bottom: 16px;

    .info-label {
      display: block;
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .info-value {
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;

      strong {
        font-weight: 600;
      }

      &.price-value {
        font-size: 18px;
        font-weight: 600;
        color: #1890ff;

        &.success {
          color: #52c41a;
        }
      }
    }
  }

  .description-box {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 12px;
    min-height: 60px;
    white-space: pre-wrap;
    word-break: break-word;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 1.6;
  }

  .timeline-label {
    font-weight: 500;
    margin-right: 8px;
  }

  // Ajustes para cards
  ::ng-deep {
    .ant-card {
      margin-bottom: 16px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px rgba(0, 0, 0, 0.02);

      .ant-card-head {
        background-color: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 16px;

        .ant-card-head-title {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .ant-card-body {
        padding: 16px;
      }
    }

    // Ajustes para tabs
    .ant-tabs {
      .ant-tabs-nav {
        margin-bottom: 16px;

        .ant-tabs-tab {
          font-size: 14px;
          padding: 8px 0;

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: #1890ff;
              font-weight: 500;
            }
          }
        }
      }

      .ant-tabs-content {
        padding: 0;
      }
    }

    // Ajustes para tags
    .ant-tag {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 2px;
    }

    // Ajustes para timeline
    .ant-timeline {
      .ant-timeline-item {
        padding-bottom: 20px;

        &:last-child {
          padding-bottom: 0;
        }

        .ant-timeline-item-content {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }

    // Ajustes para empty state
    .ant-empty {
      margin: 20px 0;
      
      .ant-empty-description {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
      }
    }

    // Ajustes para divider
    .ant-divider {
      margin: 16px 0;
    }
  }

  // Responsividade
  @media (max-width: 768px) {
    .info-item {
      margin-bottom: 12px;

      .info-label {
        font-size: 11px;
      }

      .info-value {
        font-size: 13px;

        &.price-value {
          font-size: 16px;
        }
      }
    }

    ::ng-deep {
      .ant-card {
        .ant-card-head {
          padding: 10px 12px;

          .ant-card-head-title {
            font-size: 14px;
          }
        }

        .ant-card-body {
          padding: 12px;
        }
      }

      .ant-tabs {
        .ant-tabs-tab {
          font-size: 13px;
          padding: 6px 0;
        }
      }
    }
  }
}

// Estilos para impressão
@media print {
  .attendance-details-container {
    ::ng-deep {
      .ant-tabs-nav {
        display: none;
      }

      .ant-tabs-content-holder {
        .ant-tabs-content {
          .ant-tabs-tabpane {
            display: block !important;
            opacity: 1 !important;
            height: auto !important;
            page-break-inside: avoid;
          }
        }
      }

      .ant-card {
        box-shadow: none;
        border: 1px solid #d9d9d9;
        page-break-inside: avoid;
      }
    }
  }
}