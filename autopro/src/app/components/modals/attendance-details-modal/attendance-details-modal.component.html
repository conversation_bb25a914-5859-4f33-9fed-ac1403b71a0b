<div class="attendance-details-container">
  <nz-tabset>
    <!-- Tab de Informações Básicas -->
    <nz-tab nzTitle="Informações Gerais">
      <div nz-row [nzGutter]="[16, 16]">
        <!-- Informações Básicas -->
        <div nz-col [nzSpan]="24">
          <nz-card nzTitle="Informações Básicas" [nzBordered]="true">
            <div nz-row [nzGutter]="[16, 16]">
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="6">
                <div class="info-item">
                  <label class="info-label">ID do Atendimento</label>
                  <div class="info-value"><strong>#{{ attendance.id }}</strong></div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="6">
                <div class="info-item">
                  <label class="info-label">Data da Solicitação</label>
                  <div class="info-value">{{ attendance.dateRequest | date:'dd/MM/yyyy HH:mm' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="6">
                <div class="info-item">
                  <label class="info-label">Status</label>
                  <div class="info-value">
                    <nz-tag [nzColor]="getStatusColor(attendance.status)">
                      {{ getStatusLabel(attendance.status) }}
                    </nz-tag>
                  </div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="6">
                <div class="info-item">
                  <label class="info-label">Referência do Veículo</label>
                  <div class="info-value">{{ attendance.referenceCar }}</div>
                </div>
              </div>
            </div>
          </nz-card>
        </div>

        <!-- Informações do Cliente -->
        <div nz-col [nzXs]="24" [nzMd]="12">
          <nz-card nzTitle="Informações do Cliente" [nzBordered]="true">
            <div *ngIf="attendance.customer; else noCustomer">
              <div class="info-item">
                <label class="info-label">Nome</label>
                <div class="info-value">{{ attendance.customer.name }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">{{ attendance.customer.type === 'PF' ? 'CPF' : 'CNPJ' }}</label>
                <div class="info-value">{{ attendance.customer.fiscalNumber || 'Não informado' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">Tipo</label>
                <div class="info-value">
                  <nz-tag [nzColor]="attendance.customer.type === 'PF' ? 'blue' : 'green'">
                    {{ attendance.customer.type === 'PF' ? 'Pessoa Física' : 'Pessoa Jurídica' }}
                  </nz-tag>
                </div>
              </div>
              <div class="info-item" *ngIf="attendance.vehicleOwnerName">
                <label class="info-label">Proprietário do Veículo</label>
                <div class="info-value">{{ attendance.vehicleOwnerName }}</div>
              </div>
              <div class="info-item" *ngIf="attendance.vehicleOwnerPhone">
                <label class="info-label">Telefone do Proprietário</label>
                <div class="info-value">{{ attendance.vehicleOwnerPhone }}</div>
              </div>
            </div>
            <ng-template #noCustomer>
              <nz-empty nzNotFoundContent="Informações do cliente não disponíveis"></nz-empty>
            </ng-template>
          </nz-card>
        </div>

        <!-- Informações do Veículo -->
        <div nz-col [nzXs]="24" [nzMd]="12">
          <nz-card nzTitle="Informações do Veículo" [nzBordered]="true">
            <div class="info-item">
              <label class="info-label">Marca</label>
              <div class="info-value">{{ attendance.brand?.brand || 'Não informado' }}</div>
            </div>
            <div class="info-item">
              <label class="info-label">Modelo</label>
              <div class="info-value">{{ attendance.model?.model || 'Não informado' }}</div>
            </div>
            <div class="info-item">
              <label class="info-label">Referência</label>
              <div class="info-value"><strong>{{ attendance.referenceCar }}</strong></div>
            </div>
          </nz-card>
        </div>
      </div>
    </nz-tab>

    <!-- Tab de Serviços -->
    <nz-tab nzTitle="Serviços Solicitados">
      <div nz-row [nzGutter]="[16, 16]">
        <div nz-col [nzSpan]="24">
          <nz-card nzTitle="Serviços" [nzBordered]="true">
            <div nz-row [nzGutter]="[16, 16]">
              <div nz-col [nzXs]="24" [nzSm]="12">
                <div class="info-item">
                  <label class="info-label">Serviço Cautelar</label>
                  <div class="info-value">
                    <nz-tag [nzColor]="attendance.hasCautelarService ? 'success' : 'default'">
                      <i nz-icon [nzType]="attendance.hasCautelarService ? 'check' : 'close'"></i>
                      {{ attendance.hasCautelarService ? 'Sim' : 'Não' }}
                    </nz-tag>
                  </div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12">
                <div class="info-item">
                  <label class="info-label">Serviço de Vistoria</label>
                  <div class="info-value">
                    <nz-tag [nzColor]="attendance.hasVistoriaService ? 'success' : 'default'">
                      <i nz-icon [nzType]="attendance.hasVistoriaService ? 'check' : 'close'"></i>
                      {{ attendance.hasVistoriaService ? 'Sim' : 'Não' }}
                    </nz-tag>
                  </div>
                </div>
              </div>
            </div>
            
            <nz-divider *ngIf="attendance.customerNotes"></nz-divider>
            
            <div class="info-item" *ngIf="attendance.customerNotes">
              <label class="info-label">Observações do Cliente</label>
              <div class="description-box">
                {{ attendance.customerNotes }}
              </div>
            </div>
          </nz-card>
        </div>

        <!-- Profissional Responsável -->
        <div nz-col [nzSpan]="24" *ngIf="attendance.professional">
          <nz-card nzTitle="Profissional Responsável" [nzBordered]="true">
            <div nz-row [nzGutter]="[16, 16]">
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Nome</label>
                  <div class="info-value">{{ attendance.professional.name }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">CPF</label>
                  <div class="info-value">{{ attendance.professional.cpf }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Telefone Principal</label>
                  <div class="info-value">{{ attendance.professional.cellphone1 }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8" *ngIf="attendance.professional.cellPhone2">
                <div class="info-item">
                  <label class="info-label">Telefone Secundário</label>
                  <div class="info-value">{{ attendance.professional.cellPhone2 }}</div>
                </div>
              </div>
            </div>
          </nz-card>
        </div>
      </div>
    </nz-tab>

    <!-- Tab de Endereço Principal -->
    <nz-tab nzTitle="Endereço do Atendimento">
      <div nz-row [nzGutter]="[16, 16]">
        <div nz-col [nzSpan]="24">
          <nz-card nzTitle="Endereço Principal" [nzBordered]="true">
            <div nz-row [nzGutter]="[16, 16]">
              <!-- Todos os campos com largura uniforme de 1/3 (8 colunas) -->
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Logradouro</label>
                  <div class="info-value">{{ attendance.address }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Número</label>
                  <div class="info-value">{{ attendance.number || 'S/N' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Complemento</label>
                  <div class="info-value">{{ attendance.addressComplement || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Bairro</label>
                  <div class="info-value">{{ attendance.district?.district || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Cidade</label>
                  <div class="info-value">{{ attendance.city?.city || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Estado</label>
                  <div class="info-value">{{ attendance.state?.state || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">CEP</label>
                  <div class="info-value">{{ attendance.postalCode || 'Não informado' }}</div>
                </div>
              </div>
            </div>
          </nz-card>
        </div>

        <!-- Endereço Alternativo do Veículo -->
        <div nz-col [nzSpan]="24" *ngIf="attendance.vehicleAtDifferentAddress">
          <nz-card nzTitle="Endereço Alternativo do Veículo" [nzBordered]="true">
            <nz-alert 
              nzType="info" 
              nzMessage="O veículo está em endereço diferente do principal"
              nzShowIcon
              class="mb-3">
            </nz-alert>
            <div nz-row [nzGutter]="[16, 16]">
              <!-- Todos os campos com largura uniforme de 1/3 (8 colunas) -->
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Logradouro</label>
                  <div class="info-value">{{ attendance.vehicleAddress }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Número</label>
                  <div class="info-value">{{ attendance.vehicleNumber || 'S/N' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Complemento</label>
                  <div class="info-value">{{ attendance.vehicleAddressComplement || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Bairro</label>
                  <div class="info-value">{{ attendance.vehicleDistrict?.district || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Cidade</label>
                  <div class="info-value">{{ attendance.vehicleCity?.city || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">Estado</label>
                  <div class="info-value">{{ attendance.vehicleState?.state || 'Não informado' }}</div>
                </div>
              </div>
              <div nz-col [nzXs]="24" [nzSm]="12" [nzMd]="8">
                <div class="info-item">
                  <label class="info-label">CEP</label>
                  <div class="info-value">{{ attendance.vehiclePostalCode || 'Não informado' }}</div>
                </div>
              </div>
            </div>
          </nz-card>
        </div>
      </div>
    </nz-tab>

    <!-- Tab de Datas -->
    <nz-tab nzTitle="Histórico de Datas">
      <div nz-row [nzGutter]="[16, 16]">
        <div nz-col [nzSpan]="24">
          <nz-card nzTitle="Timeline do Atendimento" [nzBordered]="true">
            <nz-timeline>
              <nz-timeline-item nzColor="blue">
                <span class="timeline-label">Solicitado em:</span>
                {{ attendance.dateRequest | date:'dd/MM/yyyy HH:mm:ss' }}
              </nz-timeline-item>
              <nz-timeline-item nzColor="orange" *ngIf="attendance.dateAttendance">
                <span class="timeline-label">Atendimento iniciado em:</span>
                {{ attendance.dateAttendance | date:'dd/MM/yyyy HH:mm:ss' }}
              </nz-timeline-item>
              <nz-timeline-item nzColor="green" *ngIf="attendance.dateFinish">
                <span class="timeline-label">Finalizado em:</span>
                {{ attendance.dateFinish | date:'dd/MM/yyyy HH:mm:ss' }}
              </nz-timeline-item>
            </nz-timeline>
          </nz-card>
        </div>
      </div>
    </nz-tab>
  </nz-tabset>
</div>