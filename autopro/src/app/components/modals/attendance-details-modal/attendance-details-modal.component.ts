import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { AttendanceModel, AttendanceStatus } from '../../../pages/configuracoes/attendance/model/attendance.model';

@Component({
  selector: 'app-attendance-details-modal',
  templateUrl: './attendance-details-modal.component.html',
  styleUrls: ['./attendance-details-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzTabsModule,
    NzTagModule,
    NzGridModule,
    NzDividerModule,
    NzEmptyModule,
    NzTimelineModule,
    NzIconModule,
    NzAlertModule
  ]
})
export class AttendanceDetailsModalComponent implements OnInit {
  readonly nzModalData: { attendance: AttendanceModel } = inject(NZ_MODAL_DATA);
  attendance: AttendanceModel;

  constructor(
    private modal: NzModalRef
  ) {
    this.attendance = this.nzModalData.attendance;
  }

  ngOnInit(): void {}

  close(): void {
    this.modal.destroy();
  }

  getStatusColor(status: number): string {
    switch (status) {
      case AttendanceStatus.Requested:
        return 'blue';
      case AttendanceStatus.InProgress:
        return 'orange';
      case AttendanceStatus.Completed:
        return 'green';
      case AttendanceStatus.Cancelled:
        return 'red';
      default:
        return 'default';
    }
  }

  getStatusLabel(status: number): string {
    switch (status) {
      case AttendanceStatus.Requested:
        return 'Solicitado';
      case AttendanceStatus.InProgress:
        return 'Em Andamento';
      case AttendanceStatus.Completed:
        return 'Concluído';
      case AttendanceStatus.Cancelled:
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  getServiceTypeLabel(serviceType: string | number): string {
    if (typeof serviceType === 'string') {
      switch (serviceType.toUpperCase()) {
        case 'MAINTENANCE':
          return 'Manutenção';
        case 'REPAIR':
          return 'Reparo';
        case 'INSPECTION':
          return 'Inspeção';
        case 'CONSULTATION':
          return 'Consulta';
        case 'CAUTELAR':
          return 'Cautelar';
        case 'VISTORIA':
          return 'Vistoria';
        default:
          return serviceType;
      }
    }
    // Se for número, retorna como string
    return serviceType?.toString() || 'Não definido';
  }
}