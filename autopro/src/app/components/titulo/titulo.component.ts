import { Component, Input } from '@angular/core';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzPageHeaderModule } from 'ng-zorro-antd/page-header';

@Component({
  selector: 'app-titulo',
  imports: [NzBreadCrumbModule, NzPageHeaderModule],
  templateUrl: './titulo.component.html',
  styleUrl: './titulo.component.scss'
})
export class TituloComponent {

  
  @Input() firstLevel:string = "Primeiro Nível";
  @Input() secondLevel : string = "Segundo Nível";
  @Input() title: string = "Welcome";
  @Input() subtitle : string  = "Página para mostrar a descrição da tela";

}
