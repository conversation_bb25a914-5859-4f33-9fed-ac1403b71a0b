/* You can add global styles to this file, and also import other style files */
body {
    font-family: 'Lexend';
}
@font-face {
    font-family: 'Lexend';
    src: url('./assets/fonts/Lexend/Lexend-VariableFont_wght.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

.ant-modal-header {
    padding: 16px 24px;
    color: rgb(255 255 255 / 85%);
    background: #c9cccf;
    border-bottom: 2px solid #bababa;
    border-radius: 2px 2px 0 0;
  }

  .ant-modal-footer {
    padding: 16px 24px;
    color: rgb(255 255 255 / 85%);
    background: #c9cccf;
    border-radius: 2px 2px 0 0;
  }

  .ant-btn-primary {
    color: #fff;
    border-color: #1890ff;
    background: #0a7ace;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.ant-notification-notice.success {
    position: relative;
    width: 384px;
    max-width: calc(100vw - 24px * 2);
    margin-bottom: 16px;
    margin-left: auto;
    padding: 16px 24px;
    overflow: hidden;
    line-height: 1.5715;
    word-wrap: break-word;
    background: #a5d3ac;
    border-radius: 2px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.ant-notification-notice.error {
    position: relative;
    width: 384px;
    max-width: calc(100vw - 24px * 2);
    margin-bottom: 16px;
    margin-left: auto;
    padding: 16px 24px;
    overflow: hidden;
    line-height: 1.5715;
    word-wrap: break-word;
    background: #d39999;
    border-radius: 2px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.card{
    background-color: #2771bb;
  } 
  .ant-card-body{
    background-color: #f2f2f2;
  }
  .ant-card-head {
    background-color: #1890ff;
    color: #ffffff;
    
  }