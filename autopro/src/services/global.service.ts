import { HttpClient } from '@angular/common/http';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ConfigService } from './config.service';
import { Observable } from 'rxjs';
import { UserGroupModel } from '../app/pages/configuracoes/user-group/model/usergroup.model';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class GlobalService {

  public url: string;
  
  constructor(
    private http: HttpClient,
    private config: ConfigService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) 
  { 
    this.url = environment.apiUrl;
  }

  post(endpoint: string, body: any) {
     return this.http
                .post(this.url + endpoint, body, {headers: this.config.getHeaders()});
  }

  postFile(endpoint: string, formData: FormData) {
    // Para upload de arquivos, não incluímos o Content-Type header
    // O browser define automaticamente como multipart/form-data
    const token = isPlatformBrowser(this.platformId) ? localStorage.getItem('token') : null;
    const headers = {
      'Authorization': 'Bearer ' + (token || '')
    };
    return this.http.post(this.url + endpoint, formData, { headers });
  }

  get(endpoint: string) {
    return this.http
              .get(this.url + endpoint, {headers: this.config.getHeaders()});
  }
  
  getbyid(endpoint: string, id: number) {
    return this.http
               .get(`${this.url}${endpoint}/${id}`, {headers: this.config.getHeaders()});
  }

  getByGuid(endpoint: string, guid: string) {
    return this.http
               .get(`${this.url}${endpoint}/id/${guid}`, {headers: this.config.getHeaders()});
  }
  put(endpoint: string, body: any) {
    return this.http
                .put(this.url + endpoint, body, {headers: this.config.getHeaders()});
  }

  

  delete(endpoint: string) {
    let url = `${this.url}${endpoint}`;
    return this.http
                .delete(url, {headers: this.config.getHeaders()});
  }

  patch(endpoint: string, body: any) {
    return this.http
                .patch(this.url + endpoint, body, {headers: this.config.getHeaders()});
  }
}
