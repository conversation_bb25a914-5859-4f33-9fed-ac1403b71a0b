using AutoMapper;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Mappings;

public class AutoMapperProfile : Profile
{
    public AutoMapperProfile()
    {
        CreateMap<UserGroupDTO, UserGroupModel>().ReverseMap();
        CreateMap<UserGroupModel, UserGroupListDTO>().ReverseMap();
        CreateMap<UserDTO, UserModel>().ReverseMap();
        CreateMap<UserModel, UserListDTO>()
            .ForMember(dest => dest.UserGroupName, opt => opt.MapFrom(src => src.UserGroup != null ? src.UserGroup.NomeGrupoUser : null));
        CreateMap<ServiceDTO, ServiceModel>()
            .ForMember(dest => dest.Id, opt => opt.Condition(src => src.Id > 0))
            .ReverseMap();
        CreateMap<ServiceModel, ServiceListDTO>().ReverseMap();
        CreateMap<ScreensModel, ScreenDTO>().ReverseMap();
        CreateMap<ScreenOptionsModel, ScreenOptionsDTO>().ReverseMap();
        CreateMap<ScreenOptionPermissionModel, ScreenOptionPermissionDTO>().ReverseMap();
        CreateMap<CautelaristModel, CautelaristDTO>().ReverseMap();
        CreateMap<CautelaristModel, CautelaristListDTO>().ReverseMap();
        CreateMap<ScreenOptionPermissionModel, ScreenOptionsPermissionCompleteDTO>()
            .ForMember(dest => dest.ScreenId, opt => opt.MapFrom(src => src.ScreenOptionModel.ScreenId))
            .ForMember(dest => dest.ScreenName, opt => opt.MapFrom(src => src.ScreenOptionModel.ScreenModel.ScreenName))
            .ForMember(dest => dest.UserGroupName, opt => opt.MapFrom(src => src.UserGroup.NomeGrupoUser))
            .ForMember(dest => dest.ScreenOptionName, opt => opt.MapFrom(src => src.ScreenOptionModel.Option));
        CreateMap<LogModel, LogDTO>().ReverseMap();
        CreateMap<CautelaristFeeModel, CautelaristFeeDTO>().ReverseMap();
        
        // Attendance mappings
        CreateMap<AttendanceModel, AttendanceDTO>().ReverseMap();
        
        // Configuração específica para criar AttendanceModel ignorando navigation properties
        CreateMap<AttendanceDTO, AttendanceModel>()
            .ForMember(dest => dest.Customer, opt => opt.Ignore())
            .ForMember(dest => dest.Professional, opt => opt.Ignore())
            .ForMember(dest => dest.Brand, opt => opt.Ignore())
            .ForMember(dest => dest.Model, opt => opt.Ignore())
            .ForMember(dest => dest.District, opt => opt.Ignore())
            .ForMember(dest => dest.City, opt => opt.Ignore())
            .ForMember(dest => dest.State, opt => opt.Ignore())
            .ForMember(dest => dest.VehicleDistrict, opt => opt.Ignore())
            .ForMember(dest => dest.VehicleCity, opt => opt.Ignore())
            .ForMember(dest => dest.VehicleState, opt => opt.Ignore())
            .ForMember(dest => dest.Id, opt => opt.Ignore()) // ID é gerado pelo banco
            .ForMember(dest => dest.Guid, opt => opt.Condition(src => src.Guid != Guid.Empty))
            .ForMember(dest => dest.DateRequest, opt => opt.Condition(src => src.DateRequest != default));
            
        CreateMap<CustomerModel, CustomerDTO>().ReverseMap();
        CreateMap<BrandModel, BrandDTO>().ReverseMap();
        CreateMap<ModelModel, ModelDTO>().ReverseMap();
        CreateMap<DistrictModel, DistrictDTO>().ReverseMap();
        CreateMap<CityModel, CityDTO>().ReverseMap();
        CreateMap<StateModel, StateDTO>().ReverseMap();

        CreateMap<RadiusModel, RadiusDTO>().ReverseMap();
        CreateMap<ItemModel, ItemDTO>().ReverseMap();
        CreateMap<SubItemModel, SubItemDTO>().ReverseMap();
        CreateMap<CautelaristFeeModel, CautelaristFeeDTO>().ReverseMap();

    }
}