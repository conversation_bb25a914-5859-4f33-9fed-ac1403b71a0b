﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AutoVPro.Migrations
{
    /// <inheritdoc />
    public partial class initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ScreenOptions_Screens_ScreenId",
                table: "ScreenOptions");

            migrationBuilder.DropIndex(
                name: "IX_ScreenOptions_ScreenId",
                table: "ScreenOptions");

            migrationBuilder.AddColumn<int>(
                name: "ScreenModelId",
                table: "ScreenOptions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_ScreenOptions_ScreenModelId",
                table: "ScreenOptions",
                column: "ScreenModelId");

            migrationBuilder.AddForeignKey(
                name: "FK_ScreenOptions_Screens_ScreenModelId",
                table: "ScreenOptions",
                column: "ScreenModelId",
                principalTable: "Screens",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ScreenOptions_Screens_ScreenModelId",
                table: "ScreenOptions");

            migrationBuilder.DropIndex(
                name: "IX_ScreenOptions_ScreenModelId",
                table: "ScreenOptions");

            migrationBuilder.DropColumn(
                name: "ScreenModelId",
                table: "ScreenOptions");

            migrationBuilder.CreateIndex(
                name: "IX_ScreenOptions_ScreenId",
                table: "ScreenOptions",
                column: "ScreenId");

            migrationBuilder.AddForeignKey(
                name: "FK_ScreenOptions_Screens_ScreenId",
                table: "ScreenOptions",
                column: "ScreenId",
                principalTable: "Screens",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
