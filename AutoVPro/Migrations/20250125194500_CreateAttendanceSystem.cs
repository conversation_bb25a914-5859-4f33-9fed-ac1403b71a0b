using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AutoVPro.Migrations
{
    /// <inheritdoc />
    public partial class CreateAttendanceSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Criar tabela States
            migrationBuilder.CreateTable(
                name: "States",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    State = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_States", x => x.Id);
                });

            // Criar tabela Cities
            migrationBuilder.CreateTable(
                name: "Cities",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    City = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cities", x => x.Id);
                });

            // Criar tabela Districts
            migrationBuilder.CreateTable(
                name: "Districts",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    District = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Districts", x => x.Id);
                });

            // Criar tabela Brands
            migrationBuilder.CreateTable(
                name: "Brands",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Brand = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Brands", x => x.Id);
                });

            // Criar tabela Models
            migrationBuilder.CreateTable(
                name: "Models",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Model = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    BrandId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Models", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Models_Brands_BrandId",
                        column: x => x.BrandId,
                        principalTable: "Brands",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Criar tabela Customers
            migrationBuilder.CreateTable(
                name: "Customers",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Guid = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FiscalNumber = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    BirthDate = table.Column<int>(type: "int", nullable: false),
                    Address = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Number = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AddressComplement = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    DistrictId = table.Column<long>(type: "bigint", nullable: false),
                    CityId = table.Column<long>(type: "bigint", nullable: false),
                    StateId = table.Column<long>(type: "bigint", nullable: false),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Customers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Customers_Districts_DistrictId",
                        column: x => x.DistrictId,
                        principalTable: "Districts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Customers_Cities_CityId",
                        column: x => x.CityId,
                        principalTable: "Cities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Customers_States_StateId",
                        column: x => x.StateId,
                        principalTable: "States",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Verificar se Cautelarist já existe (pode estar como ProfessionalCautelar)
            // Se não existir, criar a tabela
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Cautelarist' AND xtype='U')
                BEGIN
                    IF EXISTS (SELECT * FROM sysobjects WHERE name='ProfessionalCautelar' AND xtype='U')
                    BEGIN
                        -- Renomear tabela existente
                        EXEC sp_rename 'ProfessionalCautelar', 'Cautelarist';
                    END
                    ELSE
                    BEGIN
                        -- Criar nova tabela se não existir
                        CREATE TABLE [Cautelarist] (
                            [Id] bigint IDENTITY(1,1) NOT NULL,
                            [Guid] uniqueidentifier NOT NULL,
                            [Name] nvarchar(255) NOT NULL,
                            [CPF] nvarchar(255) NOT NULL,
                            [Cellphone1] nvarchar(255) NOT NULL,
                            [CellPhone2] nvarchar(255) NOT NULL,
                            [PostalCode] nvarchar(255) NOT NULL,
                            [Address] nvarchar(255) NOT NULL,
                            [Number] nvarchar(255) NOT NULL,
                            [AddressComplement] nvarchar(255) NOT NULL,
                            [DistrictId] bigint NOT NULL,
                            [CityId] bigint NOT NULL,
                            [StateId] bigint NOT NULL,
                            [Status] int NOT NULL,
                            [Source] int NOT NULL,
                            [Active] bit NOT NULL,
                            [Deleted] bit NOT NULL,
                            [CreatedOn] datetime2 NOT NULL,
                            [UpdatedOn] datetime2 NOT NULL,
                            CONSTRAINT [PK_Cautelarist] PRIMARY KEY ([Id])
                        );
                    END
                END
            ");

            // Criar tabela Attendance
            migrationBuilder.CreateTable(
                name: "Attendance",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Guid = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DateRequest = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateAttendance = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateFinish = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CustomerId = table.Column<long>(type: "bigint", nullable: false),
                    ProfessionalId = table.Column<long>(type: "bigint", nullable: true),
                    ReferenceCar = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    BrandId = table.Column<long>(type: "bigint", nullable: false),
                    ModelId = table.Column<long>(type: "bigint", nullable: false),
                    PostalCode = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Address = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Number = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AddressComplement = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    DistrictId = table.Column<long>(type: "bigint", nullable: false),
                    CityId = table.Column<long>(type: "bigint", nullable: false),
                    StateId = table.Column<long>(type: "bigint", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Attendance", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Attendance_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Attendance_Brands_BrandId",
                        column: x => x.BrandId,
                        principalTable: "Brands",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Attendance_Models_ModelId",
                        column: x => x.ModelId,
                        principalTable: "Models",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Attendance_Districts_DistrictId",
                        column: x => x.DistrictId,
                        principalTable: "Districts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Attendance_Cities_CityId",
                        column: x => x.CityId,
                        principalTable: "Cities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Attendance_States_StateId",
                        column: x => x.StateId,
                        principalTable: "States",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Adicionar Foreign Key para Cautelarist se a tabela existir
            migrationBuilder.Sql(@"
                IF EXISTS (SELECT * FROM sysobjects WHERE name='Cautelarist' AND xtype='U')
                BEGIN
                    ALTER TABLE [Attendance] 
                    ADD CONSTRAINT [FK_Attendance_Cautelarist_ProfessionalId] 
                    FOREIGN KEY ([ProfessionalId]) 
                    REFERENCES [Cautelarist] ([Id]);
                END
            ");

            // Criar índices para melhor performance
            migrationBuilder.CreateIndex(
                name: "IX_Attendance_CustomerId",
                table: "Attendance",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Attendance_ProfessionalId",
                table: "Attendance",
                column: "ProfessionalId");

            migrationBuilder.CreateIndex(
                name: "IX_Attendance_Status",
                table: "Attendance",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Attendance_DateRequest",
                table: "Attendance",
                column: "DateRequest");

            migrationBuilder.CreateIndex(
                name: "IX_Models_BrandId",
                table: "Models",
                column: "BrandId");

            migrationBuilder.CreateIndex(
                name: "IX_Customers_DistrictId",
                table: "Customers",
                column: "DistrictId");

            migrationBuilder.CreateIndex(
                name: "IX_Customers_CityId",
                table: "Customers",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_Customers_StateId",
                table: "Customers",
                column: "StateId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remover tabelas na ordem correta (relacionamentos primeiro)
            migrationBuilder.DropTable(name: "Attendance");
            migrationBuilder.DropTable(name: "Customers");
            migrationBuilder.DropTable(name: "Models");
            migrationBuilder.DropTable(name: "Brands");
            migrationBuilder.DropTable(name: "Districts");
            migrationBuilder.DropTable(name: "Cities");
            migrationBuilder.DropTable(name: "States");
            
            // Note: Não removemos Cautelarist pois pode ter dados importantes
        }
    }
}