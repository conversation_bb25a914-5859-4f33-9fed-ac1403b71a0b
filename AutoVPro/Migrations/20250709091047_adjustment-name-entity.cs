﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AutoVPro.Migrations
{
    /// <inheritdoc />
    public partial class adjustmentnameentity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ScreenOptionPermisions");

            migrationBuilder.CreateTable(
                name: "ScreenOptionPermissions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ScreenOptionId = table.Column<int>(type: "int", nullable: false),
                    UserGroupId = table.Column<int>(type: "int", nullable: false),
                    Allow = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScreenOptionPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ScreenOptionPermissions_ScreenOptions_ScreenOptionId",
                        column: x => x.ScreenOptionId,
                        principalTable: "ScreenOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ScreenOptionPermissions_UserGroups_UserGroupId",
                        column: x => x.UserGroupId,
                        principalTable: "UserGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ScreenOptionPermissions_ScreenOptionId",
                table: "ScreenOptionPermissions",
                column: "ScreenOptionId");

            migrationBuilder.CreateIndex(
                name: "IX_ScreenOptionPermissions_UserGroupId",
                table: "ScreenOptionPermissions",
                column: "UserGroupId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ScreenOptionPermissions");

            migrationBuilder.CreateTable(
                name: "ScreenOptionPermisions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ScreenOptionId = table.Column<int>(type: "int", nullable: false),
                    UserGroupId = table.Column<int>(type: "int", nullable: false),
                    Allow = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScreenOptionPermisions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ScreenOptionPermisions_ScreenOptions_ScreenOptionId",
                        column: x => x.ScreenOptionId,
                        principalTable: "ScreenOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ScreenOptionPermisions_UserGroups_UserGroupId",
                        column: x => x.UserGroupId,
                        principalTable: "UserGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ScreenOptionPermisions_ScreenOptionId",
                table: "ScreenOptionPermisions",
                column: "ScreenOptionId");

            migrationBuilder.CreateIndex(
                name: "IX_ScreenOptionPermisions_UserGroupId",
                table: "ScreenOptionPermisions",
                column: "UserGroupId");
        }
    }
}
