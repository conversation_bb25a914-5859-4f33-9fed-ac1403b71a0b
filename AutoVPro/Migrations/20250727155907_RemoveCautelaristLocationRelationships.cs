﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AutoVPro.Migrations
{
    /// <inheritdoc />
    public partial class RemoveCautelaristLocationRelationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Cautelarist_Cities_CityModelId",
                table: "Cautelarist");

            migrationBuilder.DropForeignKey(
                name: "FK_Cautelarist_Districts_DistrictModelId",
                table: "Cautelarist");

            migrationBuilder.DropForeignKey(
                name: "FK_Cautelarist_States_StateModelId",
                table: "Cautelarist");

            migrationBuilder.DropIndex(
                name: "IX_Cautelarist_CityModelId",
                table: "Cautelarist");

            migrationBuilder.DropIndex(
                name: "IX_Cautelarist_DistrictModelId",
                table: "Cautelarist");

            migrationBuilder.DropIndex(
                name: "IX_Cautelarist_StateModelId",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "Active",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "CityModelId",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "CreatedOn",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "Deleted",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "DistrictModelId",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "Guid",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "StateModelId",
                table: "Cautelarist");

            migrationBuilder.DropColumn(
                name: "UpdatedOn",
                table: "Cautelarist");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Active",
                table: "Cautelarist",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "CityModelId",
                table: "Cautelarist",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedOn",
                table: "Cautelarist",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "Deleted",
                table: "Cautelarist",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "DistrictModelId",
                table: "Cautelarist",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "Guid",
                table: "Cautelarist",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<long>(
                name: "StateModelId",
                table: "Cautelarist",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedOn",
                table: "Cautelarist",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.CreateIndex(
                name: "IX_Cautelarist_CityModelId",
                table: "Cautelarist",
                column: "CityModelId");

            migrationBuilder.CreateIndex(
                name: "IX_Cautelarist_DistrictModelId",
                table: "Cautelarist",
                column: "DistrictModelId");

            migrationBuilder.CreateIndex(
                name: "IX_Cautelarist_StateModelId",
                table: "Cautelarist",
                column: "StateModelId");

            migrationBuilder.AddForeignKey(
                name: "FK_Cautelarist_Cities_CityModelId",
                table: "Cautelarist",
                column: "CityModelId",
                principalTable: "Cities",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Cautelarist_Districts_DistrictModelId",
                table: "Cautelarist",
                column: "DistrictModelId",
                principalTable: "Districts",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Cautelarist_States_StateModelId",
                table: "Cautelarist",
                column: "StateModelId",
                principalTable: "States",
                principalColumn: "Id");
        }
    }
}
