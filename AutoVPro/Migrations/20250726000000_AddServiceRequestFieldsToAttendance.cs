using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AutoVPro.Migrations
{
    /// <inheritdoc />
    public partial class AddServiceRequestFieldsToAttendance : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Adicionar campos para informações do proprietário do veículo
            migrationBuilder.AddColumn<string>(
                name: "VehicleOwnerName",
                table: "Attendance",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleOwnerPhone",
                table: "Attendance",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            // Adicionar campos para serviços
            migrationBuilder.AddColumn<bool>(
                name: "HasCautelarService",
                table: "Attendance",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HasVistoriaService",
                table: "Attendance",
                type: "bit",
                nullable: false,
                defaultValue: false);

            // Adicionar campo para observações do cliente
            migrationBuilder.AddColumn<string>(
                name: "CustomerNotes",
                table: "Attendance",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            // Adicionar campos para endereço alternativo do veículo
            migrationBuilder.AddColumn<bool>(
                name: "VehicleAtDifferentAddress",
                table: "Attendance",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "VehiclePostalCode",
                table: "Attendance",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleAddress",
                table: "Attendance",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleNumber",
                table: "Attendance",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleAddressComplement",
                table: "Attendance",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "VehicleDistrictId",
                table: "Attendance",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "VehicleCityId",
                table: "Attendance",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "VehicleStateId",
                table: "Attendance",
                type: "bigint",
                nullable: true);

            // Criar índices para as foreign keys do endereço do veículo
            migrationBuilder.CreateIndex(
                name: "IX_Attendance_VehicleDistrictId",
                table: "Attendance",
                column: "VehicleDistrictId");

            migrationBuilder.CreateIndex(
                name: "IX_Attendance_VehicleCityId",
                table: "Attendance",
                column: "VehicleCityId");

            migrationBuilder.CreateIndex(
                name: "IX_Attendance_VehicleStateId",
                table: "Attendance",
                column: "VehicleStateId");

            // Adicionar foreign keys para o endereço do veículo
            migrationBuilder.AddForeignKey(
                name: "FK_Attendance_Districts_VehicleDistrictId",
                table: "Attendance",
                column: "VehicleDistrictId",
                principalTable: "Districts",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Attendance_Cities_VehicleCityId",
                table: "Attendance",
                column: "VehicleCityId",
                principalTable: "Cities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Attendance_States_VehicleStateId",
                table: "Attendance",
                column: "VehicleStateId",
                principalTable: "States",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remover foreign keys
            migrationBuilder.DropForeignKey(
                name: "FK_Attendance_Districts_VehicleDistrictId",
                table: "Attendance");

            migrationBuilder.DropForeignKey(
                name: "FK_Attendance_Cities_VehicleCityId",
                table: "Attendance");

            migrationBuilder.DropForeignKey(
                name: "FK_Attendance_States_VehicleStateId",
                table: "Attendance");

            // Remover índices
            migrationBuilder.DropIndex(
                name: "IX_Attendance_VehicleDistrictId",
                table: "Attendance");

            migrationBuilder.DropIndex(
                name: "IX_Attendance_VehicleCityId",
                table: "Attendance");

            migrationBuilder.DropIndex(
                name: "IX_Attendance_VehicleStateId",
                table: "Attendance");

            // Remover colunas
            migrationBuilder.DropColumn(
                name: "VehicleOwnerName",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleOwnerPhone",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "HasCautelarService",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "HasVistoriaService",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "CustomerNotes",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleAtDifferentAddress",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehiclePostalCode",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleAddress",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleNumber",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleAddressComplement",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleDistrictId",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleCityId",
                table: "Attendance");

            migrationBuilder.DropColumn(
                name: "VehicleStateId",
                table: "Attendance");
        }
    }
}