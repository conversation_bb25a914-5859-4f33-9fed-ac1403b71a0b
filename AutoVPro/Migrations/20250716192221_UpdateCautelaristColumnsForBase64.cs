﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AutoVPro.Migrations
{
    public partial class UpdateCautelaristColumnsForBase64 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "typeOfContact",
                table: "Cautelarist",
                newName: "TypeOfContact");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "Cautelarist",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "cellphone",
                table: "Cautelarist",
                newName: "Cellphone");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "Cautelarist",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "qualificationsImage",
                table: "Cautelarist",
                newName: "QualificationsImageBase64");

            migrationBuilder.RenameColumn(
                name: "qualifications",
                table: "Cautelarist",
                newName: "QualificationsDocumentBase64");

            migrationBuilder.RenameColumn(
                name: "proofOfResidence",
                table: "Cautelarist",
                newName: "ProofOfResidenceBase64");

            migrationBuilder.RenameColumn(
                name: "CNHImage",
                table: "Cautelarist",
                newName: "CnhImageBase64");

            migrationBuilder.RenameColumn(
                name: "CHNNumber",
                table: "Cautelarist",
                newName: "CnhDocumentBase64");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "TypeOfContact",
                table: "Cautelarist",
                newName: "typeOfContact");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "Cautelarist",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Cellphone",
                table: "Cautelarist",
                newName: "cellphone");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "Cautelarist",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "QualificationsImageBase64",
                table: "Cautelarist",
                newName: "qualificationsImage");

            migrationBuilder.RenameColumn(
                name: "QualificationsDocumentBase64",
                table: "Cautelarist",
                newName: "qualifications");

            migrationBuilder.RenameColumn(
                name: "ProofOfResidenceBase64",
                table: "Cautelarist",
                newName: "proofOfResidence");

            migrationBuilder.RenameColumn(
                name: "CnhImageBase64",
                table: "Cautelarist",
                newName: "CNHImage");

            migrationBuilder.RenameColumn(
                name: "CnhDocumentBase64",
                table: "Cautelarist",
                newName: "CHNNumber");
        }
    }
}
