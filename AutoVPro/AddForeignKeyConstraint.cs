using AutoVPro.Database.AutovproContext;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro;

public static class AddForeignKeyConstraint
{
    public static async Task ExecuteAsync(AutoProDbContext context)
    {
        try
        {
            string addForeignKeySql = @"
                -- Verificar se a foreign key já existe
                IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_CautelaristFees_Cautelarist_CautelaristId')
                BEGIN
                    -- Adicionar a foreign key constraint
                    ALTER TABLE [CautelaristFees]
                    ADD CONSTRAINT [FK_CautelaristFees_Cautelarist_CautelaristId] 
                    FOREIGN KEY ([CautelaristId]) REFERENCES [Cautelarist] ([Id]) ON DELETE CASCADE;
                    
                    PRINT 'Foreign Key constraint adicionada com sucesso!';
                END
                ELSE
                BEGIN
                    PRINT 'Foreign Key constraint já existe.';
                END";

            await context.Database.ExecuteSqlRawAsync(addForeignKeySql);
            Console.WriteLine("✅ Foreign Key constraint configurada com sucesso!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erro ao adicionar Foreign Key: {ex.Message}");
            throw;
        }
    }
}
