using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Database;

public class RadiusDb : IRadiusDatabase
{
    private readonly AutoProDbContext _context;

    public RadiusDb(AutoProDbContext context)
    {
        _context = context;
    }

    public async Task<RadiusModel> SaveAsync(RadiusModel entity)
    {
        try
        {
            var existingRadius = await _context.Radius
                .FirstOrDefaultAsync(r => r.RaioAtendimento == entity.RaioAtendimento);

            if (existingRadius != null)
            {
                throw new Exception($"Já existe um raio de atendimento com o valor {entity.RaioAtendimento}");
            }

            _context.Radius.Add(entity);
            await _context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao salvar raio de atendimento: {ex.Message}", ex);
        }
    }

    public async Task<RadiusModel> UpdateAsync(RadiusModel entity)
    {
        try
        {
            var existingRadius = await _context.Radius
                .FirstOrDefaultAsync(r => r.RaioAtendimento == entity.RaioAtendimento && r.Id != entity.Id);

            if (existingRadius != null)
            {
                throw new Exception($"Já existe um raio de atendimento com o valor {entity.RaioAtendimento}");
            }

            _context.Radius.Update(entity);
            await _context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao atualizar raio de atendimento: {ex.Message}", ex);
        }
    }

    public async Task<List<RadiusModel>> GetAllAsync()
    {
        var radius = await _context.Radius.OrderBy(r => r.RaioAtendimento).ToListAsync();
        return radius;
    }

    public async Task<RadiusModel> GetByIdAsync(long id)
    {
        try
        {
            var radius = await _context.Radius.FindAsync(id);
            if (radius == null)
            {
                throw new Exception("Raio de atendimento não encontrado");
            }
            return radius;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar raio de atendimento: {ex.Message}", ex);
        }
    }

    public async Task<RadiusModel> DeleteAsync(int id)
    {
        try
        {
            var radius = await _context.Radius.FindAsync((long)id);
            if (radius == null)
            {
                throw new Exception("Raio de atendimento não encontrado");
            }

            _context.Radius.Remove(radius);
            await _context.SaveChangesAsync();

            return radius;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao deletar raio de atendimento: {ex.Message}", ex);
        }
    }

    public async Task<bool> ExistsAsync(int raioAtendimento)
    {
        try
        {
            return await _context.Radius
                .AnyAsync(r => r.RaioAtendimento == raioAtendimento);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao verificar se raio de atendimento existe: {ex.Message}", ex);
        }
    }
}
