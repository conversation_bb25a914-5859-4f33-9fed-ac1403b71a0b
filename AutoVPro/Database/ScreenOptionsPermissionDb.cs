using System.Linq.Expressions;
using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class ScreenOptionsPermissionDb : IScreenOptionPermissionDb<ScreenOptionPermissionModel>
{
    private readonly AutoProDbContext _context;

    public ScreenOptionsPermissionDb(
        AutoProDbContext context)
    {
        _context = context;
    }
    public async Task<ScreenOptionPermissionModel> SaveAsync(ScreenOptionPermissionModel screenOptionPermission)
    {
        try
        {
            _context.ScreenOptionPermissions.Add(screenOptionPermission);
            await _context.SaveChangesAsync();
            return screenOptionPermission;
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenDb.SaveAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar as telas{ex.Message}");   
        }
    }

    public async Task<List<ScreenOptionPermissionModel>> GetAllAsync(int screenId, int userGroupId)
    {
        try
        {
            List<ScreenOptionPermissionModel> lstScreenOptionPermission = await _context.ScreenOptionPermissions
                .Include(x=>x.ScreenOptionModel)
                .ThenInclude(x=>x.ScreenModel)
                .Where(a =>
                    a.UserGroupId == userGroupId &&
                    a.ScreenOptionModel.ScreenId == screenId).ToListAsync();
            return lstScreenOptionPermission;      
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar as telas{ex.Message}");      
        }
    }

    public async Task<bool> UpdateAsync(int screenOptionId, int userGroupId, bool allow)
    {
        try
        {
            ScreenOptionPermissionModel? screenOptionPermission = _context.ScreenOptionPermissions.Find(screenOptionId);
            if (screenOptionPermission != null)
            {
                screenOptionPermission.Allow = allow;
                _context.ScreenOptionPermissions.Update(screenOptionPermission);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenDb.UpdateAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar as telas{ex.Message}");      
        }
    }

    public async Task<bool> CheckPermissionByScreenOptionIdAsync(int screenOptionId, int userGroupId)
    {
        try
        {
            ScreenOptionPermissionModel? screenOptionPermission = await _context.ScreenOptionPermissions.FindAsync(screenOptionId);
            if (screenOptionPermission != null)
            {
                return true;
            }
            return false;      
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenDb.CheckPermissionByScreenOptionIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar as telas{ex.Message}");      
        }
    }
}