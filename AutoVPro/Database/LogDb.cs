using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;

namespace AutoVPro.Database;

public class LogDb : ILogDb<LogModel>
{
    private readonly AutoProDbContext _context;

    public LogDb(AutoProDbContext context)
    {
        _context = context;
    }

    public async Task<bool> AddAsync(LogModel logModel)
    {
        try
        {
            _context.Log.Add(logModel);
            await _context.SaveChangesAsync();
            return true;
        }
        catch(Exception ex)
        {
            throw new Exception($"Ocorreram erros ao salvar o log.{ex.Message}");
        }
    }
}