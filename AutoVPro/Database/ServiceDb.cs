using System.Linq.Expressions;
using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class ServiceDb : IDatabase<ServiceModel>
{
    private readonly AutoProDbContext _context;
    
    public ServiceDb(AutoProDbContext context)
    {
        _context = context;
    }
    
    public async Task<ServiceModel> SaveAsync(ServiceModel entity)
    {
        try
        {
            entity.CreatedOn = DateTime.Now;
            entity.Guid = Guid.NewGuid().ToString();
            await _context.Services.AddAsync(entity);
            await _context.SaveChangesAsync();
            return entity;
        }
        catch(Exception ex)
        {
            Log.Error($"ServiceDb.SaveAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }
    
    public async Task<ServiceModel> UpdateAsync(ServiceModel entity)
    {
        try
        {
            entity.UpdatedOn = DateTime.Now;
            var service = await _context.Services.FindAsync(entity.Id);
            if (service != null)
                _context.Entry(service).CurrentValues.SetValues(entity);

            await _context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            Log.Error($"ServiceDb.UpdateAsync: {ex.Message}", DateTime.Now);       
            throw new Exception(ex.Message);
        }
    }
    
    public async Task<List<ServiceModel>> GetAllAsync()
    {
        try
        {
            var services = await _context.Services
                .Where(a=>
                    a.Deleted == false 
                    && a.Active == true)
                .ToListAsync();
            return services; 
        }
        catch (Exception ex)
        {
            Log.Error($"ServiceDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);       
        }
    }
    
    public async Task<ServiceModel> GetByIdAsync(Guid guid)
    {
        try
        {
            var service = await _context.Services.FirstOrDefaultAsync(c=>c.Guid == guid.ToString());
            return service;
        }
        catch (Exception ex)
        {
            Log.Error($"ServiceDb.GetByIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);      
        }
    }
    
    public async Task<ServiceModel> DeleteAsync(int id)
    {
        try
        {
            var service = await _context.Services.FindAsync((long)id);
            if (service != null)
            {
                service.Deleted = true;
                service.UpdatedOn = DateTime.Now;
                await _context.SaveChangesAsync();
            }
            return service;
        }
        catch (Exception ex)
        {
            Log.Error($"ServiceDb.DeleteAsync: {ex.Message}", DateTime.Now);       
            throw new Exception(ex.Message);
        }
    }

    /// <summary>
    /// Método otimizado para buscar lista paginada de serviços com filtros
    /// </summary>
    public async Task<PagedResultDTO<ServiceModel>> GetPagedListAsync(PagedRequestDTO request)
    {
        try
        {
            var query = _context.Services
                .Where(s => !s.Deleted);

            // Aplicar filtro de busca
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                query = query.Where(s => 
                    s.Name.ToLower().Contains(searchTerm) ||
                    s.Description.ToLower().Contains(searchTerm)
                );
            }

            // Contar total de registros após aplicar filtros
            var totalCount = await query.CountAsync();

            // Aplicar ordenação
            if (!string.IsNullOrEmpty(request.SortBy))
            {
                query = request.SortBy.ToLower() switch
                {
                    "id" => request.SortDescending ? 
                        query.OrderByDescending(s => s.Id) : 
                        query.OrderBy(s => s.Id),
                    "name" => request.SortDescending ? 
                        query.OrderByDescending(s => s.Name) : 
                        query.OrderBy(s => s.Name),
                    "valueamount" => request.SortDescending ? 
                        query.OrderByDescending(s => s.ValueAmount) : 
                        query.OrderBy(s => s.ValueAmount),
                    "active" => request.SortDescending ? 
                        query.OrderByDescending(s => s.Active) : 
                        query.OrderBy(s => s.Active),
                    "createdon" => request.SortDescending ? 
                        query.OrderByDescending(s => s.CreatedOn) : 
                        query.OrderBy(s => s.CreatedOn),
                    _ => request.SortDescending ? 
                        query.OrderByDescending(s => s.Id) : 
                        query.OrderBy(s => s.Id)
                };
            }
            else
            {
                // Ordenação padrão por ID descendente (mais recentes primeiro)
                query = query.OrderByDescending(s => s.Id);
            }

            // Aplicar paginação
            var skip = (request.PageNumber - 1) * request.PageSize;
            var services = await query
                .Skip(skip)
                .Take(request.PageSize)
                .ToListAsync();

            return new PagedResultDTO<ServiceModel>
            {
                Items = services,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Log.Error($"ServiceDb.GetPagedListAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar serviços paginados: {ex.Message}");
        }
    }
}
