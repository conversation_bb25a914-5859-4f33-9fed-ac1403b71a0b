using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

[Table("Customers")]
public class CustomerModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
    
    public Guid Guid { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Name { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Type { get; set; } // PF ou PJ
    
    [Required]
    [StringLength(255)]
    public string FiscalNumber { get; set; } // CPF ou CNPJ
    
    public DateTime? BirthDate { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Address { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Number { get; set; }
    
    [StringLength(255)]
    public string AddressComplement { get; set; }
    
    [Required]
    public long DistrictId { get; set; }
    
    [Required]
    public long CityId { get; set; }
    
    [Required]
    public long StateId { get; set; }
    
    [Required]
    public bool Active { get; set; }
    
    [Required]
    public bool Deleted { get; set; }
    
    [Required]
    public DateTime CreatedOn { get; set; }
    
    public DateTime UpdatedOn { get; set; }
    
    // Navigation properties
    [ForeignKey("DistrictId")]
    public virtual DistrictModel District { get; set; }
    
    [ForeignKey("CityId")]
    public virtual CityModel City { get; set; }
    
    [ForeignKey("StateId")]
    public virtual StateModel State { get; set; }
    
    // Attendance relationship
    public virtual ICollection<AttendanceModel> Attendances { get; set; }
}