using System.ComponentModel.DataAnnotations;
namespace AutoVPro.Database.Model;

    public class UserGroupModel
    {
        [Key]
        public int Id { get; set; }
        public string Guid { get; set; }
        
        public string NomeGrupoUser { get; set; }
        public bool Active { get; set; }
        public bool Deleted { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime UpdatedOn { get; set; }
        
        public ICollection<UserModel> Users { get; set; }
    }


