using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

public class ItemModel
{
    [Key]
    public long Id { get; set; }

    [Required]
    [Column(TypeName = "varchar(100)")]
    public string Name { get; set; } = string.Empty;

    [Required]
    [Column(TypeName = "varchar(255)")]
    public string Description { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public bool IsDeleted { get; set; } = false;

    public virtual ICollection<SubItemModel> SubItems { get; set; } = new List<SubItemModel>();
}
