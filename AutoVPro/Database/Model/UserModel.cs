using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Database.Model;

public record UserModel
{
    [Key]
    public long Id { get; set; }
    public Guid Guid { get; set; }
    
    [Column(TypeName = "varchar(100)")]
    public string Email { get; set; }
    
    [Column(TypeName = "varchar(100)")]
    public string Password { get; set; }
    public int UserGroupId { get; set; }

    public bool Active { get; set; }
    
    public bool Deleted { get; set; }

    public DateTime? CreatedOn { get; set; }
    
    public DateTime? UpdatedOn { get; set; }
    
    [ForeignKey(nameof(UserGroupId))]
    public virtual UserGroupModel UserGroup { get; set; }
}