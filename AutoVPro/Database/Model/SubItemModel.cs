using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

public class SubItemModel
{
    [Key]
    public long Id { get; set; }

    [Required]
    public long ItemModelId { get; set; }

    [Required]
    [Column(TypeName = "varchar(100)")]
    public string Name { get; set; } = string.Empty;

    [Required]
    [Column(TypeName = "varchar(255)")]
    public string Description { get; set; }

    public bool IsActive { get; set; } = true;

    public bool IsDeleted { get; set; } = false;
    
    [ForeignKey(nameof(ItemModelId))]
    public virtual ItemModel ItemModel { get; set; }
}
