using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

public class ScreenOptionPermissionModel
{
    [Key]
    public int Id { get; set; }
    public int ScreenOptionId { get; set; }
    public int UserGroupId { get; set; }
    public bool Allow { get; set; }
    
    [ForeignKey("ScreenOptionId")]
    public virtual ScreenOptionsModel ScreenOptionModel { get; set; }
    
    [ForeignKey("UserGroupId")]
    public virtual UserGroupModel UserGroup { get; set; }
}