using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

[Table("Attendance")]
public class AttendanceModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
    
    public Guid Guid { get; set; }
    
    [Required]
    public DateTime DateRequest { get; set; }
    
    public DateTime? DateAttendance { get; set; }
    
    public DateTime? DateFinish { get; set; }
    
    [Required]
    public long CustomerId { get; set; }
    
    public long? ProfessionalId { get; set; }
    
    [Required]
    [StringLength(255)]
    public string ReferenceCar { get; set; }
    
    [Required]
    public long BrandId { get; set; }
    
    [Required]
    public long ModelId { get; set; }
    
    [Required]
    [StringLength(255)]
    public string PostalCode { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Address { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Number { get; set; }
    
    [StringLength(255)]
    public string AddressComplement { get; set; }
    
    [Required]
    public long DistrictId { get; set; }
    
    [Required]
    public long CityId { get; set; }
    
    [Required]
    public long StateId { get; set; }
    
    [Required]
    public int Status { get; set; }
    
    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual CustomerModel Customer { get; set; }
    
    [ForeignKey("ProfessionalId")]
    public virtual CautelaristModel Professional { get; set; }
    
    [ForeignKey("BrandId")]
    public virtual BrandModel Brand { get; set; }
    
    [ForeignKey("ModelId")]
    public virtual ModelModel Model { get; set; }
    
    [ForeignKey("DistrictId")]
    public virtual DistrictModel District { get; set; }
    
    [ForeignKey("CityId")]
    public virtual CityModel City { get; set; }
    
    [ForeignKey("StateId")]
    public virtual StateModel State { get; set; }
    
    // Campos adicionais para Service Request
    [StringLength(255)]
    public string? VehicleOwnerName { get; set; }
    
    [StringLength(20)]
    public string? VehicleOwnerPhone { get; set; }
    
    public bool HasCautelarService { get; set; }
    
    public bool HasVistoriaService { get; set; }
    
    [StringLength(500)]
    public string? CustomerNotes { get; set; }
    
    // Campos para endereço alternativo do veículo
    public bool VehicleAtDifferentAddress { get; set; }
    
    [StringLength(10)]
    public string? VehiclePostalCode { get; set; }
    
    [StringLength(255)]
    public string? VehicleAddress { get; set; }
    
    [StringLength(10)]
    public string? VehicleNumber { get; set; }
    
    [StringLength(255)]
    public string? VehicleAddressComplement { get; set; }
    
    public long? VehicleDistrictId { get; set; }
    
    public long? VehicleCityId { get; set; }
    
    public long? VehicleStateId { get; set; }
    
    // Navigation properties para endereço do veículo
    [ForeignKey("VehicleDistrictId")]
    public virtual DistrictModel? VehicleDistrict { get; set; }
    
    [ForeignKey("VehicleCityId")]
    public virtual CityModel? VehicleCity { get; set; }
    
    [ForeignKey("VehicleStateId")]
    public virtual StateModel? VehicleState { get; set; }
    
    // TODO: Future payment relationship
    // public virtual ICollection<AttendancePaymentModel> Payments { get; set; }
}