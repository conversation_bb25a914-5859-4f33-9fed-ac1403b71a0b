using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

public class CautelaristFeeModel
{
    [Key]
    public long Id { get; set; }

    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Fee { get; set; }

    public bool IsActive { get; set; } = true;

    public bool IsDeleted { get; set; } = false;
}
