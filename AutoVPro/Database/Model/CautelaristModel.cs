using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

[Table("Cautelarist")]
public class CautelaristModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; }

    [Required]
    [StringLength(100)]
    public string Cellphone { get; set; }

    [Required]
    [StringLength(200)]
    public string TypeOfContact { get; set; }

    [Required]
    [Column(TypeName = "nvarchar(max)")]
    public string CnhDocumentBase64 { get; set; }

    [Required]
    [Column(TypeName = "nvarchar(max)")]
    public string ProofOfResidenceBase64 { get; set; }

    [Required]
    [Column(TypeName = "nvarchar(max)")]
    public string QualificationsDocumentBase64 { get; set; }

    [Required]
    [Column(TypeName = "nvarchar(max)")]
    public string CnhImageBase64 { get; set; }

    [Required]
    [Column(TypeName = "nvarchar(max)")]
    public string QualificationsImageBase64 { get; set; }

    [Required]
    public bool IsAprove { get; set; } = false;

    [EmailAddress(ErrorMessage = "Email deve ter um formato válido")]
    [StringLength(255, ErrorMessage = "Email deve ter no máximo 255 caracteres")]
    public string? Email { get; set; }

    [StringLength(1000, ErrorMessage = "Observações devem ter no máximo 1000 caracteres")]
    public string? Observacoes { get; set; }

    [Required]
    public bool Origem { get; set; } = false;
}