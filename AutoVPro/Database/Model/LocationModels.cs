using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

[Table("Brands")]
public class BrandModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Brand { get; set; }
    
    // Navigation properties
    public virtual ICollection<ModelModel> Models { get; set; }
    public virtual ICollection<AttendanceModel> Attendances { get; set; }
}

[Table("Models")]
public class ModelModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Model { get; set; }
    
    [Required]
    public long BrandId { get; set; }
    
    // Navigation properties
    [ForeignKey("BrandId")]
    public virtual BrandModel Brand { get; set; }
    
    public virtual ICollection<AttendanceModel> Attendances { get; set; }
}

[Table("Districts")]
public class DistrictModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
    
    [Required]
    [StringLength(255)]
    public string District { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Code { get; set; }
    
    public Guid Guid { get; set; }
    
    [Required]
    public bool Active { get; set; } = true;
    
    [Required]
    public bool Deleted { get; set; } = false;
    
    [Required]
    public DateTime CreatedOn { get; set; } = DateTime.Now;
    
    public DateTime UpdatedOn { get; set; } = DateTime.Now;
    
    // Navigation properties
    public virtual ICollection<CustomerModel> Customers { get; set; }
    public virtual ICollection<AttendanceModel> Attendances { get; set; }
}

[Table("Cities")]
public class CityModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
    
    [Required]
    [StringLength(255)]
    public string City { get; set; }
    
    public Guid Guid { get; set; }
    
    [Required]
    public bool Active { get; set; } = true;
    
    [Required]
    public bool Deleted { get; set; } = false;
    
    [Required]
    public DateTime CreatedOn { get; set; } = DateTime.Now;
    
    public DateTime UpdatedOn { get; set; } = DateTime.Now;
    
    // Navigation properties
    public virtual ICollection<CustomerModel> Customers { get; set; }
    public virtual ICollection<AttendanceModel> Attendances { get; set; }
}

[Table("States")]
public class StateModel
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }
    
    [Required]
    [StringLength(255)]
    public string State { get; set; }
    
    public Guid Guid { get; set; }
    
    [Required]
    public bool Active { get; set; } = true;
    
    [Required]
    public bool Deleted { get; set; } = false;
    
    [Required]
    public DateTime CreatedOn { get; set; } = DateTime.Now;
    
    public DateTime UpdatedOn { get; set; } = DateTime.Now;
    
    // Navigation properties
    public virtual ICollection<CustomerModel> Customers { get; set; }
    public virtual ICollection<AttendanceModel> Attendances { get; set; }
}

