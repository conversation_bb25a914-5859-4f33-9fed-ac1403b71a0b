using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AutoVPro.Database.Model;

public class ServiceModel
{
    [Key]
    public long Id { get; set; }

    public string Guid { get; set; }

    [Column(TypeName = "varchar(255)")]
    public string Name { get; set; }

    [Column(TypeName = "varchar(255)")]
    public string Icon { get; set; }

    [Column(TypeName = "varchar(255)")]
    public string Description { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal ValueAmount { get; set; }

    public bool Active { get; set; }

    public bool Deleted { get; set; }

    public DateTime? CreatedOn { get; set; }

    public DateTime? UpdatedOn { get; set; }
}
