using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Azure.Core.GeoJson;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class ScreenOptionsDb : IScreenOptionsDb<ScreenOptionsModel>
{
    private readonly AutoProDbContext _context;

    public ScreenOptionsDb(
        AutoProDbContext context 
        )
    {
        _context = context;
    }

    public async Task<ScreenOptionsModel> SaveAsync(ScreenOptionsModel screenOptions)
    {
        try
        {
            _context.ScreenOptions.Add(screenOptions);
            await _context.SaveChangesAsync();
            return screenOptions;
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenOptionsDb.SaveAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao salvar a tela{ex.Message}");       
        }
              
    }

    public async Task<List<ScreenOptionsModel>> GetAllAsync(int? screenId)
    {
        try
        {
            List<ScreenOptionsModel> lstScreenOptions = lstScreenOptions = await _context.ScreenOptions.ToListAsync();
            if (screenId != null)
                lstScreenOptions = lstScreenOptions.Where(x => x.ScreenId == screenId).ToList();
            
            return lstScreenOptions;
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenOptionsDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar as telas{ex.Message}");       
        }
    }

    public async Task<bool> DeleteAsync(int id)
    {
        try
        {
            ScreenOptionsModel? screenOptions = await _context.ScreenOptions.FindAsync(id); 
            if (screenOptions != null)
            {
                _context.ScreenOptions.Remove(screenOptions);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;    
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenOptionsDb.DeleteAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao deletar a tela{ex.Message}");       
        }
    }
}