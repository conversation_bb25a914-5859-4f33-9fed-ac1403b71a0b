using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database
{
    public class ItemDb : IItemDatabase
    {
        private readonly AutoProDbContext _context;
        
        public ItemDb(AutoProDbContext context)
        {
            _context = context;
        }
        
        public async Task<ItemModel> SaveAsync(ItemModel entity)
        {
            try
            {
                await _context.Items.AddAsync(entity);
                await _context.SaveChangesAsync();
                return entity;
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.SaveAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao salvar item: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemModel> UpdateAsync(ItemModel entity)
        {
            try
            {
                var existingItem = await _context.Items.FindAsync(entity.Id);
                if (existingItem == null)
                    throw new KeyNotFoundException($"Item com ID {entity.Id} não encontrado");
                
                existingItem.Name = entity.Name;
                existingItem.Description = entity.Description;
                existingItem.IsActive = entity.IsActive;
                existingItem.IsDeleted = entity.IsDeleted;
                
                _context.Items.Update(existingItem);
                await _context.SaveChangesAsync();
                
                return existingItem;
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.UpdateAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao atualizar item: {ex.Message}", ex);
            }
        }
        
        public async Task<List<ItemModel>> GetAllAsync()
        {
            try
            {
                return await _context.Items
                    .Include(i => i.SubItems.Where(si => !si.IsDeleted))
                    .Where(i => !i.IsDeleted)
                    .OrderBy(i => i.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.GetAllAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemModel> GetByIdAsync(Guid guid)
        {
            try
            {
                var item = await _context.Items
                    .Include(i => i.SubItems.Where(si => !si.IsDeleted))
                    .FirstOrDefaultAsync(i => i.Id.ToString() == guid.ToString() && !i.IsDeleted);
                
                if (item == null)
                    throw new KeyNotFoundException($"Item com GUID {guid} não encontrado");
                
                return item;
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.GetByIdAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar item: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemModel> GetByIdAsync(long id)
        {
            try
            {
                var item = await _context.Items
                    .Include(i => i.SubItems.Where(si => !si.IsDeleted))
                    .FirstOrDefaultAsync(i => i.Id == id && !i.IsDeleted);
                
                if (item == null)
                    throw new KeyNotFoundException($"Item com ID {id} não encontrado");
                
                return item;
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.GetByIdAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar item: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemModel> DeleteAsync(int id)
        {
            try
            {
                var item = await _context.Items.FindAsync((long)id);
                if (item == null)
                    throw new KeyNotFoundException($"Item com ID {id} não encontrado");
                
                item.IsDeleted = true;
                item.IsActive = false;
                
                _context.Items.Update(item);
                await _context.SaveChangesAsync();
                
                return item;
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.DeleteAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao deletar item: {ex.Message}", ex);
            }
        }
        
        public async Task<List<ItemModel>> SearchByNameAsync(string name)
        {
            try
            {
                return await _context.Items
                    .Include(i => i.SubItems.Where(si => !si.IsDeleted))
                    .Where(i => i.Name.Contains(name) && !i.IsDeleted)
                    .OrderBy(i => i.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.SearchByNameAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens por nome: {ex.Message}", ex);
            }
        }
        
        public async Task<List<ItemModel>> GetByStatusAsync(bool isActive)
        {
            try
            {
                return await _context.Items
                    .Include(i => i.SubItems.Where(si => !si.IsDeleted))
                    .Where(i => i.IsActive == isActive && !i.IsDeleted)
                    .OrderBy(i => i.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.GetByStatusAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens por status: {ex.Message}", ex);
            }
        }

        public async Task<(List<ItemModel> Items, int TotalCount)> GetPagedAsync(
            int pageNumber = 1,
            int pageSize = 10,
            string? searchTerm = null,
            string? sortBy = null,
            bool sortDescending = false)
        {
            try
            {
                var query = _context.Items
                    .Include(i => i.SubItems.Where(si => !si.IsDeleted))
                    .Where(i => !i.IsDeleted);

                // Aplicar filtro de busca
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(i =>
                        i.Name.Contains(searchTerm) ||
                        (i.Description != null && i.Description.Contains(searchTerm)));
                }

                // Contar total antes da paginação
                var totalCount = await query.CountAsync();

                // Aplicar ordenação
                if (!string.IsNullOrEmpty(sortBy))
                {
                    switch (sortBy.ToLower())
                    {
                        case "name":
                            query = sortDescending ? query.OrderByDescending(i => i.Name) : query.OrderBy(i => i.Name);
                            break;
                        case "description":
                            query = sortDescending ? query.OrderByDescending(i => i.Description) : query.OrderBy(i => i.Description);
                            break;
                        case "isactive":
                            query = sortDescending ? query.OrderByDescending(i => i.IsActive) : query.OrderBy(i => i.IsActive);
                            break;
                        default:
                            query = query.OrderBy(i => i.Name);
                            break;
                    }
                }
                else
                {
                    query = query.OrderBy(i => i.Name);
                }

                // Aplicar paginação
                var items = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemDb.GetPagedAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens paginados: {ex.Message}", ex);
            }
        }
    }
}
