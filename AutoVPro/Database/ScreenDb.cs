using AutoMapper;
using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class ScreenDb : IScreenDb<ScreensModel>
{
    private readonly AutoProDbContext _context;
    
    public ScreenDb(
        AutoProDbContext context,
        IMapper mapper)
    {
            _context = context; 
    }
    public async Task<ScreensModel> SaveAsync(ScreensModel screen)
    {
        try
        {
            _context.Screens.Add(screen);
            await _context.SaveChangesAsync();
            return screen;          
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenDb.SaveAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao salvar a tela{ex.Message}");
        }
    }

    public async  Task<List<ScreensModel>> GetAllAsync()
    {
        try
        { 
            List<ScreensModel> lstModel = new List<ScreensModel>();
            lstModel = await _context.Screens.ToListAsync();
            return lstModel;            
        }
        catch (Exception ex)
        {
            Log.Error($"ScreenDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar as telas{ex.Message}");       
        }
    }
}