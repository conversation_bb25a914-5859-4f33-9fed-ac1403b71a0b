using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class UserDb : IDatabase<UserModel>
{
    private readonly AutoProDbContext _context;
    
    public UserDb(AutoProDbContext context)
    {
            _context = context;
    }
    
    public async Task<UserModel> SaveAsync(UserModel entity)
    {
        try
        {
            entity.CreatedOn = DateTime.Now;
            await _context.Users.AddAsync(entity);
            await _context.SaveChangesAsync();
            return entity;
        }
        catch(Exception ex)
        {
            Log.Error($"UserDb.SaveAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);       
        }
    }
    
    public async Task<UserModel> UpdateAsync(UserModel entity)
    {
        try
        {
            entity.UpdatedOn = DateTime.Now;
            var user = await _context.Users.FindAsync(entity.Id);
            if (user != null)
                _context.Entry(user).CurrentValues.SetValues(entity);

            await _context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            Log.Error($"UserDb.UpdateAsync: {ex.Message}", DateTime.Now);       
            throw new Exception(ex.Message);   
        }
    }
    
    public async Task<List<UserModel>> GetAllAsync()
    {
        try
        {
            var user = await _context.Users
                .Where(a=>
                    a.Deleted == false)
                .ToListAsync();
            return user;
        }
        catch (Exception ex)
        {
            Log.Error($"UserDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);      
        }
    }
    
    public async Task<UserModel> GetByIdAsync(Guid guid)
    {
        try
        {
            var user = await _context.Users.FirstOrDefaultAsync(c=>c.Guid == guid);
            return user;       
        }
        catch (Exception ex)
        {
            Log.Error($"UserDb.GetByIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);     
        }
    }

    public async Task<UserModel> DeleteAsync(int id)
    {
        try
        {
            var user = await _context.Users
                                            .FirstOrDefaultAsync(a=>a.Id == id);
            if (user != null)
                user.Deleted = true;

            await _context.SaveChangesAsync();
            return user;
        }
        catch (Exception ex)
        {
            Log.Error($"UserDb.GetByIdAsync: {ex.Message}", DateTime.Now);
            throw;
        }
    }

    /// <summary>
    /// Método otimizado para buscar lista paginada de usuários com filtros
    /// </summary>
    public async Task<PagedResultDTO<UserModel>> GetPagedListAsync(PagedRequestDTO request)
    {
        try
        {
            var query = _context.Users
                .Include(u => u.UserGroup)
                .Where(u => !u.Deleted);

            // Aplicar filtro de busca
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                query = query.Where(u => 
                    u.Email.ToLower().Contains(searchTerm) ||
                    (u.UserGroup != null && u.UserGroup.NomeGrupoUser.ToLower().Contains(searchTerm))
                );
            }

            // Contar total de registros após aplicar filtros
            var totalCount = await query.CountAsync();

            // Aplicar ordenação
            if (!string.IsNullOrEmpty(request.SortBy))
            {
                query = request.SortBy.ToLower() switch
                {
                    "id" => request.SortDescending ? 
                        query.OrderByDescending(u => u.Id) : 
                        query.OrderBy(u => u.Id),
                    "email" => request.SortDescending ? 
                        query.OrderByDescending(u => u.Email) : 
                        query.OrderBy(u => u.Email),
                    "active" => request.SortDescending ? 
                        query.OrderByDescending(u => u.Active) : 
                        query.OrderBy(u => u.Active),
                    "usergroup" => request.SortDescending ? 
                        query.OrderByDescending(u => u.UserGroup.NomeGrupoUser) : 
                        query.OrderBy(u => u.UserGroup.NomeGrupoUser),
                    "createdon" => request.SortDescending ? 
                        query.OrderByDescending(u => u.CreatedOn) : 
                        query.OrderBy(u => u.CreatedOn),
                    _ => request.SortDescending ? 
                        query.OrderByDescending(u => u.Id) : 
                        query.OrderBy(u => u.Id)
                };
            }
            else
            {
                // Ordenação padrão por ID descendente (mais recentes primeiro)
                query = query.OrderByDescending(u => u.Id);
            }

            // Aplicar paginação
            var skip = (request.PageNumber - 1) * request.PageSize;
            var users = await query
                .Skip(skip)
                .Take(request.PageSize)
                .ToListAsync();

            return new PagedResultDTO<UserModel>
            {
                Items = users,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Log.Error($"UserDb.GetPagedListAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar usuários paginados: {ex.Message}");
        }
    }
}