
using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class CautelaristDb : IDatabase<CautelaristModel>
{
    private readonly AutoProDbContext  _context;

    public CautelaristDb(AutoProDbContext context){
        _context = context;
    }

    public async Task<CautelaristModel> SaveAsync(CautelaristModel entity){

        try
        {
            await _context.Cautelarist.AddAsync(entity);
            await _context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristDb.SaveAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao salvar cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristModel> UpdateAsync(CautelaristModel entity)
    {
        try
        {
            var existingCautelarist = await _context.Cautelarist.FindAsync(entity.Id);
            if (existingCautelarist == null)
            {
                throw new Exception("Cautelarista não encontrado");
            }

            // Atualizar propriedades
            existingCautelarist.Name = entity.Name;
            existingCautelarist.Cellphone = entity.Cellphone;
            existingCautelarist.TypeOfContact = entity.TypeOfContact;
            existingCautelarist.Email = entity.Email;
            existingCautelarist.Observacoes = entity.Observacoes;
            existingCautelarist.IsAprove = entity.IsAprove;
            existingCautelarist.Origem = entity.Origem;
            existingCautelarist.CnhDocumentBase64 = entity.CnhDocumentBase64;
            existingCautelarist.ProofOfResidenceBase64 = entity.ProofOfResidenceBase64;
            existingCautelarist.QualificationsDocumentBase64 = entity.QualificationsDocumentBase64;
            existingCautelarist.CnhImageBase64 = entity.CnhImageBase64;
            existingCautelarist.QualificationsImageBase64 = entity.QualificationsImageBase64;

            _context.Cautelarist.Update(existingCautelarist);
            await _context.SaveChangesAsync();

            return existingCautelarist;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao atualizar cautelarista: {ex.Message}", ex);
        }
    }



    public async Task<CautelaristModel> GetByIdAsync(Guid guid)
    {
        try
        {
            // Para manter compatibilidade com interface genérica, usar Id como long
            var cautelarist = await _context.Cautelarist.FirstOrDefaultAsync(c => c.Id.ToString() == guid.ToString());
            if (cautelarist == null)
            {
                throw new Exception("Cautelarista não encontrado");
            }
            return cautelarist;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar cautelarista: {ex.Message}", ex);
        }
    }

    // Método específico do domínio usando long id
    public async Task<CautelaristModel> GetByIdAsync(long id)
    {
        try
        {
            var cautelarist = await _context.Cautelarist.FindAsync(id);
            if (cautelarist == null)
            {
                throw new Exception("Cautelarista não encontrado");
            }
            return cautelarist;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristModel> DeleteAsync(int id)
    {
        try
        {
            var cautelarist = await _context.Cautelarist.FindAsync((long)id);
            if (cautelarist == null)
            {
                throw new Exception("Cautelarista não encontrado");
            }

            _context.Cautelarist.Remove(cautelarist);
            await _context.SaveChangesAsync();

            return cautelarist;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao deletar cautelarista: {ex.Message}", ex);
        }
    }



    public async Task<List<CautelaristModel>> GetAllAsync()
    {
        var cautelarist = await _context.Cautelarist.ToListAsync();
        return cautelarist;
    }

    /// <summary>
    /// Busca cautelaristas com paginação e projeção otimizada (sem campos Base64)
    /// </summary>
    public async Task<(List<CautelaristModel> Items, int TotalCount)> GetPagedAsync(
        int pageNumber = 1, 
        int pageSize = 10, 
        string? searchTerm = null,
        string? sortBy = null,
        bool sortDescending = false)
    {
        var query = _context.Cautelarist.AsQueryable();

        // Aplicar filtro de busca
        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(c => 
                c.Name.Contains(searchTerm) ||
                c.Email.Contains(searchTerm) ||
                c.Cellphone.Contains(searchTerm));
        }

        // Contar total antes da paginação
        var totalCount = await query.CountAsync();

        // Aplicar ordenação
        if (!string.IsNullOrEmpty(sortBy))
        {
            query = sortBy.ToLower() switch
            {
                "name" => sortDescending ? query.OrderByDescending(c => c.Name) : query.OrderBy(c => c.Name),
                "email" => sortDescending ? query.OrderByDescending(c => c.Email) : query.OrderBy(c => c.Email),
                "cellphone" => sortDescending ? query.OrderByDescending(c => c.Cellphone) : query.OrderBy(c => c.Cellphone),
                "isapprove" => sortDescending ? query.OrderByDescending(c => c.IsAprove) : query.OrderBy(c => c.IsAprove),
                _ => query.OrderBy(c => c.Id)
            };
        }
        else
        {
            query = query.OrderBy(c => c.Id);
        }

        // Aplicar paginação COM PROJEÇÃO para excluir campos Base64 pesados
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .Select(c => new CautelaristModel
            {
                Id = c.Id,
                Name = c.Name,
                Cellphone = c.Cellphone,
                TypeOfContact = c.TypeOfContact,
                Email = c.Email,
                Observacoes = c.Observacoes,
                IsAprove = c.IsAprove,
                Origem = c.Origem
                // Excluindo propositalmente os campos Base64 para melhor performance:
                // CnhDocumentBase64, ProofOfResidenceBase64, QualificationsDocumentBase64,
                // CnhImageBase64, QualificationsImageBase64
            })
            .ToListAsync();

        return (items, totalCount);
    }

    /// <summary>
    /// Busca apenas os documentos Base64 de um cautelarista específico
    /// </summary>
    public async Task<CautelaristModel?> GetDocumentsAsync(long id)
    {
        try
        {
            var documents = await _context.Cautelarist
                .Where(c => c.Id == id)
                .Select(c => new CautelaristModel
                {
                    Id = c.Id,
                    CnhDocumentBase64 = c.CnhDocumentBase64,
                    ProofOfResidenceBase64 = c.ProofOfResidenceBase64,
                    QualificationsDocumentBase64 = c.QualificationsDocumentBase64,
                    CnhImageBase64 = c.CnhImageBase64,
                    QualificationsImageBase64 = c.QualificationsImageBase64
                })
                .FirstOrDefaultAsync();

            return documents;
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristDb.GetDocumentsAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao buscar documentos do cautelarista: {ex.Message}", ex);
        }
    }

    // Método específico do domínio de negócio - mantido além da interface genérica
    public async Task<CautelaristModel> UpdateApprovalStatusAsync(long id, bool isApprove)
    {
        try
        {
            var cautelarist = await _context.Cautelarist.FindAsync(id);
            if (cautelarist == null)
            {
                throw new Exception("Cautelarista não encontrado");
            }

            cautelarist.IsAprove = isApprove;
            await _context.SaveChangesAsync();

            return cautelarist;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao atualizar status de aprovação: {ex.Message}", ex);
        }
    }
}