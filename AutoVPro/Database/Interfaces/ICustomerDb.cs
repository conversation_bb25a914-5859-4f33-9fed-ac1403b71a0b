using AutoVPro.Database.Model;

namespace AutoVPro.Database.Interfaces;

public interface ICustomerDb : IDatabase<CustomerModel>
{
    Task<CustomerModel?> GetByCpfAsync(string cpf);
    Task<CustomerModel?> GetByCnpjAsync(string cnpj);
    Task<List<CustomerModel>> GetByStatusAsync(bool active);
    Task<List<CustomerModel>> SearchByNameAsync(string name);
    Task<List<CustomerModel>> GetPagedAsync(int page, int pageSize, string? search, bool? active);
    Task<int> GetTotalCountAsync(string? search = null, bool? active = null);
}