namespace AutoVPro.Database.Interfaces;

public interface IScreenOptionPermissionDb<ScreenOptionPermissionModel> where ScreenOptionPermissionModel : class
{
    public Task<ScreenOptionPermissionModel> SaveAsync(ScreenOptionPermissionModel screenOptionPermission);
    public Task<List<ScreenOptionPermissionModel>> GetAllAsync(int screenId, int userGroupId);
    public Task<bool> UpdateAsync(int screenOptionId, int userGroupId, bool allow);

    public Task<bool> CheckPermissionByScreenOptionIdAsync(int screenOptionId, int userGroupId);
}