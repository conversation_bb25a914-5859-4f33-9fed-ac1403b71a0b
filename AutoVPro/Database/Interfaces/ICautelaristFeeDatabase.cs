using AutoVPro.Database.Model;

namespace AutoVPro.Database.Interfaces;

public interface ICautelaristFeeDatabase
{
    Task<CautelaristFeeModel> SaveAsync(CautelaristFeeModel entity);
    Task<CautelaristFeeModel> UpdateAsync(CautelaristFeeModel entity);
    Task<List<CautelaristFeeModel>> GetAllAsync();
    Task<CautelaristFeeModel> GetByIdAsync(long id);
    Task<CautelaristFeeModel> DeleteAsync(long id);
    Task<List<CautelaristFeeModel>> GetActiveFeesAsync();
}
