using AutoVPro.Database.Model;

namespace AutoVPro.Database.Interfaces;

public interface ICautelaristDatabase
{
    Task<CautelaristModel> SaveAsync(CautelaristModel entity);
    Task<CautelaristModel> UpdateAsync(CautelaristModel entity);
    Task<List<CautelaristModel>> GetAllAsync();
    Task<CautelaristModel> GetByIdAsync(long id);
    Task<CautelaristModel> DeleteAsync(int id);
    Task<CautelaristModel> UpdateApprovalStatusAsync(long id, bool isApprove);
}
