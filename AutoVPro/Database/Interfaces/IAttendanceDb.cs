using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Database.Interfaces;

public interface IAttendanceDb : IDatabase<AttendanceModel>
{
    Task<List<AttendanceModel>> GetByCustomerIdAsync(int customerId);
    Task<List<AttendanceModel>> GetByStatusAsync(int status);
    Task<List<AttendanceModel>> GetByProfessionalIdAsync(int professionalId);
    Task<AttendanceModel> UpdateStatusAsync(long id, int newStatus);
    Task<AttendanceModel> AssignProfessionalAsync(long id, int professionalId);
    Task<PagedResultDTO<AttendanceListDTO>> GetPagedListAsync(AttendancePagedRequestDTO request);
}