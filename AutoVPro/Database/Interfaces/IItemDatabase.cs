using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Database.Interfaces
{
    public interface IItemDatabase : IDatabase<ItemModel>
    {
        Task<ItemModel> GetByIdAsync(long id);
        Task<List<ItemModel>> SearchByNameAsync(string name);
        Task<List<ItemModel>> GetByStatusAsync(bool isActive);
        Task<(List<ItemModel> Items, int TotalCount)> GetPagedAsync(
            int pageNumber = 1,
            int pageSize = 10,
            string? searchTerm = null,
            string? sortBy = null,
            bool sortDescending = false);
    }
}
