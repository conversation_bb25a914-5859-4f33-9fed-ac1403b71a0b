using AutoVPro.Database.Model;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Database.AutovproContext;

public class AutoProDbContext : DbContext
{
    public AutoProDbContext(DbContextOptions<AutoProDbContext> options) 
    :base(options)
    {
        
    }

    public DbSet<UserModel> Users { get; set; }
    public DbSet<ServiceModel> Services { get; set; }
    public DbSet<LogModel> Log { get; set; }
    public DbSet<UserGroupModel> UserGroups { get; set; }    
    public DbSet<ScreensModel>  Screens { get; set; }
    public DbSet<ScreenOptionsModel> ScreenOptions { get; set; }
    public DbSet<ScreenOptionPermissionModel> ScreenOptionPermissions { get; set; }

    public DbSet<CautelaristModel> Cautelarist { get; set;}
    public DbSet<CautelaristFeeModel> CautelaristFees { get; set;}
    public DbSet<RadiusModel> Radius { get; set;}
    public DbSet<ItemModel> Items { get; set;}
    public DbSet<SubItemModel> SubItems { get; set;}
    public DbSet<AttendanceModel> Attendances { get; set;}
    public DbSet<CustomerModel> Customers { get; set;}
    public DbSet<BrandModel> Brands { get; set;}
    public DbSet<ModelModel> Models { get; set;}
    public DbSet<StateModel> States { get; set;}
    public DbSet<CityModel> Cities { get; set;}
    public DbSet<DistrictModel> Districts { get; set;}

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuração do relacionamento Item -> SubItem
        modelBuilder.Entity<SubItemModel>()
            .HasOne(si => si.ItemModel)
            .WithMany(i => i.SubItems)
            .HasForeignKey(si => si.ItemModelId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configuração dos relacionamentos de AttendanceModel
        modelBuilder.Entity<AttendanceModel>()
            .HasOne(a => a.City)
            .WithMany(c => c.Attendances)
            .HasForeignKey(a => a.CityId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<AttendanceModel>()
            .HasOne(a => a.State)
            .WithMany(s => s.Attendances)
            .HasForeignKey(a => a.StateId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<AttendanceModel>()
            .HasOne(a => a.District)
            .WithMany(d => d.Attendances)
            .HasForeignKey(a => a.DistrictId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<AttendanceModel>()
            .HasOne(a => a.Brand)
            .WithMany(b => b.Attendances)
            .HasForeignKey(a => a.BrandId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<AttendanceModel>()
            .HasOne(a => a.Model)
            .WithMany(m => m.Attendances)
            .HasForeignKey(a => a.ModelId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configuração do relacionamento Model -> Brand
        modelBuilder.Entity<ModelModel>()
            .HasOne(m => m.Brand)
            .WithMany(b => b.Models)
            .HasForeignKey(m => m.BrandId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}