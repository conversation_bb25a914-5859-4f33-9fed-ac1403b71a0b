using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class CautelaristFeeDb : ICautelaristFeeDatabase
{
    private readonly AutoProDbContext _context;

    public CautelaristFeeDb(AutoProDbContext context)
    {
        _context = context;
    }

    public async Task<CautelaristFeeModel> SaveAsync(CautelaristFeeModel entity)
    {
        try
        {
            await _context.CautelaristFees.AddAsync(entity);
            await _context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeDb.SaveAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao salvar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristFeeModel> UpdateAsync(CautelaristFeeModel entity)
    {
        try
        {
            var existingFee = await _context.CautelaristFees.FindAsync(entity.Id);
            if (existingFee != null)
            {
                _context.Entry(existingFee).CurrentValues.SetValues(entity);
                await _context.SaveChangesAsync();
                return entity;
            }
            throw new Exception("Taxa do cautelarista não encontrada");
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeDb.UpdateAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao atualizar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<List<CautelaristFeeModel>> GetAllAsync()
    {
        try
        {
            return await _context.CautelaristFees
                .Where(cf => !cf.IsDeleted)
                .OrderByDescending(cf => cf.Id)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeDb.GetAllAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao buscar taxas dos cautelaristas: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristFeeModel> GetByIdAsync(long id)
    {
        try
        {
            var fee = await _context.CautelaristFees
                .FirstOrDefaultAsync(cf => cf.Id == id && !cf.IsDeleted);

            if (fee == null)
                throw new Exception("Taxa do cautelarista não encontrada");

            return fee;
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeDb.GetByIdAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao buscar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristFeeModel> DeleteAsync(long id)
    {
        try
        {
            var fee = await _context.CautelaristFees.FindAsync(id);
            if (fee != null)
            {
                fee.IsDeleted = true;
                await _context.SaveChangesAsync();
                return fee;
            }
            throw new Exception("Taxa do cautelarista não encontrada");
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeDb.DeleteAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao deletar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<List<CautelaristFeeModel>> GetActiveFeesAsync()
    {
        try
        {
            return await _context.CautelaristFees
                .Where(cf => cf.IsActive && !cf.IsDeleted)
                .OrderByDescending(cf => cf.Id)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeDb.GetActiveFeesAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao buscar taxas ativas: {ex.Message}", ex);
        }
    }
}
