using System.Linq.Expressions;
using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class UserGroupDb : IDatabase<UserGroupModel>
{
    private readonly AutoProDbContext _context;
    
    public UserGroupDb(AutoProDbContext context)
    {
            _context = context;
    }
    
    public async Task<UserGroupModel> SaveAsync(UserGroupModel entity)
    {
        try
        {
            entity.CreatedOn = DateTime.Now;
            await _context.UserGroups.AddAsync(entity);
            await _context.SaveChangesAsync();
            return entity;
        }
        catch(Exception ex)
        {
            Log.Error($"UserGroupDb.SaveAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);       
        }
    }
    
    public async Task<UserGroupModel> UpdateAsync(UserGroupModel entity)
    {
        try
        {
            entity.UpdatedOn = DateTime.Now;
            var userGroup = await _context.UserGroups.FindAsync(entity.Id);
            if (userGroup != null)
                _context.Entry(userGroup).CurrentValues.SetValues(entity);

            await _context.SaveChangesAsync();
            return entity;
        }
        catch (Exception ex)
        {
            Log.Error($"UserGroupDb.UpdateAsync: {ex.Message}", DateTime.Now);       
            throw new Exception(ex.Message);   
        }
    }
    
    public async Task<List<UserGroupModel>> GetAllAsync()
    {
        try
        {
            var userGroup = await _context.UserGroups
                .Where(a=>
                    a.Deleted == false 
                    && a.Active == true)
                .ToListAsync();
            return userGroup;       
        }
        catch (Exception ex)
        {
            Log.Error($"UserGroupDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);      
        }
    }
    
    public async Task<UserGroupModel> GetByIdAsync(Guid guid)
    {
        try
        {
            var userGroup = await _context.UserGroups.FirstOrDefaultAsync(c=>c.Guid == guid.ToString());
            return userGroup;       
        }
        catch (Exception ex)
        {
            Log.Error($"UserGroupDb.GetByIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);     
        }
        
    }

    public async Task<UserGroupModel> DeleteAsync(int id)
    {
        try
        {
            var userGroup = await _context.UserGroups
                                            .FirstOrDefaultAsync(a=>a.Id == id);
            if (userGroup != null)
                userGroup.Deleted = true;

            await _context.SaveChangesAsync();
            return userGroup;
        }
        catch (Exception e)
        {
            Log.Error($"UserGroupDb.GetByIdAsync: {e.Message}", DateTime.Now);       
            throw;
        }
    }

    /// <summary>
    /// Método otimizado para buscar lista paginada de grupos de usuário com filtros
    /// </summary>
    public async Task<PagedResultDTO<UserGroupModel>> GetPagedListAsync(PagedRequestDTO request)
    {
        try
        {
            var query = _context.UserGroups
                .Where(ug => !ug.Deleted);

            // Aplicar filtro de busca
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                query = query.Where(ug => 
                    ug.NomeGrupoUser.ToLower().Contains(searchTerm)
                );
            }

            // Contar total de registros após aplicar filtros
            var totalCount = await query.CountAsync();

            // Aplicar ordenação
            if (!string.IsNullOrEmpty(request.SortBy))
            {
                query = request.SortBy.ToLower() switch
                {
                    "id" => request.SortDescending ? 
                        query.OrderByDescending(ug => ug.Id) : 
                        query.OrderBy(ug => ug.Id),
                    "nomegrupouser" => request.SortDescending ? 
                        query.OrderByDescending(ug => ug.NomeGrupoUser) : 
                        query.OrderBy(ug => ug.NomeGrupoUser),
                    "active" => request.SortDescending ? 
                        query.OrderByDescending(ug => ug.Active) : 
                        query.OrderBy(ug => ug.Active),
                    "createdon" => request.SortDescending ? 
                        query.OrderByDescending(ug => ug.CreatedOn) : 
                        query.OrderBy(ug => ug.CreatedOn),
                    _ => request.SortDescending ? 
                        query.OrderByDescending(ug => ug.Id) : 
                        query.OrderBy(ug => ug.Id)
                };
            }
            else
            {
                // Ordenação padrão por ID descendente (mais recentes primeiro)
                query = query.OrderByDescending(ug => ug.Id);
            }

            // Aplicar paginação
            var skip = (request.PageNumber - 1) * request.PageSize;
            var userGroups = await query
                .Skip(skip)
                .Take(request.PageSize)
                .ToListAsync();

            return new PagedResultDTO<UserGroupModel>
            {
                Items = userGroups,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Log.Error($"UserGroupDb.GetPagedListAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar grupos de usuário paginados: {ex.Message}");
        }
    }
}