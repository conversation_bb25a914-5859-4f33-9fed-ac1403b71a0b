using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class AttendanceDb : IAttendanceDb
{
    private readonly AutoProDbContext _context;
    
    public AttendanceDb(AutoProDbContext context)
    {
        _context = context;
    }
    
    public async Task<AttendanceModel> SaveAsync(AttendanceModel entity)
    {
        try
        {
            entity.Guid = Guid.NewGuid();
            entity.DateRequest = DateTime.Now;
            entity.Status = (int)AttendanceStatus.Requested;
            
            await _context.Attendances.AddAsync(entity);
            await _context.SaveChangesAsync();
            
            // Reload with includes
            var savedAttendance = await _context.Attendances
                .FirstAsync(a => a.Id == entity.Id);
                
            return savedAttendance;
        }
        catch(Exception ex)
        {
            Log.Error($"AttendanceDb.SaveAsync: {ex.ToString()}", DateTime.Now);
            throw new Exception($"Error saving attendance: {ex.Message}. Inner: {ex.InnerException?.Message}");       
        }
    }
    
    public async Task<AttendanceModel> UpdateAsync(AttendanceModel entity)
    {
        try
        {
            var attendance = await _context.Attendances.FindAsync(entity.Id);
            if (attendance != null)
            {
                // Update only allowed fields
                attendance.DateAttendance = entity.DateAttendance;
                attendance.DateFinish = entity.DateFinish;
                attendance.ProfessionalId = entity.ProfessionalId;
                attendance.Status = entity.Status;
                attendance.ReferenceCar = entity.ReferenceCar;
                attendance.BrandId = entity.BrandId;
                attendance.ModelId = entity.ModelId;
                attendance.PostalCode = entity.PostalCode;
                attendance.Address = entity.Address;
                attendance.Number = entity.Number;
                attendance.AddressComplement = entity.AddressComplement;
                attendance.DistrictId = entity.DistrictId;
                attendance.CityId = entity.CityId;
                attendance.StateId = entity.StateId;
            }

            await _context.SaveChangesAsync();
            
            // Reload with includes
            var updatedAttendance = await _context.Attendances
                .FirstAsync(a => a.Id == entity.Id);
                
            return updatedAttendance;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.UpdateAsync: {ex.Message}", DateTime.Now);       
            throw new Exception(ex.Message);   
        }
    }
    
    public async Task<List<AttendanceModel>> GetAllAsync()
    {
        try
        {
            var attendances = await _context.Attendances
                .Include(a => a.Customer)
                .Include(a => a.Professional)
                .Include(a => a.Brand)
                .Include(a => a.Model)
                .Include(a => a.District)
                .Include(a => a.City)
                .Include(a => a.State)
                .Include(a => a.VehicleDistrict)
                .Include(a => a.VehicleCity)
                .Include(a => a.VehicleState)
                .Where(a => a.Status != (int)AttendanceStatus.Cancelled)
                .OrderByDescending(a => a.DateRequest)
                .ToListAsync();
                
            return attendances;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);      
        }
    }
    
    public async Task<AttendanceModel> GetByIdAsync(Guid guid)
    {
        try
        {
            var attendance = await _context.Attendances
                .Include(a => a.Customer)
                .Include(a => a.Professional)
                .Include(a => a.Brand)
                .Include(a => a.Model)
                .Include(a => a.District)
                .Include(a => a.City)
                .Include(a => a.State)
                .Include(a => a.VehicleDistrict)
                .Include(a => a.VehicleCity)
                .Include(a => a.VehicleState)
                .FirstOrDefaultAsync(a => a.Guid == guid);
                
            return attendance;       
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.GetByIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);     
        }
    }

    public async Task<AttendanceModel> DeleteAsync(int id)
    {
        try
        {
            var attendance = await _context.Attendances
                .FirstOrDefaultAsync(a => a.Id == id);
                
            if (attendance != null)
            {
                // Soft delete - apenas marca como cancelado
                attendance.Status = (int)AttendanceStatus.Cancelled;
            }

            await _context.SaveChangesAsync();
            return attendance;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.DeleteAsync: {ex.Message}", DateTime.Now);
            throw;
        }
    }
    
    // Additional methods specific to Attendance
    public async Task<List<AttendanceModel>> GetByCustomerIdAsync(int customerId)
    {
        try
        {
            var attendances = await _context.Attendances
                .Include(a => a.Customer)
                .Include(a => a.Professional)
                .Include(a => a.Brand)
                .Include(a => a.Model)
                .Include(a => a.District)
                .Include(a => a.City)
                .Include(a => a.State)
                .Include(a => a.VehicleDistrict)
                .Include(a => a.VehicleCity)
                .Include(a => a.VehicleState)
                .Where(a => a.CustomerId == customerId)
                .OrderByDescending(a => a.DateRequest)
                .ToListAsync();

            return attendances;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.GetByCustomerIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<List<AttendanceModel>> GetByStatusAsync(int status)
    {
        try
        {
            var attendances = await _context.Attendances
                .Include(a => a.Customer)
                .Include(a => a.Professional)
                .Include(a => a.Brand)
                .Include(a => a.Model)
                .Include(a => a.District)
                .Include(a => a.City)
                .Include(a => a.State)
                .Include(a => a.VehicleDistrict)
                .Include(a => a.VehicleCity)
                .Include(a => a.VehicleState)
                .Where(a => a.Status == status)
                .OrderByDescending(a => a.DateRequest)
                .ToListAsync();

            return attendances;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.GetByStatusAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<List<AttendanceModel>> GetByProfessionalIdAsync(int professionalId)
    {
        try
        {
            var attendances = await _context.Attendances
                .Include(a => a.Customer)
                .Include(a => a.Professional)
                .Include(a => a.Brand)
                .Include(a => a.Model)
                .Include(a => a.District)
                .Include(a => a.City)
                .Include(a => a.State)
                .Include(a => a.VehicleDistrict)
                .Include(a => a.VehicleCity)
                .Include(a => a.VehicleState)
                .Where(a => a.ProfessionalId == professionalId)
                .OrderByDescending(a => a.DateRequest)
                .ToListAsync();

            return attendances;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.GetByProfessionalIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<AttendanceModel> UpdateStatusAsync(long id, int newStatus)
    {
        try
        {
            var attendance = await _context.Attendances.FindAsync(id);
            if (attendance == null)
                throw new KeyNotFoundException($"Attendance with id {id} not found");

            attendance.Status = newStatus;
            
            // Update dates based on status
            if (newStatus == (int)AttendanceStatus.InProgress && !attendance.DateAttendance.HasValue)
            {
                attendance.DateAttendance = DateTime.Now;
            }
            else if (newStatus == (int)AttendanceStatus.Completed && !attendance.DateFinish.HasValue)
            {
                attendance.DateFinish = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            // Reload with includes
            var updatedAttendance = await _context.Attendances
                .FirstAsync(a => a.Id == attendance.Id);

            return updatedAttendance;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.UpdateStatusAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<AttendanceModel> AssignProfessionalAsync(long id, int professionalId)
    {
        try
        {
            var attendance = await _context.Attendances.FindAsync(id);
            if (attendance == null)
                throw new KeyNotFoundException($"Attendance with id {id} not found");

            attendance.ProfessionalId = professionalId;
            
            // Auto update status to InProgress if still Requested
            if (attendance.Status == (int)AttendanceStatus.Requested)
            {
                attendance.Status = (int)AttendanceStatus.InProgress;
                attendance.DateAttendance = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            // Reload with includes
            var updatedAttendance = await _context.Attendances
                .FirstAsync(a => a.Id == attendance.Id);

            return updatedAttendance;
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.AssignProfessionalAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    /// <summary>
    /// Método otimizado para buscar lista paginada de atendimentos com filtros
    /// Usa joins otimizados em vez de Includes pesados para melhor performance
    /// </summary>
    public async Task<PagedResultDTO<AttendanceListDTO>> GetPagedListAsync(AttendancePagedRequestDTO request)
    {
        try
        {
            // Validação de entrada
            if (request.PageSize > 100)
                request.PageSize = 100;
            
            if (request.PageNumber < 1)
                request.PageNumber = 1;

            // Query base com joins otimizados
            var query = from a in _context.Attendances
                        join c in _context.Customers on a.CustomerId equals c.Id
                        join p in _context.Cautelarist on a.ProfessionalId equals p.Id into professionals
                        from prof in professionals.DefaultIfEmpty()
                        join b in _context.Brands on a.BrandId equals b.Id
                        join m in _context.Models on a.ModelId equals m.Id
                        join d in _context.Districts on a.DistrictId equals d.Id
                        join city in _context.Cities on a.CityId equals city.Id
                        join state in _context.States on a.StateId equals state.Id
                        where a.Status != (int)AttendanceStatus.Cancelled
                        select new AttendanceListDTO
                        {
                            Id = a.Id,
                            Guid = a.Guid,
                            DateRequest = a.DateRequest,
                            DateAttendance = a.DateAttendance,
                            DateFinish = a.DateFinish,
                            Status = a.Status,
                            
                            // Dados do cliente
                            CustomerId = c.Id,
                            CustomerName = c.Name,
                            CustomerType = c.Type,
                            CustomerFiscalNumber = c.FiscalNumber,
                            
                            // Dados do profissional (pode ser null)
                            ProfessionalId = prof != null ? prof.Id : null,
                            ProfessionalName = prof != null ? prof.Name : null,
                            
                            // Informações do veículo
                            ReferenceCar = a.ReferenceCar,
                            BrandId = b.Id,
                            BrandName = b.Brand,
                            ModelId = m.Id,
                            ModelName = m.Model,
                            
                            // Serviços
                            HasCautelarService = a.HasCautelarService,
                            HasVistoriaService = a.HasVistoriaService,
                            
                            // Endereço principal
                            Address = a.Address,
                            Number = a.Number,
                            AddressComplement = a.AddressComplement,
                            PostalCode = a.PostalCode,
                            DistrictName = d.District,
                            CityName = city.City,
                            StateName = state.State,
                            
                            // Notas do cliente
                            CustomerNotes = a.CustomerNotes
                        };

            // Aplicar filtro de busca (SearchTerm)
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                query = query.Where(a => 
                    a.CustomerName.ToLower().Contains(searchTerm) ||
                    a.ReferenceCar.ToLower().Contains(searchTerm) ||
                    a.Address.ToLower().Contains(searchTerm)
                );
            }

            // Aplicar filtro de status
            if (request.StatusFilter.HasValue)
            {
                query = query.Where(a => a.Status == request.StatusFilter.Value);
            }

            // Aplicar filtros de data
            if (!string.IsNullOrWhiteSpace(request.DateRangeStart))
            {
                if (DateTime.TryParse(request.DateRangeStart, out var startDate))
                {
                    query = query.Where(a => a.DateRequest >= startDate);
                }
            }

            if (!string.IsNullOrWhiteSpace(request.DateRangeEnd))
            {
                if (DateTime.TryParse(request.DateRangeEnd, out var endDate))
                {
                    // Adicionar 23:59:59 ao final do dia
                    endDate = endDate.Date.AddDays(1).AddTicks(-1);
                    query = query.Where(a => a.DateRequest <= endDate);
                }
            }

            // Contar total de registros após aplicar filtros
            var totalCount = await query.CountAsync();

            // Aplicar ordenação dinâmica
            if (!string.IsNullOrEmpty(request.SortBy))
            {
                query = request.SortBy.ToLower() switch
                {
                    "id" => request.SortDescending ? 
                        query.OrderByDescending(a => a.Id) : 
                        query.OrderBy(a => a.Id),
                    "daterequest" => request.SortDescending ? 
                        query.OrderByDescending(a => a.DateRequest) : 
                        query.OrderBy(a => a.DateRequest),
                    "status" => request.SortDescending ? 
                        query.OrderByDescending(a => a.Status) : 
                        query.OrderBy(a => a.Status),
                    "customername" => request.SortDescending ? 
                        query.OrderByDescending(a => a.CustomerName) : 
                        query.OrderBy(a => a.CustomerName),
                    _ => request.SortDescending ? 
                        query.OrderByDescending(a => a.DateRequest) : 
                        query.OrderBy(a => a.DateRequest)
                };
            }
            else
            {
                // Ordenação padrão por data de solicitação descendente (mais recentes primeiro)
                query = query.OrderByDescending(a => a.DateRequest);
            }

            // Aplicar paginação
            var skip = (request.PageNumber - 1) * request.PageSize;
            var attendances = await query
                .Skip(skip)
                .Take(request.PageSize)
                .ToListAsync();

            return new PagedResultDTO<AttendanceListDTO>
            {
                Items = attendances,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Log.Error($"AttendanceDb.GetPagedListAsync: {ex.Message}", DateTime.Now);
            throw new Exception($"Erro ao buscar atendimentos paginados: {ex.Message}");
        }
    }
}

// Enum for Status - copied here to avoid compilation issues
public enum AttendanceStatus
{
    Requested = 1,      // Solicitado
    InProgress = 2,     // Em andamento
    Completed = 3,      // Concluído
    Cancelled = 4,      // Cancelado
}