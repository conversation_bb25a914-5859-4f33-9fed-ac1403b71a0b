using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace AutoVPro.Database;

public class CustomerDb : ICustomerDb
{
    private readonly AutoProDbContext _context;
    
    public CustomerDb(AutoProDbContext context)
    {
        _context = context;
    }
    
    public async Task<CustomerModel> SaveAsync(CustomerModel entity)
    {
        try
        {
            if (entity.Guid == Guid.Empty)
                entity.Guid = Guid.NewGuid();
                
            if (entity.CreatedOn == default)
                entity.CreatedOn = DateTime.UtcNow;
                
            entity.UpdatedOn = DateTime.UtcNow;
            entity.Active = true;
            entity.Deleted = false;
            
            await _context.Customers.AddAsync(entity);
            await _context.SaveChangesAsync();
            
            // Reload with includes
            var savedCustomer = await _context.Customers
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .FirstAsync(c => c.Id == entity.Id);
                
            return savedCustomer;
        }
        catch(Exception ex)
        {
            Log.Error($"CustomerDb.SaveAsync: {ex.ToString()}", DateTime.Now);
            throw new Exception($"Error saving customer: {ex.Message}. Inner: {ex.InnerException?.Message}");       
        }
    }
    
    public async Task<CustomerModel> UpdateAsync(CustomerModel entity)
    {
        try
        {
            var customer = await _context.Customers.FindAsync(entity.Id);
            if (customer == null)
                throw new KeyNotFoundException($"Customer with id {entity.Id} not found");
                
            // Update fields
            customer.Name = entity.Name;
            customer.Type = entity.Type;
            customer.FiscalNumber = entity.FiscalNumber;
            customer.BirthDate = entity.BirthDate;
            customer.Address = entity.Address;
            customer.Number = entity.Number;
            customer.AddressComplement = entity.AddressComplement;
            customer.DistrictId = entity.DistrictId;
            customer.CityId = entity.CityId;
            customer.StateId = entity.StateId;
            customer.UpdatedOn = DateTime.UtcNow;
            customer.Active = entity.Active;
            customer.Deleted = entity.Deleted;

            await _context.SaveChangesAsync();
            
            // Reload with includes
            var updatedCustomer = await _context.Customers
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .FirstAsync(c => c.Id == customer.Id);
                
            return updatedCustomer;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.UpdateAsync: {ex.Message}", DateTime.Now);       
            throw new Exception(ex.Message);   
        }
    }
    
    public async Task<List<CustomerModel>> GetAllAsync()
    {
        try
        {
            var customers = await _context.Customers
                .Where(c => !c.Deleted)
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .OrderBy(c => c.Name)
                .ToListAsync();
                
            return customers;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.GetAllAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);      
        }
    }
    
    public async Task<CustomerModel> GetByIdAsync(Guid guid)
    {
        try
        {
            var customer = await _context.Customers
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .FirstOrDefaultAsync(c => c.Guid == guid && !c.Deleted);
                
            return customer;       
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.GetByIdAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);     
        }
    }

    public async Task<CustomerModel> DeleteAsync(int id)
    {
        try
        {
            var customer = await _context.Customers
                .FirstOrDefaultAsync(c => c.Id == id);
                
            if (customer != null)
            {
                // Soft delete - apenas marca como deletado
                customer.Deleted = true;
                customer.Active = false;
                customer.UpdatedOn = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return customer;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.DeleteAsync: {ex.Message}", DateTime.Now);
            throw;
        }
    }
    
    // Additional methods specific to Customer
    public async Task<CustomerModel?> GetByCpfAsync(string cpf)
    {
        try
        {
            // CPF deve vir limpo (somente números)
            var customer = await _context.Customers
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .FirstOrDefaultAsync(c => c.FiscalNumber == cpf && 
                                        c.Type == "PF" && 
                                        !c.Deleted);

            return customer;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.GetByCpfAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<CustomerModel?> GetByCnpjAsync(string cnpj)
    {
        try
        {
            // CNPJ deve vir limpo (somente números)
            var customer = await _context.Customers
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .FirstOrDefaultAsync(c => c.FiscalNumber == cnpj && 
                                        c.Type == "PJ" && 
                                        !c.Deleted);

            return customer;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.GetByCnpjAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<List<CustomerModel>> GetByStatusAsync(bool active)
    {
        try
        {
            var customers = await _context.Customers
                .Where(c => c.Active == active && !c.Deleted)
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .OrderBy(c => c.Name)
                .ToListAsync();

            return customers;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.GetByStatusAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<List<CustomerModel>> SearchByNameAsync(string name)
    {
        try
        {
            var customers = await _context.Customers
                .Where(c => c.Name.Contains(name) && !c.Deleted)
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .OrderBy(c => c.Name)
                .ToListAsync();

            return customers;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.SearchByNameAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<List<CustomerModel>> GetPagedAsync(int page, int pageSize, string? search, bool? active)
    {
        try
        {
            var query = _context.Customers
                .Where(c => !c.Deleted);

            // Aplicar filtro de busca
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(c => c.Name.Contains(search) || 
                                       c.FiscalNumber.Contains(search) ||
                                       (c.City != null && c.City.City.Contains(search)));
            }

            // Aplicar filtro de status
            if (active.HasValue)
            {
                query = query.Where(c => c.Active == active.Value);
            }

            var customers = await query
                .Include(c => c.City)
                .Include(c => c.District)
                .Include(c => c.State)
                .OrderBy(c => c.Name)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return customers;
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.GetPagedAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }

    public async Task<int> GetTotalCountAsync(string? search = null, bool? active = null)
    {
        try
        {
            var query = _context.Customers
                .Where(c => !c.Deleted);

            // Aplicar filtro de busca
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(c => c.Name.Contains(search) || 
                                       c.FiscalNumber.Contains(search) ||
                                       (c.City != null && c.City.City.Contains(search)));
            }

            // Aplicar filtro de status
            if (active.HasValue)
            {
                query = query.Where(c => c.Active == active.Value);
            }

            return await query.CountAsync();
        }
        catch (Exception ex)
        {
            Log.Error($"CustomerDb.GetTotalCountAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);
        }
    }
}