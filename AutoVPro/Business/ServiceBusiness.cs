using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.Extensions.Logging;

namespace AutoVPro.Business;

public class ServiceBusiness : IServiceBusiness
{
    private readonly IDatabase<ServiceModel> _serviceDb;
    private readonly IMapper _mapper;
    private readonly ILogger<ServiceBusiness> _logger;
    
    public ServiceBusiness(
        IDatabase<ServiceModel> serviceDb,
        IMapper mapper,
        ILogger<ServiceBusiness> logger
        )
    {
        _serviceDb = serviceDb;
        _mapper = mapper;
        _logger = logger;
    }
    
    public async Task<ServiceDTO> SaveAsync(ServiceDTO entity)
    {
        var service = _mapper.Map<ServiceModel>(entity);
        await _serviceDb.SaveAsync(service);
        var serviceDTO = _mapper.Map<ServiceDTO>(service);
        return serviceDTO;
    }
    
    public async Task<ServiceDTO> UpdateAsync(ServiceDTO entity)
    {
        var service = _mapper.Map<ServiceModel>(entity);
        await _serviceDb.UpdateAsync(service);
        
        return _mapper.Map<ServiceDTO>(service);
    }
    
    public async Task<List<ServiceDTO>> GetAllAsync()
    {
        var services = await _serviceDb.GetAllAsync();
        return _mapper.Map<List<ServiceDTO>>(services);
    }
    
    public async Task<ServiceDTO> GetByIdAsync(Guid guid)
    {
        var service = await _serviceDb.GetByIdAsync(guid);
        return _mapper.Map<ServiceDTO>(service);
    }

    public async Task<ServiceDTO> DeleteAsync(int id)
    {
        var service = await _serviceDb.DeleteAsync(id);
        return _mapper.Map<ServiceDTO>(service);
    }

    /// <summary>
    /// Método otimizado para buscar lista paginada de serviços com filtros
    /// </summary>
    public async Task<PagedResultDTO<ServiceListDTO>> GetPagedListAsync(PagedRequestDTO request)
    {
        try
        {
            _logger.LogInformation("Iniciando busca paginada de serviços. Página: {PageNumber}, Tamanho: {PageSize}, Termo: {SearchTerm}", 
                request.PageNumber, request.PageSize, request.SearchTerm);
            
            var serviceDbInstance = (ServiceDb)_serviceDb;
            var result = await serviceDbInstance.GetPagedListAsync(request);
            
            var pagedResult = new PagedResultDTO<ServiceListDTO>
            {
                Items = _mapper.Map<List<ServiceListDTO>>(result.Items),
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize
            };
            
            _logger.LogInformation("Busca paginada concluída. {ItemCount} serviços retornados de {TotalCount} total", 
                pagedResult.Items.Count, pagedResult.TotalCount);
            
            return pagedResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar lista paginada de serviços");
            throw;
        }
    }
}
