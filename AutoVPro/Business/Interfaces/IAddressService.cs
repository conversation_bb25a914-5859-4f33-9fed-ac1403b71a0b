using AutoVPro.Web.DTO;

namespace AutoVPro.Business.Interfaces;

/// <summary>
/// Serviço para integração com APIs externas de endereçamento
/// Centraliza lógica de busca de endereços no backend
/// </summary>
public interface IAddressService
{
    /// <summary>
    /// Busca endereço por CEP usando ViaCEP
    /// </summary>
    /// <param name="cep">CEP para busca (com ou sem máscara)</param>
    /// <returns>Dados do endereço ou null se não encontrado</returns>
    Task<AddressDTO?> GetAddressByCepAsync(string cep);
}