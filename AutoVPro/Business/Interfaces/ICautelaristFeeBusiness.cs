using AutoVPro.Web.DTO;

namespace AutoVPro.Business.Interfaces;

public interface ICautelaristFeeBusiness
{
    Task<CautelaristFeeDTO> SaveAsync(CautelaristFeeDTO entity);
    Task<CautelaristFeeDTO> UpdateAsync(CautelaristFeeDTO entity);
    Task<List<CautelaristFeeDTO>> GetAllAsync();
    Task<CautelaristFeeDTO> GetByIdAsync(long id);
    Task<CautelaristFeeDTO> DeleteAsync(long id);
    Task<List<CautelaristFeeDTO>> GetActiveFeesAsync();
}
