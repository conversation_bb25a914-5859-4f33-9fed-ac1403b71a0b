using AutoVPro.Web.DTO;

namespace AutoVPro.Business.Interfaces;

public interface IAttendanceBusiness : IBusiness<AttendanceDTO>
{
    Task<List<AttendanceDTO>> GetByCustomerIdAsync(int customerId);
    Task<List<AttendanceDTO>> GetByStatusAsync(int status);
    Task<List<AttendanceDTO>> GetByProfessionalIdAsync(int professionalId);
    Task<AttendanceDTO> UpdateStatusAsync(long id, int newStatus);
    Task<AttendanceDTO> AssignProfessionalAsync(long id, int professionalId);
    
    /// <summary>
    /// Cria solicitação de serviço com resolução automática de Customer/Brand/Model
    /// Move lógica de negócio do frontend para backend seguindo padrões arquiteturais
    /// </summary>
    Task<AttendanceDTO> CreateServiceRequestAsync(ServiceRequestDTO serviceRequest);
    
    /// <summary>
    /// Busca atendimentos com paginação para listagem otimizada
    /// </summary>
    Task<PagedResultDTO<AttendanceListDTO>> GetPagedListAsync(AttendancePagedRequestDTO request);
}