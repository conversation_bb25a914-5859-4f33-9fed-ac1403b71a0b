using AutoVPro.Web.DTO;

namespace AutoVPro.Business.Interfaces;

public interface ICautelaristBusiness
{
    Task<CautelaristDTO> SaveAsync(CautelaristDTO entity);
    Task<CautelaristDTO> UpdateAsync(CautelaristDTO entity);
    Task<List<CautelaristDTO>> GetAllAsync();
    Task<CautelaristDTO> GetByIdAsync(long id);
    Task<CautelaristDTO> DeleteAsync(int id);
    Task<CautelaristDTO> UpdateApprovalStatusAsync(long id, bool isApprove);
}
