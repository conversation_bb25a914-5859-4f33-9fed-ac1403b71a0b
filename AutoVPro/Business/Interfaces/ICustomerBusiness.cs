using AutoVPro.Web.DTO;

namespace AutoVPro.Business.Interfaces
{
    public interface ICustomerBusiness : IBusiness<CustomerDTO>
    {
        Task<CustomerDTO?> GetByCpfAsync(string cpf);
        Task<CustomerDTO?> GetByCnpjAsync(string cnpj);
        Task<CustomerDTO> CreateOrUpdateAsync(CustomerDTO dto);
        Task<PagedResultDTO<CustomerDTO>> GetPagedAsync(int page, int pageSize, string? search, bool? active);
        Task<List<CustomerDTO>> SearchAsync(string term);
        Task<List<CustomerDTO>> GetByStatusAsync(bool status);
        Task<int> GetTotalCountAsync();
    }
}