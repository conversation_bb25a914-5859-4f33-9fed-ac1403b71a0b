using AutoVPro.Web.DTO;

namespace AutoVPro.Business.Interfaces;

public interface IScreenOptionsPermissionsBusiness<ScreenOptionPermissionDTO> where ScreenOptionPermissionDTO : class
{
    public Task<ScreenOptionPermissionDTO> SaveAsync(ScreenOptionPermissionDTO screenOptionPermissionDTO);
    public Task<IList<ScreenOptionPermissionDTO>> GetAllAsync(int screenOptionId, int userGroupId);
    public Task<IList<ScreenOptionsPermissionCompleteDTO>> GetAllPermissionsAsync(int screenOptionId, int userGroupId);
    public Task<bool> UpdateAsync(int screenOptionPermissionId,int userGroupId, bool allow);
}