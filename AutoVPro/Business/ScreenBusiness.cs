using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Business;

public class ScreenBusiness : IScreenBusiness<ScreenDTO>
{
    private readonly IMapper _mapper;
    private readonly IScreenDb<ScreensModel> _screenDb;

    public ScreenBusiness(
        IScreenDb<ScreensModel> screenDb,
        IMapper mapper
        )
    {
        _screenDb = screenDb;
        _mapper = mapper;
    }
    
    public async Task<ScreenDTO> SaveAsync(ScreenDTO screenDTO)
    {
        ScreensModel screenModel = _mapper.Map<ScreensModel>(screenDTO);
        await _screenDb.SaveAsync(screenModel);
        ScreenDTO screen = _mapper.Map(screenModel, screenDTO);
        return screen;
    }

    public async Task<List<ScreenDTO>> GetAllAsync()
    {
        List<ScreensModel> lstScreeenDb = await _screenDb.GetAllAsync();
        List<ScreenDTO> lstScreenDTO = _mapper.Map<List<ScreenDTO>>(lstScreeenDb);
        return lstScreenDTO;
    }
}