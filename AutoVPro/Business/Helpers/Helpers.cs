using System.Security.Cryptography;
using System.Text;
using AutoVPro.Business.Interfaces;

namespace AutoVPro.Business.Helpers;

public static class Helpers 
{
    public static string ToCript(this string texto)
    {
        try
        {
            byte[] buffer = Encoding.Default.GetBytes(texto);
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] hashBytes = sha256.ComputeHash(buffer);

                // Convertendo o hash para string hexadecimal
                string hash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();

                return hash;
            }
        }
        catch (Exception x)
        {
            throw new Exception(x.Message);
        }
    }
}