using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Serilog;

namespace AutoVPro.Business;

public class CautelaristFeeBusiness : ICautelaristFeeBusiness
{
    private readonly ICautelaristFeeDatabase _cautelaristFeeDatabase;
    private readonly IMapper _mapper;

    public CautelaristFeeBusiness(ICautelaristFeeDatabase cautelaristFeeDatabase, IMapper mapper)
    {
        _cautelaristFeeDatabase = cautelaristFeeDatabase;
        _mapper = mapper;
    }

    public async Task<CautelaristFeeDTO> SaveAsync(CautelaristFeeDTO entity)
    {
        try
        {
            var feeModel = _mapper.Map<CautelaristFeeModel>(entity);
            var savedFee = await _cautelaristFeeDatabase.SaveAsync(feeModel);
            return _mapper.Map<CautelaristFeeDTO>(savedFee);
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeBusiness.SaveAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao salvar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristFeeDTO> UpdateAsync(CautelaristFeeDTO entity)
    {
        try
        {
            var feeModel = _mapper.Map<CautelaristFeeModel>(entity);
            var updatedFee = await _cautelaristFeeDatabase.UpdateAsync(feeModel);
            return _mapper.Map<CautelaristFeeDTO>(updatedFee);
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeBusiness.UpdateAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao atualizar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<List<CautelaristFeeDTO>> GetAllAsync()
    {
        try
        {
            var fees = await _cautelaristFeeDatabase.GetAllAsync();
            return _mapper.Map<List<CautelaristFeeDTO>>(fees);
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeBusiness.GetAllAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao buscar taxas dos cautelaristas: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristFeeDTO> GetByIdAsync(long id)
    {
        try
        {
            var fee = await _cautelaristFeeDatabase.GetByIdAsync(id);
            return _mapper.Map<CautelaristFeeDTO>(fee);
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeBusiness.GetByIdAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao buscar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristFeeDTO> DeleteAsync(long id)
    {
        try
        {
            var deletedFee = await _cautelaristFeeDatabase.DeleteAsync(id);
            return _mapper.Map<CautelaristFeeDTO>(deletedFee);
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeBusiness.DeleteAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao deletar taxa do cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<List<CautelaristFeeDTO>> GetActiveFeesAsync()
    {
        try
        {
            var fees = await _cautelaristFeeDatabase.GetActiveFeesAsync();
            return _mapper.Map<List<CautelaristFeeDTO>>(fees);
        }
        catch (Exception ex)
        {
            Log.Error($"CautelaristFeeBusiness.GetActiveFeesAsync: {ex.Message}", ex, DateTime.Now);
            throw new Exception($"Erro ao buscar taxas ativas: {ex.Message}", ex);
        }
    }
}
