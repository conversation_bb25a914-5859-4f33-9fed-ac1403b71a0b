using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Business;

public class RadiusBusiness : IRadiusBusiness
{
    private readonly IRadiusDatabase _radiusDb;
    private readonly IMapper _mapper;

    public RadiusBusiness(
        IRadiusDatabase radiusDb,
        IMapper mapper
    ){
        _radiusDb = radiusDb;
        _mapper = mapper;
    }

    public async Task<RadiusDTO> SaveAsync(RadiusDTO radiusDto)
    {
        var radiusModel = _mapper.Map<RadiusModel>(radiusDto);
        await _radiusDb.SaveAsync(radiusModel);
        return _mapper.Map<RadiusDTO>(radiusModel);
    }

    public async Task<RadiusDTO> UpdateAsync(RadiusDTO entity)
    {
        try
        {
            var radiusModel = _mapper.Map<RadiusModel>(entity);
            var updatedModel = await _radiusDb.UpdateAsync(radiusModel);
            return _mapper.Map<RadiusDTO>(updatedModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao atualizar raio de atendimento: {ex.Message}", ex);
        }
    }

    public async Task<List<RadiusDTO>> GetAllAsync()
    {
        var radiusDb = await _radiusDb.GetAllAsync();
        return _mapper.Map<List<RadiusDTO>>(radiusDb);
    }

    public async Task<RadiusDTO> GetByIdAsync(long id)
    {
        try
        {
            var radiusModel = await _radiusDb.GetByIdAsync(id);
            return _mapper.Map<RadiusDTO>(radiusModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar raio de atendimento: {ex.Message}", ex);
        }
    }

    public async Task<RadiusDTO> DeleteAsync(int id)
    {
        try
        {
            var deletedModel = await _radiusDb.DeleteAsync(id);
            return _mapper.Map<RadiusDTO>(deletedModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao deletar raio de atendimento: {ex.Message}", ex);
        }
    }
    public async Task<bool> ExistsAsync(int raioAtendimento)
    {
        try
        {
            return await _radiusDb.ExistsAsync(raioAtendimento);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao verificar se raio de atendimento existe: {ex.Message}", ex);
        }
    }
}
