using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Business;

public class ScreenOptionsBusiness : IScreenOptionsBusiness<ScreenOptionsDTO>
{
    private readonly IMapper _mapper;
    private readonly IScreenOptionsDb<ScreenOptionsModel>  _screenOptionsDbDb;
    
    public ScreenOptionsBusiness(
        IScreenOptionsDb<ScreenOptionsModel> screenOptionsDbDb,
        IMapper mapper)
    {
        _screenOptionsDbDb = screenOptionsDbDb;
        _mapper = mapper;       
    }
    
    public async Task<ScreenOptionsDTO> SaveAsync(ScreenOptionsDTO screenOptionsDTO)
    {
        ScreenOptionsModel screenOptionsModel = _mapper.Map<ScreenOptionsModel>(screenOptionsDTO);
        await _screenOptionsDbDb.SaveAsync(screenOptionsModel);
        ScreenOptionsDTO screenOptions = _mapper.Map<ScreenOptionsDTO>(screenOptionsModel);
        return screenOptions;
    }
    
    public async Task<List<ScreenOptionsDTO>> GetAllAsync(int? screenId)
    {
        List<ScreenOptionsModel> lstScreenOptionsDb = await _screenOptionsDbDb.GetAllAsync(screenId);
        List<ScreenOptionsDTO> lstScreenOptionsDTO = _mapper.Map<List<ScreenOptionsDTO>>(lstScreenOptionsDb);
        return lstScreenOptionsDTO;
    }
    
    public async Task<bool> DeleteAsync(int id)
    {
        return await _screenOptionsDbDb.DeleteAsync(id);
    }
}