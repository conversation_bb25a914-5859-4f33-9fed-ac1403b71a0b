using System.Text.RegularExpressions;
using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using System.Text.Json;

namespace AutoVPro.Business;

/// <summary>
/// Serviço para integração com APIs externas de endereçamento
/// Move integração ViaCEP do frontend para backend seguindo padrões arquiteturais
/// </summary>
public class AddressService : IAddressService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<AddressService> _logger;

    public AddressService(HttpClient httpClient, ILogger<AddressService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    /// <summary>
    /// Busca endereço por CEP usando ViaCEP
    /// </summary>
    /// <param name="cep">CEP para busca (com ou sem máscara)</param>
    /// <returns>Dados do endereço ou null se não encontrado</returns>
    public async Task<AddressDTO?> GetAddressByCepAsync(string cep)
    {
        try
        {
            // Validar e limpar CEP
            var cleanCep = ValidateAndCleanCep(cep);
            if (string.IsNullOrEmpty(cleanCep))
            {
                _logger.LogWarning("CEP inválido fornecido: {Cep}", cep);
                return null;
            }

            // Fazer requisição para ViaCEP
            var url = $"https://viacep.com.br/ws/{cleanCep}/json/";
            _logger.LogInformation("Buscando endereço para CEP: {Cep}", cleanCep);
            
            var response = await _httpClient.GetAsync(url);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Erro na requisição ViaCEP: {StatusCode}", response.StatusCode);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync();
            var viaCepResponse = JsonSerializer.Deserialize<ViaCepResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (viaCepResponse == null)
            {
                _logger.LogWarning("Resposta inválida do ViaCEP para CEP: {Cep}", cleanCep);
                return null;
            }

            // ViaCEP retorna "erro": true quando CEP não existe
            if (viaCepResponse.Erro)
            {
                _logger.LogInformation("CEP não encontrado: {Cep}", cleanCep);
                return null;
            }

            // Mapear resposta para nosso DTO
            return new AddressDTO
            {
                Logradouro = viaCepResponse.Logradouro ?? "",
                Bairro = viaCepResponse.Bairro ?? "",
                Localidade = viaCepResponse.Localidade ?? "",
                Uf = viaCepResponse.Uf ?? "",
                Complemento = viaCepResponse.Complemento,
                Cep = cleanCep,
                Erro = false
            };
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Erro de rede ao buscar CEP: {Cep}", cep);
            return null;
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout ao buscar CEP: {Cep}", cep);
            return null;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Erro ao deserializar resposta do ViaCEP para CEP: {Cep}", cep);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro inesperado ao buscar CEP: {Cep}", cep);
            return null;
        }
    }

    /// <summary>
    /// Valida e limpa o CEP removendo caracteres não numéricos
    /// </summary>
    /// <param name="cep">CEP com ou sem máscara</param>
    /// <returns>CEP limpo com 8 dígitos ou null se inválido</returns>
    private static string? ValidateAndCleanCep(string cep)
    {
        if (string.IsNullOrWhiteSpace(cep))
            return null;

        // Remove todos os caracteres não numéricos
        var cleanCep = Regex.Replace(cep, @"\D", "");

        // CEP deve ter exatamente 8 dígitos
        if (cleanCep.Length != 8)
            return null;

        // Verificar se não é um CEP com todos os dígitos iguais (ex: 00000000)
        if (cleanCep.All(c => c == cleanCep[0]))
            return null;

        return cleanCep;
    }
}

/// <summary>
/// Modelo para resposta da API ViaCEP
/// </summary>
internal class ViaCepResponse
{
    public string? Cep { get; set; }
    public string? Logradouro { get; set; }
    public string? Complemento { get; set; }
    public string? Bairro { get; set; }
    public string? Localidade { get; set; }
    public string? Uf { get; set; }
    public string? Ibge { get; set; }
    public string? Gia { get; set; }
    public string? Ddd { get; set; }
    public string? Siafi { get; set; }
    public bool Erro { get; set; } = false;
}