using AutoMapper;
using AutoVPro.Business.Helpers;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Business;

public class LoginBusiness : ILoginBusiness
{
    private readonly IMapper _mapper;
    private readonly IBusiness<UserDTO> _userBusiness;
    private readonly ILogBusiness<LogDTO> _logBusiness;

    public LoginBusiness(
        IMapper mapper,
        IBusiness<UserDTO> userBusiness
    )
    {
        _mapper = mapper;
        _userBusiness = userBusiness;       
    }

    public async Task<LoginDTO?> Logar(LoginDTO loginDTO)
    {
        var allUsers = await _userBusiness.GetAllAsync();
        
        if (allUsers.Count == 0)
            return null;
        
        UserDTO? user = allUsers
            .FirstOrDefault(x =>
                x.Email == loginDTO.Email &&
                x.Password == loginDTO.Password.ToCript());

        if (user != null)
            return new LoginDTO
            {
                Email = user.Email,
                Guid = user.Guid,
                UserGroupId = user.UserGroupId
            };
        
        return null;
    }
}
