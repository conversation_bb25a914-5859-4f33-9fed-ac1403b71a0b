using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Business
{
    public class CustomerBusiness : ICustomerBusiness
    {
        private readonly ICustomerDb _customerDb;
        private readonly IMapper _mapper;

        public CustomerBusiness(ICustomerDb customerDb, IMapper mapper)
        {
            _customerDb = customerDb;
            _mapper = mapper;
        }

        public async Task<CustomerDTO> DeleteAsync(int id)
        {
            var customer = await _customerDb.DeleteAsync(id);
            if (customer == null)
                throw new KeyNotFoundException($"Customer with id {id} not found");

            return _mapper.Map<CustomerDTO>(customer);
        }

        public async Task<List<CustomerDTO>> GetAllAsync()
        {
            var customers = await _customerDb.GetAllAsync();
            return _mapper.Map<List<CustomerDTO>>(customers);
        }

        public async Task<CustomerDTO> GetByIdAsync(Guid guid)
        {
            var customer = await _customerDb.GetByIdAsync(guid);
            if (customer == null)
                throw new KeyNotFoundException($"Customer with guid {guid} not found");

            return _mapper.Map<CustomerDTO>(customer);
        }

        public async Task<CustomerDTO> SaveAsync(CustomerDTO dto)
        {
            // Validações e valores padrão
            if (dto.Guid == Guid.Empty)
                dto.Guid = Guid.NewGuid();
                
            if (dto.CreatedOn == default)
                dto.CreatedOn = DateTime.UtcNow;
                
            dto.UpdatedOn = DateTime.UtcNow;
            dto.Active = true;
            dto.Deleted = false;

            var customer = _mapper.Map<CustomerModel>(dto);
            var savedCustomer = await _customerDb.SaveAsync(customer);
            return _mapper.Map<CustomerDTO>(savedCustomer);
        }

        public async Task<CustomerDTO> UpdateAsync(CustomerDTO dto)
        {
            dto.UpdatedOn = DateTime.UtcNow;
            
            var customer = _mapper.Map<CustomerModel>(dto);
            var updatedCustomer = await _customerDb.UpdateAsync(customer);
            if (updatedCustomer == null)
                throw new KeyNotFoundException($"Customer with id {dto.Id} not found");
                
            return _mapper.Map<CustomerDTO>(updatedCustomer);
        }

        public async Task<CustomerDTO?> GetByCpfAsync(string cpf)
        {
            // Limpar CPF
            cpf = cpf.Replace(".", "").Replace("-", "").Replace("/", "");
            
            var customer = await _customerDb.GetByCpfAsync(cpf);
            return customer != null ? _mapper.Map<CustomerDTO>(customer) : null;
        }

        public async Task<CustomerDTO?> GetByCnpjAsync(string cnpj)
        {
            // Limpar CNPJ
            cnpj = cnpj.Replace(".", "").Replace("-", "").Replace("/", "");
            
            var customer = await _customerDb.GetByCnpjAsync(cnpj);
            return customer != null ? _mapper.Map<CustomerDTO>(customer) : null;
        }

        public async Task<CustomerDTO> CreateOrUpdateAsync(CustomerDTO dto)
        {
            CustomerModel? existingCustomer = null;

            // Verificar se já existe por CPF ou CNPJ
            if (!string.IsNullOrEmpty(dto.FiscalNumber))
            {
                var cleanedFiscalNumber = dto.FiscalNumber.Replace(".", "").Replace("-", "").Replace("/", "");
                
                if (dto.Type == "PF" && cleanedFiscalNumber.Length == 11)
                {
                    existingCustomer = await _customerDb.GetByCpfAsync(cleanedFiscalNumber);
                }
                else if (dto.Type == "PJ" && cleanedFiscalNumber.Length == 14)
                {
                    existingCustomer = await _customerDb.GetByCnpjAsync(cleanedFiscalNumber);
                }
            }

            if (existingCustomer != null)
            {
                // Atualizar cliente existente
                dto.Id = existingCustomer.Id;
                dto.Guid = existingCustomer.Guid;
                dto.CreatedOn = existingCustomer.CreatedOn;
                return await UpdateAsync(dto);
            }
            else
            {
                // Criar novo cliente
                return await SaveAsync(dto);
            }
        }

        public async Task<PagedResultDTO<CustomerDTO>> GetPagedAsync(int page, int pageSize, string? search, bool? active)
        {
            var customers = await _customerDb.GetPagedAsync(page, pageSize, search, active);
            var totalCount = await _customerDb.GetTotalCountAsync(search, active);
            
            var customerDtos = _mapper.Map<List<CustomerDTO>>(customers);
            
            return new PagedResultDTO<CustomerDTO>
            {
                Items = customerDtos,
                TotalCount = totalCount,
                PageNumber = page,
                PageSize = pageSize
            };
        }

        public async Task<List<CustomerDTO>> SearchAsync(string term)
        {
            var customers = await _customerDb.SearchByNameAsync(term);
            return _mapper.Map<List<CustomerDTO>>(customers);
        }

        public async Task<List<CustomerDTO>> GetByStatusAsync(bool status)
        {
            var customers = await _customerDb.GetByStatusAsync(status);
            return _mapper.Map<List<CustomerDTO>>(customers);
        }

        public async Task<int> GetTotalCountAsync()
        {
            return await _customerDb.GetTotalCountAsync();
        }
    }
}