using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.Extensions.Logging;

namespace AutoVPro.Business;

public class UserGroupBusiness : IUserGroupBusiness
{
    private readonly IDatabase<UserGroupModel> _userGroupDb;
    private readonly IMapper _mapper;
    private readonly ILogger<UserGroupBusiness> _logger;
    
    public UserGroupBusiness(
        IDatabase<UserGroupModel> userGroupDb,
        IMapper mapper,
        ILogger<UserGroupBusiness> logger
        )
    {
        _userGroupDb = userGroupDb;
        _mapper = mapper;
        _logger = logger;
    }
    public async Task<UserGroupDTO> SaveAsync(UserGroupDTO entity)
    {
        var userGroup = _mapper.Map<UserGroupModel>(entity);
        await _userGroupDb.SaveAsync(userGroup);
        var userGroupDTO = _mapper.Map<UserGroupDTO>(userGroup);
        return userGroupDTO;
    }
    
    public async Task<UserGroupDTO> UpdateAsync(UserGroupDTO entity)
    {
        var userGroup = _mapper.Map<UserGroupModel>(entity);
        await _userGroupDb.UpdateAsync(userGroup);
        
        return _mapper.Map<UserGroupDTO>(userGroup);
    }
    
    public async Task<List<UserGroupDTO>> GetAllAsync()
    {
        var userGroup = await _userGroupDb.GetAllAsync();
        return _mapper.Map<List<UserGroupDTO>>(userGroup);
    }
    
    public async Task<UserGroupDTO> GetByIdAsync(Guid guid)
    {
        var userGroup = await _userGroupDb.GetByIdAsync(guid);
        return _mapper.Map<UserGroupDTO>(userGroup);
    }
    
    public async Task<UserGroupDTO> DeleteAsync(int id)
    {
        var userGroup = await _userGroupDb.DeleteAsync(id);
        return _mapper.Map<UserGroupDTO>(userGroup);   
    }

    /// <summary>
    /// Método otimizado para buscar lista paginada de grupos de usuário com filtros
    /// </summary>
    public async Task<PagedResultDTO<UserGroupListDTO>> GetPagedListAsync(PagedRequestDTO request)
    {
        try
        {
            _logger.LogInformation("Iniciando busca paginada de grupos de usuário. Página: {PageNumber}, Tamanho: {PageSize}, Termo: {SearchTerm}", 
                request.PageNumber, request.PageSize, request.SearchTerm);
            
            var userGroupDbInstance = (UserGroupDb)_userGroupDb;
            var result = await userGroupDbInstance.GetPagedListAsync(request);
            
            var pagedResult = new PagedResultDTO<UserGroupListDTO>
            {
                Items = _mapper.Map<List<UserGroupListDTO>>(result.Items),
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize
            };
            
            _logger.LogInformation("Busca paginada concluída. {ItemCount} grupos de usuário retornados de {TotalCount} total", 
                pagedResult.Items.Count, pagedResult.TotalCount);
            
            return pagedResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar lista paginada de grupos de usuário");
            throw;
        }
    }
}