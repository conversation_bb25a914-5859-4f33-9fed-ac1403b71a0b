using AutoMapper;
using AutoVPro.Business.Helpers;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.Extensions.Logging;

namespace AutoVPro.Business;

public class UserBusiness : IUserBusiness
{
    private readonly IDatabase<UserModel> _userDb;
    private readonly IMapper _mapper;
    private readonly ILogger<UserBusiness> _logger;
    
    public UserBusiness(
        IDatabase<UserModel> userDb,
        IMapper mapper,
        ILogger<UserBusiness> logger
        )
    {
        _userDb = userDb;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<UserDTO> SaveAsync(UserDTO userDTO)
    {
        try
        {
            _logger.LogInformation("Iniciando criação de usuário com email: {Email}", userDTO.Email);
            
            userDTO.Password = userDTO.Password.ToCript();
            var userModel = _mapper.Map<UserModel>(userDTO);
            await _userDb.SaveAsync(userModel);
            
            var result = _mapper.Map<UserDTO>(userModel);
            _logger.LogInformation("Usuário criado com sucesso. ID: {Id}", result.Id);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar usuário com email: {Email}", userDTO.Email);
            throw;
        }
    }

    public async Task<UserDTO> DeleteAsync(int id)
    {
        try
        {
            _logger.LogInformation("Iniciando exclusão de usuário com ID: {Id}", id);
            
            var userDb = await _userDb.DeleteAsync(id);
            if (userDb == null)
            {
                _logger.LogWarning("Usuário com ID {Id} não encontrado para exclusão", id);
                throw new Exception("Usuário não encontrado");
            }
            
            var result = _mapper.Map<UserDTO>(userDb);
            _logger.LogInformation("Usuário com ID {Id} excluído com sucesso", id);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao excluir usuário com ID: {Id}", id);
            throw;
        }
    }
    
    public async Task<UserDTO> UpdateAsync(UserDTO userDTO)
    {
        try
        {
            _logger.LogInformation("Iniciando atualização de usuário com ID: {Id}", userDTO.Id);
            
            userDTO.Password = userDTO.Password.ToCript();
            var userDb = _mapper.Map<UserModel>(userDTO);
            await _userDb.UpdateAsync(userDb);
            
            var result = _mapper.Map<UserDTO>(userDb);
            _logger.LogInformation("Usuário com ID {Id} atualizado com sucesso", userDTO.Id);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar usuário com ID: {Id}", userDTO.Id);
            throw;
        }
    }

    public async Task<UserDTO> UpdateAsync(UserUpdateDTO userUpdateDto)
    {
        try
        {
            _logger.LogInformation("Iniciando atualização de usuário com ID: {Id}", userUpdateDto.Id);
            
            // Busca o usuário atual no banco
            var existingUser = await _userDb.GetByIdAsync(userUpdateDto.Guid);
            if (existingUser == null || !existingUser.Active || existingUser.Deleted)
            {
                _logger.LogWarning("Usuário com GUID {Guid} não encontrado ou inativo", userUpdateDto.Guid);
                throw new Exception("Usuário não encontrado ou inativo");
            }

            // Atualiza apenas os campos fornecidos
            existingUser.Email = userUpdateDto.Email;
            existingUser.UserGroupId = userUpdateDto.UserGroupId;
            existingUser.Active = userUpdateDto.Active;
            existingUser.Deleted = userUpdateDto.Deleted;
            existingUser.UpdatedOn = DateTime.UtcNow;

            // Só atualiza a senha se foi fornecida
            if (!string.IsNullOrWhiteSpace(userUpdateDto.Password))
            {
                existingUser.Password = userUpdateDto.Password.ToCript();
            }

            await _userDb.UpdateAsync(existingUser);
            
            var result = _mapper.Map<UserDTO>(existingUser);
            _logger.LogInformation("Usuário com ID {Id} atualizado com sucesso", userUpdateDto.Id);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar usuário com ID: {Id}", userUpdateDto.Id);
            throw;
        }
    }
    
    public async Task<List<UserDTO>> GetAllAsync()
    {
        try
        {
            _logger.LogInformation("Iniciando busca de todos os usuários");
            
            var userDb = await _userDb.GetAllAsync();
            var result = _mapper.Map<List<UserDTO>>(userDb);
            
            _logger.LogInformation("Busca concluída. {Count} usuários encontrados", result.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar todos os usuários");
            throw;
        }
    }
    
    public async Task<UserDTO> GetByIdAsync(Guid guid)
    {
        try
        {
            _logger.LogInformation("Iniciando busca de usuário por GUID: {Guid}", guid);
            
            var userDb = await _userDb.GetByIdAsync(guid);
            if (userDb == null || !userDb.Active || userDb.Deleted)
            {
                _logger.LogWarning("Usuário com GUID {Guid} não encontrado ou inativo", guid);
                throw new Exception("Usuário não encontrado ou inativo");
            }
            
            var result = _mapper.Map<UserDTO>(userDb);
            _logger.LogInformation("Usuário com GUID {Guid} encontrado com sucesso", guid);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar usuário por GUID: {Guid}", guid);
            throw;
        }
    }

    /// <summary>
    /// Método otimizado para buscar lista paginada de usuários com filtros
    /// </summary>
    public async Task<PagedResultDTO<UserListDTO>> GetPagedListAsync(PagedRequestDTO request)
    {
        try
        {
            _logger.LogInformation("Iniciando busca paginada de usuários. Página: {PageNumber}, Tamanho: {PageSize}, Termo: {SearchTerm}", 
                request.PageNumber, request.PageSize, request.SearchTerm);
            
            var userDbInstance = (UserDb)_userDb;
            var result = await userDbInstance.GetPagedListAsync(request);
            
            var pagedResult = new PagedResultDTO<UserListDTO>
            {
                Items = _mapper.Map<List<UserListDTO>>(result.Items),
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize
            };
            
            _logger.LogInformation("Busca paginada concluída. {ItemCount} usuários retornados de {TotalCount} total", 
                pagedResult.Items.Count, pagedResult.TotalCount);
            
            return pagedResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar lista paginada de usuários");
            throw;
        }
    }
}