using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;

namespace AutoVPro.Business;

public class ScreenOptionsesPermissionsBusiness : IScreenOptionsPermissionsBusiness<ScreenOptionPermissionDTO>
{
    private readonly IMapper _mapper;
    private readonly IScreenOptionPermissionDb<ScreenOptionPermissionModel> _screenOptionPermissionDb;
    
    public ScreenOptionsesPermissionsBusiness(
        IScreenOptionPermissionDb<ScreenOptionPermissionModel> screenOptionPermissionDbDb,
        IMapper mapper)
    {
        _screenOptionPermissionDb = screenOptionPermissionDbDb;
        _mapper = mapper;
    }
    
    public async Task<ScreenOptionPermissionDTO> SaveAsync(ScreenOptionPermissionDTO screenOptionPermissionDTO)
    {
        ScreenOptionPermissionModel screenOptionPermissionModel = _mapper.Map<ScreenOptionPermissionModel>(screenOptionPermissionDTO);

        bool checkExistsScreenOptionPermission = await _screenOptionPermissionDb.CheckPermissionByScreenOptionIdAsync(
                screenOptionPermissionModel.ScreenOptionId, screenOptionPermissionModel.UserGroupId);
        
        //ADD PERMISSION ONLY IF NO RECORDS IN SCRENPERMISSIONS
        if(checkExistsScreenOptionPermission)
            await _screenOptionPermissionDb.UpdateAsync(screenOptionPermissionModel.ScreenOptionId, screenOptionPermissionModel.UserGroupId, screenOptionPermissionModel.Allow);
        else
            await _screenOptionPermissionDb.SaveAsync(screenOptionPermissionModel);
        
        ScreenOptionPermissionDTO screenOptionPermission = _mapper.Map<ScreenOptionPermissionDTO>(screenOptionPermissionModel);
        return screenOptionPermission;
    }

    public async Task<IList<ScreenOptionPermissionDTO>> GetAllAsync(int screenId, int userGroupId)
    {
        List<ScreenOptionPermissionModel> lstScreenOptionPermissionDb = await _screenOptionPermissionDb.GetAllAsync(screenId, userGroupId);
        List<ScreenOptionPermissionDTO> lstScreenOptionPermissionDTO = _mapper.Map<List<ScreenOptionPermissionDTO>>(lstScreenOptionPermissionDb);
        return lstScreenOptionPermissionDTO;
    }

    public async Task<IList<ScreenOptionsPermissionCompleteDTO>> GetAllPermissionsAsync(int screenId, int userGroupId)
    {
        var  lstScreenOptionsPermission = await _screenOptionPermissionDb.GetAllAsync(screenId, userGroupId);
        IList<ScreenOptionsPermissionCompleteDTO> lstScreenOptionCustomDTO = new List<ScreenOptionsPermissionCompleteDTO>();

        var lstScreenOptionCustomModel  = lstScreenOptionsPermission.Select(x => new
        {
            Id = x.Id,
            UserGroupId = x.UserGroupId,
            ScreenId = x.ScreenOptionModel?.ScreenId,
            ScreenOptionId = x.ScreenOptionId,
            ScreenName = x.ScreenOptionModel?.ScreenModel.ScreenName,
            UserGroupName = x.UserGroup?.NomeGrupoUser,
            ScreenOptionName = x.ScreenOptionModel?.Option,
            Allow = x.Allow
        }).ToList();
        
        return _mapper.Map<List<ScreenOptionsPermissionCompleteDTO>>(lstScreenOptionsPermission);
    }

    public async Task<bool> UpdateAsync(int screenOptionId, int userGroupId, bool allow)
    {
        return await _screenOptionPermissionDb.UpdateAsync(screenOptionId, userGroupId, allow);      
    }
}