using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using AutoMapper;
using Serilog;

namespace AutoVPro.Business
{
    public class ItemBusiness : IItemBusiness
    {
        private readonly IItemDatabase _itemDb;
        private readonly IMapper _mapper;
        
        public ItemBusiness(IItemDatabase itemDb, IMapper mapper)
        {
            _itemDb = itemDb;
            _mapper = mapper;
        }
        
        public async Task<ItemDTO> SaveAsync(ItemDTO entity)
        {
            try
            {
                var itemModel = _mapper.Map<ItemModel>(entity);
                var savedItem = await _itemDb.SaveAsync(itemModel);
                return _mapper.Map<ItemDTO>(savedItem);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.SaveAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao salvar item: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemDTO> UpdateAsync(ItemDTO entity)
        {
            try
            {
                var itemModel = _mapper.Map<ItemModel>(entity);
                var updatedItem = await _itemDb.UpdateAsync(itemModel);
                return _mapper.Map<ItemDTO>(updatedItem);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.UpdateAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao atualizar item: {ex.Message}", ex);
            }
        }
        
        public async Task<List<ItemDTO>> GetAllAsync()
        {
            try
            {
                var items = await _itemDb.GetAllAsync();
                return _mapper.Map<List<ItemDTO>>(items);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.GetAllAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemDTO> GetByIdAsync(Guid guid)
        {
            try
            {
                var item = await _itemDb.GetByIdAsync(guid);
                return _mapper.Map<ItemDTO>(item);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.GetByIdAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar item: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemDTO> GetByIdAsync(long id)
        {
            try
            {
                var item = await _itemDb.GetByIdAsync(id);
                return _mapper.Map<ItemDTO>(item);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.GetByIdAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar item: {ex.Message}", ex);
            }
        }
        
        public async Task<ItemDTO> DeleteAsync(int id)
        {
            try
            {
                var deletedItem = await _itemDb.DeleteAsync(id);
                return _mapper.Map<ItemDTO>(deletedItem);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.DeleteAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao deletar item: {ex.Message}", ex);
            }
        }
        
        public async Task<List<ItemDTO>> SearchByNameAsync(string name)
        {
            try
            {
                var items = await _itemDb.SearchByNameAsync(name);
                return _mapper.Map<List<ItemDTO>>(items);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.SearchByNameAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens por nome: {ex.Message}", ex);
            }
        }
        
        public async Task<List<ItemDTO>> GetByStatusAsync(bool isActive)
        {
            try
            {
                var items = await _itemDb.GetByStatusAsync(isActive);
                return _mapper.Map<List<ItemDTO>>(items);
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.GetByStatusAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens por status: {ex.Message}", ex);
            }
        }

        public async Task<PagedResultDTO<ItemDTO>> GetPagedListAsync(PagedRequestDTO request)
        {
            try
            {
                var (items, totalCount) = await _itemDb.GetPagedAsync(
                    request.PageNumber,
                    request.PageSize,
                    request.SearchTerm,
                    request.SortBy,
                    request.SortDescending);

                var itemDtos = _mapper.Map<List<ItemDTO>>(items);

                return new PagedResultDTO<ItemDTO>
                {
                    Items = itemDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }
            catch (Exception ex)
            {
                Log.Error($"ItemBusiness.GetPagedListAsync: {ex.Message}", DateTime.Now);
                throw new Exception($"Erro ao buscar itens paginados: {ex.Message}", ex);
            }
        }
    }
}
