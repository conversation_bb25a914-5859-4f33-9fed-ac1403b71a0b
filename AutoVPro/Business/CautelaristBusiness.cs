using AutoMapper;
using AutoVPro.Business.Helpers;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Serilog;

namespace AutoVPro.Business;

public class CautelaristBusiness : IBusiness<CautelaristDTO>
{
    private readonly IDatabase<CautelaristModel> _cautelaristDb;
    private readonly IMapper _mapper;

    public CautelaristBusiness(
        IDatabase<CautelaristModel> cautelaristDb,
        IMapper mapper
    ){
        _cautelaristDb = cautelaristDb;
        _mapper = mapper;
    }

     public async Task<CautelaristDTO> SaveAsync(CautelaristDTO cautelaristDto)
    {
        try
        {
            var cautelaristModel = _mapper.Map<CautelaristModel>(cautelaristDto);
            await _cautelaristDb.SaveAsync(cautelaristModel);
            return _mapper.Map<CautelaristDTO>(cautelaristModel);
        }
        catch (Exception ex)
        {
            Log.Error($"UserGroupDb.SaveAsync: {ex.Message}", DateTime.Now);
            throw new Exception(ex.Message);       
        }
    }

    public async Task<CautelaristDTO> UpdateAsync(CautelaristDTO entity)
    {
        try
        {
            var cautelaristModel = _mapper.Map<CautelaristModel>(entity);
            var updatedModel = await _cautelaristDb.UpdateAsync(cautelaristModel);
            return _mapper.Map<CautelaristDTO>(updatedModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao atualizar cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<List<CautelaristDTO>> GetAllAsync()
    {
        var userDb = await _cautelaristDb.GetAllAsync();
        return _mapper.Map<List<CautelaristDTO>>(userDb);
    }


    public async Task<CautelaristDTO> GetByIdAsync(Guid guid)
    {
        try
        {
            var cautelaristModel = await _cautelaristDb.GetByIdAsync(guid);
            return _mapper.Map<CautelaristDTO>(cautelaristModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar cautelarista: {ex.Message}", ex);
        }
    }

    // Método específico do domínio usando long id
    public async Task<CautelaristDTO> GetByIdAsync(long id)
    {
        try
        {
            var db = (CautelaristDb)_cautelaristDb;
            var cautelaristModel = await db.GetByIdAsync(id);
            return _mapper.Map<CautelaristDTO>(cautelaristModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar cautelarista: {ex.Message}", ex);
        }
    }

    public async Task<CautelaristDTO> DeleteAsync(int id)
    {
        try
        {
            var deletedModel = await _cautelaristDb.DeleteAsync(id);
            return _mapper.Map<CautelaristDTO>(deletedModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao deletar cautelarista: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Busca cautelaristas com paginação para listagem otimizada
    /// </summary>
    public async Task<PagedResultDTO<CautelaristListDTO>> GetPagedListAsync(PagedRequestDTO request)
    {
        try
        {
            var db = (CautelaristDb)_cautelaristDb;
            var (items, totalCount) = await db.GetPagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.SortBy,
                request.SortDescending);

            var listDtos = _mapper.Map<List<CautelaristListDTO>>(items);
            
            return new PagedResultDTO<CautelaristListDTO>
            {
                Items = listDtos,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar cautelaristas paginados: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Busca apenas os documentos Base64 de um cautelarista específico
    /// </summary>
    public async Task<CautelaristDTO?> GetDocumentsAsync(long id)
    {
        try
        {
            var db = (CautelaristDb)_cautelaristDb;
            var documents = await db.GetDocumentsAsync(id);
            return documents != null ? _mapper.Map<CautelaristDTO>(documents) : null;
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao buscar documentos do cautelarista: {ex.Message}", ex);
        }
    }

    // Método específico do domínio de negócio - mantido além da interface genérica
    public async Task<CautelaristDTO> UpdateApprovalStatusAsync(long id, bool isApprove)
    {
        try
        {
            var db = (CautelaristDb)_cautelaristDb;
            var updatedModel = await db.UpdateApprovalStatusAsync(id, isApprove);
            return _mapper.Map<CautelaristDTO>(updatedModel);
        }
        catch (Exception ex)
        {
            throw new Exception($"Erro ao atualizar status de aprovação: {ex.Message}", ex);
        }
    }
}