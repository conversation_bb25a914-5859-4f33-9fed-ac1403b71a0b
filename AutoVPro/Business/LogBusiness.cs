using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using AutoMapper;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;

namespace AutoVPro.Business;

public class LogBusiness : ILogBusiness<LogDTO>
{
    private readonly IMapper _mapper;
    private readonly ILogDb<LogModel> _logDb;
    
    public LogBusiness(
        ILogDb<LogModel> logDb,
        IMapper mapper)
    {
        _logDb = logDb;
        _mapper = mapper;
    }
    
    public async Task<LogDTO> SaveAsync(LogDTO logDTO)
    {
        LogModel logModel = _mapper.Map<LogModel>(logDTO);
        await _logDb.AddAsync(logModel);
        LogDTO log = _mapper.Map<LogDTO>(logModel);
        return log;
    }
}