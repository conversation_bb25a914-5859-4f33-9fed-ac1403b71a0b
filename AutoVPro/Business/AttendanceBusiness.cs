using AutoMapper;
using AutoVPro.Business.Interfaces;
using AutoVPro.Database.Interfaces;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using AutoVPro.Database.AutovproContext;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace AutoVPro.Business;

public class AttendanceBusiness : IAttendanceBusiness
{
    private readonly IAttendanceDb _attendanceDb;
    private readonly ICustomerBusiness _customerBusiness;
    private readonly IAddressService _addressService;
    private readonly AutoProDbContext _context;
    private readonly IMapper _mapper;
    private readonly ILogger<AttendanceBusiness> _logger;

    public AttendanceBusiness(
        IAttendanceDb attendanceDb, 
        ICustomerBusiness customerBusiness, 
        IAddressService addressService,
        AutoProDbContext context,
        IMapper mapper,
        ILogger<AttendanceBusiness> logger)
    {
        _attendanceDb = attendanceDb;
        _customerBusiness = customerBusiness;
        _addressService = addressService;
        _context = context;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AttendanceDTO> DeleteAsync(int id)
    {
        var attendance = await _attendanceDb.DeleteAsync(id);
        if (attendance == null)
            throw new KeyNotFoundException($"Attendance with id {id} not found");

        return _mapper.Map<AttendanceDTO>(attendance);
    }

    public async Task<List<AttendanceDTO>> GetAllAsync()
    {
        var attendances = await _attendanceDb.GetAllAsync();
        return _mapper.Map<List<AttendanceDTO>>(attendances);
    }

    public async Task<AttendanceDTO> GetByIdAsync(Guid guid)
    {
        var attendance = await _attendanceDb.GetByIdAsync(guid);
        if (attendance == null)
            throw new KeyNotFoundException($"Attendance with guid {guid} not found");

        return _mapper.Map<AttendanceDTO>(attendance);
    }

    public async Task<AttendanceDTO> SaveAsync(AttendanceDTO dto)
    {
        // Validações e valores padrão
        if (dto.Guid == Guid.Empty)
            dto.Guid = Guid.NewGuid();
            
        if (dto.DateRequest == default)
            dto.DateRequest = DateTime.UtcNow;
            
        // Gerenciar cliente - auto-criação se necessário
        if (dto.Customer != null)
        {
            // Se dados do cliente foram fornecidos, criar ou atualizar cliente
            var customerDto = await _customerBusiness.CreateOrUpdateAsync(dto.Customer);
            dto.CustomerId = customerDto.Id;
        }
        else if (dto.CustomerId <= 0)
        {
            // Se não há dados do cliente nem CustomerId válido, erro
            throw new InvalidOperationException("Customer information is required. Provide either Customer data or valid CustomerId.");
        }
        
        // Garantir que navigation properties são null no DTO para mapeamento
        dto.Customer = null;
        dto.Professional = null;
        dto.Brand = null;
        dto.Model = null;
        dto.District = null;
        dto.City = null;
        dto.State = null;
        dto.VehicleDistrict = null;
        dto.VehicleCity = null;
        dto.VehicleState = null;

        var attendance = _mapper.Map<AttendanceModel>(dto);
        var savedAttendance = await _attendanceDb.SaveAsync(attendance);
        return _mapper.Map<AttendanceDTO>(savedAttendance);
    }

    public async Task<AttendanceDTO> UpdateAsync(AttendanceDTO dto)
    {
        var attendance = _mapper.Map<AttendanceModel>(dto);
        var updatedAttendance = await _attendanceDb.UpdateAsync(attendance);
        if (updatedAttendance == null)
            throw new KeyNotFoundException($"Attendance with id {dto.Id} not found");
            
        return _mapper.Map<AttendanceDTO>(updatedAttendance);
    }

    public async Task<List<AttendanceDTO>> GetByCustomerIdAsync(int customerId)
    {
        var attendances = await _attendanceDb.GetByCustomerIdAsync(customerId);
        return _mapper.Map<List<AttendanceDTO>>(attendances);
    }

    public async Task<List<AttendanceDTO>> GetByStatusAsync(int status)
    {
        var attendances = await _attendanceDb.GetByStatusAsync(status);
        return _mapper.Map<List<AttendanceDTO>>(attendances);
    }

    public async Task<List<AttendanceDTO>> GetByProfessionalIdAsync(int professionalId)
    {
        var attendances = await _attendanceDb.GetByProfessionalIdAsync(professionalId);
        return _mapper.Map<List<AttendanceDTO>>(attendances);
    }

    public async Task<AttendanceDTO> UpdateStatusAsync(long id, int newStatus)
    {
        var updatedAttendance = await _attendanceDb.UpdateStatusAsync(id, newStatus);
        return _mapper.Map<AttendanceDTO>(updatedAttendance);
    }

    public async Task<AttendanceDTO> AssignProfessionalAsync(long id, int professionalId)
    {
        var updatedAttendance = await _attendanceDb.AssignProfessionalAsync(id, professionalId);
        return _mapper.Map<AttendanceDTO>(updatedAttendance);
    }

    /// <summary>
    /// Cria solicitação de serviço com resolução automática de Customer/Brand/Model
    /// Move lógica de negócio do frontend para backend seguindo padrões arquiteturais
    /// </summary>
    public async Task<AttendanceDTO> CreateServiceRequestAsync(ServiceRequestDTO serviceRequest)
    {
        try
        {
            _logger.LogInformation("Iniciando criação de service request para: {Email}", serviceRequest.Email);

            // 1. Validar dados básicos
            ValidateServiceRequest(serviceRequest);

            // 2. Resolver Customer (buscar ou criar)
            var customerId = await ResolveOrCreateCustomerAsync(serviceRequest);
            _logger.LogInformation("Customer resolvido: ID {CustomerId}", customerId);

            // 3. Resolver Brand (buscar ou criar)
            var brandId = await ResolveOrCreateBrandAsync(serviceRequest.Marca);
            _logger.LogInformation("Brand resolvida: ID {BrandId}", brandId);

            // 4. Resolver Model (buscar ou criar)
            var modelId = await ResolveOrCreateModelAsync(serviceRequest.Modelo, brandId);
            _logger.LogInformation("Model resolvido: ID {ModelId}", modelId);

            // 5. Converter para AttendanceDTO
            var attendanceDto = await MapServiceRequestToAttendanceAsync(serviceRequest, customerId, brandId, modelId);

            // 6. Salvar attendance
            var savedAttendance = await SaveAsync(attendanceDto);
            
            _logger.LogInformation("Service request criado com sucesso: GUID {Guid}", savedAttendance.Guid);
            return savedAttendance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar service request para {Email}", serviceRequest.Email);
            throw;
        }
    }

    /// <summary>
    /// Busca atendimentos com paginação para listagem otimizada
    /// </summary>
    public async Task<PagedResultDTO<AttendanceListDTO>> GetPagedListAsync(AttendancePagedRequestDTO request)
    {
        try
        {
            _logger.LogInformation("Iniciando busca paginada de atendimentos. Página: {PageNumber}, Tamanho: {PageSize}, Termo: {SearchTerm}", 
                request.PageNumber, request.PageSize, request.SearchTerm);
            
            // Delegar para a camada Database
            var result = await _attendanceDb.GetPagedListAsync(request);
            
            _logger.LogInformation("Busca paginada de atendimentos concluída. {ItemCount} atendimentos retornados de {TotalCount} total", 
                result.Items.Count, result.TotalCount);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar lista paginada de atendimentos");
            throw;
        }
    }

    private void ValidateServiceRequest(ServiceRequestDTO serviceRequest)
    {
        var errors = new List<string>();

        // Validações básicas
        if (string.IsNullOrWhiteSpace(serviceRequest.NomeCompleto) || serviceRequest.NomeCompleto.Length < 3)
            errors.Add("Nome completo deve ter pelo menos 3 caracteres");

        if (string.IsNullOrWhiteSpace(serviceRequest.Email) || !IsValidEmail(serviceRequest.Email))
            errors.Add("Email inválido");

        if (serviceRequest.ClientType == "juridica")
        {
            if (string.IsNullOrWhiteSpace(serviceRequest.Cnpj) || !IsValidCNPJ(serviceRequest.Cnpj))
                errors.Add("CNPJ inválido para pessoa jurídica");
        }
        else
        {
            if (string.IsNullOrWhiteSpace(serviceRequest.Cpf) || !IsValidCPF(serviceRequest.Cpf))
                errors.Add("CPF inválido para pessoa física");
        }

        if (string.IsNullOrWhiteSpace(serviceRequest.Placa) || !IsValidPlaca(serviceRequest.Placa))
            errors.Add("Placa do veículo inválida");

        if (string.IsNullOrWhiteSpace(serviceRequest.Marca) || serviceRequest.Marca.Length < 2)
            errors.Add("Marca do veículo é obrigatória");

        if (string.IsNullOrWhiteSpace(serviceRequest.Modelo) || serviceRequest.Modelo.Length < 2)
            errors.Add("Modelo do veículo é obrigatório");

        if (!new[] { "cautelar", "vistoria" }.Contains(serviceRequest.TipoServico))
            errors.Add("Tipo de serviço deve ser 'cautelar' ou 'vistoria'");

        if (errors.Any())
            throw new ArgumentException($"Dados inválidos: {string.Join(", ", errors)}");
    }

    private async Task<long> ResolveOrCreateCustomerAsync(ServiceRequestDTO serviceRequest)
    {
        try
        {
            _logger.LogInformation("Iniciando resolução de customer para: {Name}, Tipo: {Type}", 
                serviceRequest.NomeCompleto, serviceRequest.ClientType);

            var fiscalNumber = serviceRequest.ClientType == "juridica" ? 
                Regex.Replace(serviceRequest.Cnpj ?? "", @"\D", "") : 
                Regex.Replace(serviceRequest.Cpf ?? "", @"\D", "");

            if (string.IsNullOrEmpty(fiscalNumber))
            {
                _logger.LogError("CPF/CNPJ vazio ou nulo. ClientType: {ClientType}, CPF: {CPF}, CNPJ: {CNPJ}", 
                    serviceRequest.ClientType, serviceRequest.Cpf, serviceRequest.Cnpj);
                throw new ArgumentException("CPF/CNPJ é obrigatório");
            }

            _logger.LogInformation("Buscando customer com fiscal number: {FiscalNumber}", fiscalNumber);

            // Tentar buscar customer existente por fiscal number
            // Primeiro busca todos os customers e depois filtra em memória devido a limitação do EF com Regex
            var customers = await _context.Customers.ToListAsync();
            var existingCustomer = customers
                .FirstOrDefault(c => Regex.Replace(c.FiscalNumber ?? "", @"\D", "") == fiscalNumber);

            if (existingCustomer != null)
            {
                _logger.LogInformation("Customer existente encontrado: ID {CustomerId}, Nome: {Name}", 
                    existingCustomer.Id, existingCustomer.Name);
                return existingCustomer.Id;
            }

            _logger.LogInformation("Customer não encontrado, criando novo customer");

            // Customer não existe, criar novo
            var customerDto = new CustomerDTO
            {
                Guid = Guid.NewGuid(),
                Name = serviceRequest.ClientType == "juridica" ? 
                    (serviceRequest.RazaoSocial ?? serviceRequest.NomeFantasia ?? serviceRequest.NomeCompleto) : 
                    serviceRequest.NomeCompleto,
                Type = serviceRequest.ClientType == "juridica" ? "PJ" : "PF",
                FiscalNumber = fiscalNumber,
                BirthDate = null, // TODO: Capturar data de nascimento se necessário
                Address = serviceRequest.Endereco ?? "",
                Number = serviceRequest.Numero ?? "",
                AddressComplement = serviceRequest.Complemento ?? "",
                DistrictId = 1, // TODO: Resolver baseado no CEP
                CityId = 1, // TODO: Resolver baseado no CEP
                StateId = 1, // TODO: Resolver baseado no CEP
                Active = true,
                Deleted = false
            };

            _logger.LogInformation("Criando customer com dados: Name={Name}, Type={Type}, FiscalNumber={FiscalNumber}, Address={Address}", 
                customerDto.Name, customerDto.Type, customerDto.FiscalNumber, customerDto.Address);

            // NOTA: Email e telefone do serviceRequest não são salvos no Customer atualmente
            // TODO: Adicionar campos de contato no modelo Customer ou criar tabela de contatos
            _logger.LogWarning("Email ({Email}) e Celular ({Celular}) recebidos mas não salvos - campos não existem no modelo Customer", 
                serviceRequest.Email, serviceRequest.Celular);

            var createdCustomer = await _customerBusiness.SaveAsync(customerDto);
            _logger.LogInformation("Novo customer criado com sucesso: ID {CustomerId}, GUID {Guid}", 
                createdCustomer.Id, createdCustomer.Guid);
            return createdCustomer.Id;
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "Erro de validação ao resolver customer");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro inesperado ao resolver customer. ServiceRequest: {@ServiceRequest}", serviceRequest);
            throw new InvalidOperationException($"Erro ao processar dados do cliente: {ex.Message}", ex);
        }
    }

    private async Task<long> ResolveOrCreateBrandAsync(string brandName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(brandName) || brandName.Length < 2)
                throw new ArgumentException("Nome da marca é obrigatório");

            var normalizedBrandName = brandName.Trim().ToUpperInvariant();

            // Buscar brand existente
            var existingBrand = await _context.Brands
                .FirstOrDefaultAsync(b => b.Brand.ToUpper() == normalizedBrandName);

            if (existingBrand != null)
            {
                _logger.LogInformation("Brand existente encontrada: {BrandName} - ID {BrandId}", brandName, existingBrand.Id);
                return existingBrand.Id;
            }

            // Brand não existe, criar nova
            var newBrand = new BrandModel
            {
                Brand = brandName.Trim()
            };

            _context.Brands.Add(newBrand);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Nova brand criada: {BrandName} - ID {BrandId}", brandName, newBrand.Id);
            return newBrand.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao resolver brand: {BrandName}", brandName);
            throw new InvalidOperationException("Erro ao processar marca do veículo", ex);
        }
    }

    private async Task<long> ResolveOrCreateModelAsync(string modelName, long brandId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(modelName) || modelName.Length < 2)
                throw new ArgumentException("Nome do modelo é obrigatório");

            var normalizedModelName = modelName.Trim().ToUpperInvariant();

            // Buscar model existente para a marca
            var existingModel = await _context.Models
                .FirstOrDefaultAsync(m => m.Model.ToUpper() == normalizedModelName && m.BrandId == brandId);

            if (existingModel != null)
            {
                _logger.LogInformation("Model existente encontrado: {ModelName} - ID {ModelId}", modelName, existingModel.Id);
                return existingModel.Id;
            }

            // Model não existe, criar novo
            var newModel = new ModelModel
            {
                Model = modelName.Trim(),
                BrandId = brandId
            };

            _context.Models.Add(newModel);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Novo model criado: {ModelName} - ID {ModelId}", modelName, newModel.Id);
            return newModel.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao resolver model: {ModelName}", modelName);
            throw new InvalidOperationException("Erro ao processar modelo do veículo", ex);
        }
    }

    private async Task<AttendanceDTO> MapServiceRequestToAttendanceAsync(ServiceRequestDTO serviceRequest, long customerId, long brandId, long modelId)
    {
        var currentDate = DateTime.UtcNow;
        
        // Buscar endereço por CEP se necessário
        AddressDTO? addressData = null;
        if (!string.IsNullOrWhiteSpace(serviceRequest.Cep))
        {
            addressData = await _addressService.GetAddressByCepAsync(serviceRequest.Cep);
        }
        
        return new AttendanceDTO
        {
            Guid = Guid.NewGuid(),
            DateRequest = currentDate,
            CustomerId = customerId,
            ProfessionalId = null, // Será atribuído posteriormente
            ReferenceCar = $"{serviceRequest.Placa} - {serviceRequest.Marca} {serviceRequest.Modelo} ({serviceRequest.Cor})",
            BrandId = brandId,
            ModelId = modelId,
            PostalCode = Regex.Replace(serviceRequest.Cep ?? "", @"\D", ""),
            Address = addressData?.Logradouro ?? serviceRequest.Endereco ?? "",
            Number = serviceRequest.Numero ?? "",
            AddressComplement = serviceRequest.Complemento ?? "",
            DistrictId = 1, // TODO: Integrar com API de District
            CityId = 1, // TODO: Integrar com API de City
            StateId = 1, // TODO: Integrar com API de State
            Status = 1, // AttendanceStatus.Requested
            
            // Campos específicos do Service Request
            VehicleOwnerName = serviceRequest.NomeProprietario ?? "",
            VehicleOwnerPhone = Regex.Replace(serviceRequest.CelularProprietario ?? "", @"\D", ""),
            HasCautelarService = serviceRequest.TipoServico == "cautelar",
            HasVistoriaService = serviceRequest.TipoServico == "vistoria",
            CustomerNotes = serviceRequest.Observacoes ?? "",
            
            // Endereço alternativo do veículo
            VehicleAtDifferentAddress = serviceRequest.EnderecoVeiculo == "outro",
            VehiclePostalCode = serviceRequest.EnderecoVeiculo == "outro" ? Regex.Replace(serviceRequest.CepVeiculo ?? "", @"\D", "") : null,
            VehicleAddress = serviceRequest.EnderecoVeiculo == "outro" ? serviceRequest.EnderecoVeiculoCompleto : null,
            VehicleNumber = serviceRequest.EnderecoVeiculo == "outro" ? serviceRequest.NumeroVeiculo : null,
            VehicleAddressComplement = serviceRequest.EnderecoVeiculo == "outro" ? serviceRequest.ComplementoVeiculo : null,
            VehicleDistrictId = serviceRequest.EnderecoVeiculo == "outro" ? 1 : null, // TODO: Resolver ID baseado no CEP
            VehicleCityId = serviceRequest.EnderecoVeiculo == "outro" ? 1 : null, // TODO: Resolver ID baseado no CEP
            VehicleStateId = serviceRequest.EnderecoVeiculo == "outro" ? 1 : null, // TODO: Resolver ID baseado no CEP
            
            // Navigation properties como null (serão carregadas pelo backend)
            Customer = null,
            Professional = null,
            Brand = null,
            Model = null,
            District = null,
            City = null,
            State = null,
            VehicleDistrict = null,
            VehicleCity = null,
            VehicleState = null
        };
    }

    #region Validation Helpers
    
    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email)) return false;
        return Regex.IsMatch(email, @"^[^\s@]+@[^\s@]+\.[^\s@]+$");
    }
    
    private static bool IsValidCPF(string cpf)
    {
        if (string.IsNullOrWhiteSpace(cpf)) return false;
        
        var cleanCPF = Regex.Replace(cpf, @"\D", "");
        if (cleanCPF.Length != 11) return false;
        if (Regex.IsMatch(cleanCPF, @"^(\d)\1{10}$")) return false;

        int sum = 0;
        for (int i = 0; i < 9; i++)
            sum += int.Parse(cleanCPF[i].ToString()) * (10 - i);
        int remainder = (sum * 10) % 11;
        if (remainder == 10 || remainder == 11) remainder = 0;
        if (remainder != int.Parse(cleanCPF[9].ToString())) return false;

        sum = 0;
        for (int i = 0; i < 10; i++)
            sum += int.Parse(cleanCPF[i].ToString()) * (11 - i);
        remainder = (sum * 10) % 11;
        if (remainder == 10 || remainder == 11) remainder = 0;
        if (remainder != int.Parse(cleanCPF[10].ToString())) return false;

        return true;
    }
    
    private static bool IsValidCNPJ(string cnpj)
    {
        if (string.IsNullOrWhiteSpace(cnpj)) return false;
        
        var cleanCNPJ = Regex.Replace(cnpj, @"\D", "");
        if (cleanCNPJ.Length != 14) return false;
        if (Regex.IsMatch(cleanCNPJ, @"^(\d)\1{13}$")) return false;

        int[] multiplicador1 = { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
        int[] multiplicador2 = { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
        
        string tempCnpj = cleanCNPJ.Substring(0, 12);
        int soma = 0;
        for (int i = 0; i < 12; i++)
            soma += int.Parse(tempCnpj[i].ToString()) * multiplicador1[i];
        int resto = (soma % 11);
        if (resto < 2) resto = 0;
        else resto = 11 - resto;
        string digito = resto.ToString();
        tempCnpj = tempCnpj + digito;
        soma = 0;
        for (int i = 0; i < 13; i++)
            soma += int.Parse(tempCnpj[i].ToString()) * multiplicador2[i];
        resto = (soma % 11);
        if (resto < 2) resto = 0;
        else resto = 11 - resto;
        digito = digito + resto.ToString();
        return cleanCNPJ.EndsWith(digito);
    }
    
    private static bool IsValidPlaca(string placa)
    {
        if (string.IsNullOrWhiteSpace(placa)) return false;
        
        var cleanPlaca = Regex.Replace(placa, @"[^A-Za-z0-9]", "");
        // Formato antigo: AAA0000 ou novo: AAA0A00
        return cleanPlaca.Length == 7 || cleanPlaca.Length == 8;
    }
    
    #endregion
}