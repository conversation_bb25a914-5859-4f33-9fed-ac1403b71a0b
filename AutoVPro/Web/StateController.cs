using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/state")]
    public class StateController : ControllerBase
    {
        private readonly AutoProDbContext _context;
        
        public StateController(AutoProDbContext context)
        {
            _context = context;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<StateDTO>> ListStates()
        {
            var states = await _context.States
                .Where(s => s.Active && !s.Deleted)
                .OrderBy(s => s.State)
                .Select(s => new StateDTO
                {
                    Id = s.Id,
                    State = s.State,
                    Guid = s.Guid,
                    Active = s.Active,
                    Deleted = s.Deleted,
                    CreatedOn = s.CreatedOn,
                    UpdatedOn = s.UpdatedOn
                })
                .ToListAsync();
                
            return states;
        }
        
        [HttpGet]
        [Route("{id:long}")]
        public async Task<ActionResult<StateDTO>> GetState([FromRoute] long id)
        {
            var state = await _context.States
                .Where(s => s.Id == id && s.Active && !s.Deleted)
                .Select(s => new StateDTO
                {
                    Id = s.Id,
                    State = s.State,
                    Guid = s.Guid,
                    Active = s.Active,
                    Deleted = s.Deleted,
                    CreatedOn = s.CreatedOn,
                    UpdatedOn = s.UpdatedOn
                })
                .FirstOrDefaultAsync();
                
            if (state == null)
                return NotFound($"State with id {id} not found");
                
            return state;
        }
        
        [HttpGet]
        [Route("search/{name}")]
        public async Task<List<StateDTO>> SearchStates([FromRoute] string name)
        {
            var states = await _context.States
                .Where(s => s.State.Contains(name) && s.Active && !s.Deleted)
                .OrderBy(s => s.State)
                .Select(s => new StateDTO
                {
                    Id = s.Id,
                    State = s.State,
                    Guid = s.Guid,
                    Active = s.Active,
                    Deleted = s.Deleted,
                    CreatedOn = s.CreatedOn,
                    UpdatedOn = s.UpdatedOn
                })
                .ToListAsync();
                
            return states;
        }
    }
}