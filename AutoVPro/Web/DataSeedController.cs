using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Model;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/data-seed")]
    public class DataSeedController : ControllerBase
    {
        private readonly AutoProDbContext _context;
        
        public DataSeedController(AutoProDbContext context)
        {
            _context = context;       
        }
        
        [HttpPost]
        [Route("insert-test-data")]
        public async Task<IActionResult> InsertTestData()
        {
            try
            {
                // Estados
                if (!await _context.States.AnyAsync())
                {
                    var states = new List<StateModel>
                    {
                        new StateModel { State = "São Paulo", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new StateModel { State = "Rio de Janeiro", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new StateModel { State = "Minas Gerais", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow }
                    };
                    
                    await _context.States.AddRangeAsync(states);
                    await _context.SaveChangesAsync();
                }

                // Cidades
                if (!await _context.Cities.AnyAsync())
                {
                    var cities = new List<CityModel>
                    {
                        new CityModel { City = "São Paulo", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new CityModel { City = "Campinas", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new CityModel { City = "Santos", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new CityModel { City = "Rio de Janeiro", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new CityModel { City = "Belo Horizonte", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow }
                    };
                    
                    await _context.Cities.AddRangeAsync(cities);
                    await _context.SaveChangesAsync();
                }

                // Bairros
                if (!await _context.Districts.AnyAsync())
                {
                    var districts = new List<DistrictModel>
                    {
                        new DistrictModel { District = "Centro", Code = "01001", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new DistrictModel { District = "Vila Madalena", Code = "05432", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new DistrictModel { District = "Copacabana", Code = "22071", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new DistrictModel { District = "Ipanema", Code = "22411", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow },
                        new DistrictModel { District = "Savassi", Code = "30112", Guid = Guid.NewGuid(), Active = true, Deleted = false, CreatedOn = DateTime.UtcNow, UpdatedOn = DateTime.UtcNow }
                    };
                    
                    await _context.Districts.AddRangeAsync(districts);
                    await _context.SaveChangesAsync();
                }

                // Marcas
                if (!await _context.Brands.AnyAsync())
                {
                    var brands = new List<BrandModel>
                    {
                        new BrandModel { Brand = "Volkswagen" },
                        new BrandModel { Brand = "Fiat" },
                        new BrandModel { Brand = "Ford" },
                        new BrandModel { Brand = "Chevrolet" },
                        new BrandModel { Brand = "Toyota" }
                    };
                    
                    await _context.Brands.AddRangeAsync(brands);
                    await _context.SaveChangesAsync();
                }

                // Modelos
                if (!await _context.Models.AnyAsync())
                {
                    var volkswagen = await _context.Brands.FirstAsync(b => b.Brand == "Volkswagen");
                    var fiat = await _context.Brands.FirstAsync(b => b.Brand == "Fiat");
                    var ford = await _context.Brands.FirstAsync(b => b.Brand == "Ford");
                    var chevrolet = await _context.Brands.FirstAsync(b => b.Brand == "Chevrolet");
                    var toyota = await _context.Brands.FirstAsync(b => b.Brand == "Toyota");

                    var models = new List<ModelModel>
                    {
                        new ModelModel { Model = "Gol", BrandId = volkswagen.Id },
                        new ModelModel { Model = "Fox", BrandId = volkswagen.Id },
                        new ModelModel { Model = "Uno", BrandId = fiat.Id },
                        new ModelModel { Model = "Palio", BrandId = fiat.Id },
                        new ModelModel { Model = "Ka", BrandId = ford.Id },
                        new ModelModel { Model = "Fiesta", BrandId = ford.Id },
                        new ModelModel { Model = "Celta", BrandId = chevrolet.Id },
                        new ModelModel { Model = "Corsa", BrandId = chevrolet.Id },
                        new ModelModel { Model = "Corolla", BrandId = toyota.Id },
                        new ModelModel { Model = "Etios", BrandId = toyota.Id }
                    };
                    
                    await _context.Models.AddRangeAsync(models);
                    await _context.SaveChangesAsync();
                }

                return Ok(new { message = "Dados de teste inseridos com sucesso!", success = true });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = $"Erro ao inserir dados: {ex.Message}", success = false });
            }
        }
        
        [HttpGet]
        [Route("status")]
        public async Task<IActionResult> GetDataStatus()
        {
            var status = new
            {
                States = await _context.States.CountAsync(),
                Cities = await _context.Cities.CountAsync(),
                Districts = await _context.Districts.CountAsync(),
                Brands = await _context.Brands.CountAsync(),
                Models = await _context.Models.CountAsync(),
                Customers = await _context.Customers.CountAsync(),
                Attendances = await _context.Attendances.CountAsync()
            };
            
            return Ok(status);
        }
    }
}