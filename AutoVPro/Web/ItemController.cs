using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/item")]
    public class ItemController : ControllerBase
    {
        private readonly IItemBusiness _itemBusiness;
        
        public ItemController(IItemBusiness itemBusiness)
        {
            _itemBusiness = itemBusiness;       
        }
        
        /// <summary>
        /// Lista todos os itens ativos
        /// </summary>
        /// <returns>Lista de itens</returns>
        [HttpGet]
        public async Task<IActionResult> ListItems()
        {
            try
            {
                List<ItemDTO> items = await _itemBusiness.GetAllAsync();
                return Ok(items);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        /// <summary>
        /// Endpoint otimizado para listagem com paginação e filtros
        /// </summary>
        [HttpGet("paged")]
        public async Task<IActionResult> GetPaged([FromQuery] PagedRequestDTO request)
        {
            try
            {
                if (request.PageSize > 100) // Limitar tamanho máximo da página
                {
                    request.PageSize = 100;
                }

                var result = await _itemBusiness.GetPagedListAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Busca um item por ID
        /// </summary>
        /// <param name="id">ID do item</param>
        /// <returns>Item encontrado</returns>
        [HttpGet]
        [Route("{id:long}")]
        public async Task<IActionResult> GetItem([FromRoute] long id)
        {
            try
            {
                var item = await _itemBusiness.GetByIdAsync(id);
                if (item == null)
                    return NotFound($"Item com ID {id} não encontrado");
                    
                return Ok(item);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Cria um novo item
        /// </summary>
        /// <param name="itemDto">Dados do item</param>
        /// <returns>Item criado</returns>
        [HttpPost]
        [Route("add")]
        public async Task<IActionResult> AddItem([FromBody] ItemDTO itemDto)
        {
            try
            {
                if (itemDto == null)
                    return BadRequest("Dados do item são obrigatórios");
                
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);
                
                var createdItem = await _itemBusiness.SaveAsync(itemDto);
                return CreatedAtAction(nameof(GetItem), new { id = createdItem.Id }, createdItem);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Atualiza um item existente
        /// </summary>
        /// <param name="itemDto">Dados atualizados do item</param>
        /// <returns>Item atualizado</returns>
        [HttpPut]
        [Route("update/{id:int}")]
        public async Task<IActionResult> UpdateItem([FromBody] ItemDTO itemDto)
        {
            try
            {
                if (itemDto == null)
                    return BadRequest("Dados do item são obrigatórios");
                
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);
                
                var updatedItem = await _itemBusiness.UpdateAsync(itemDto);
                return Ok(updatedItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Remove um item (soft delete)
        /// </summary>
        /// <param name="id">ID do item</param>
        /// <returns>Item removido</returns>
        [HttpDelete]
        [Route("delete/{id:int}")]
        public async Task<IActionResult> DeleteItem([FromRoute] int id)
        {
            try
            {
                var deletedItem = await _itemBusiness.DeleteAsync(id);
                return Ok(deletedItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Busca itens por nome
        /// </summary>
        /// <param name="name">Nome para busca</param>
        /// <returns>Lista de itens encontrados</returns>
        [HttpGet]
        [Route("search/{name}")]
        public async Task<IActionResult> SearchItems([FromRoute] string name)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                    return BadRequest("Nome para busca é obrigatório");
                
                var items = await _itemBusiness.SearchByNameAsync(name);
                return Ok(items);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Lista itens por status (ativo/inativo)
        /// </summary>
        /// <param name="isActive">Status do item</param>
        /// <returns>Lista de itens filtrados por status</returns>
        [HttpGet]
        [Route("status/{isActive:bool}")]
        public async Task<IActionResult> GetItemsByStatus([FromRoute] bool isActive)
        {
            try
            {
                var items = await _itemBusiness.GetByStatusAsync(isActive);
                return Ok(items);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
    }
}
