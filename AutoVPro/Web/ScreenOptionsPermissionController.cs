using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [Route("api/screenoptionpermission")]
    [ApiController]
    public class ScreenOptionsPermissionController : ControllerBase
    {
        private readonly IScreenOptionsPermissionsBusiness<ScreenOptionPermissionDTO> _screenOptionsPermissionsBusiness;

        public ScreenOptionsPermissionController(
            IScreenOptionsPermissionsBusiness<ScreenOptionPermissionDTO> screenOptionsPermissionsBusiness)
        {
            _screenOptionsPermissionsBusiness = screenOptionsPermissionsBusiness;       
        }
        
        [HttpPost]
        [Route("add")]
        public async Task<ScreenOptionPermissionDTO> SaveAsync(ScreenOptionPermissionDTO screenOptionPermissionDTO) {
            return await _screenOptionsPermissionsBusiness.SaveAsync(screenOptionPermissionDTO);
        }

        [HttpGet]
        [Route("list/{screenId:int}/{userGroupId:int}")]
        public async Task<IList<ScreenOptionPermissionDTO>> GetAllAsync([FromRoute] int screenId, [FromRoute] int userGroupId)
        {
            return await _screenOptionsPermissionsBusiness.GetAllAsync(screenId, userGroupId);
        }
        [HttpGet]
        [Route("list-permissions/{screenId:int}/{userGroupId:int}")]
        public async Task<IList<ScreenOptionsPermissionCompleteDTO>> GetAllPermissionsAsync([FromRoute] int screenId, [FromRoute] int userGroupId)
        {
            return await _screenOptionsPermissionsBusiness.GetAllPermissionsAsync(screenId, userGroupId);
        }

        [HttpPut]
        [Route("update")]
        public async Task<bool> UpdateAsync([FromBody] ScreenOptionPermissionDTO screenOptionPermissionDTO)
        {
            var screenOptionId = screenOptionPermissionDTO.ScreenOptionId;
            var userGroupId = screenOptionPermissionDTO.UserGroupId;
            var allow = screenOptionPermissionDTO.Allow;
            
            return await _screenOptionsPermissionsBusiness.UpdateAsync(screenOptionId, userGroupId, allow);
        }
    }
}
