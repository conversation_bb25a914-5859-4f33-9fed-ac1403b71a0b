using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/service")]
    public class ServiceController : ControllerBase
    {
        private readonly IServiceBusiness _serviceBusiness;
        
        public ServiceController(
            IServiceBusiness serviceBusiness
            )
        {
            _serviceBusiness = serviceBusiness;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<ServiceDTO>> ListService()
        {
            List<ServiceDTO> lst = await _serviceBusiness.GetAllAsync();
            return lst;
        }

        /// <summary>
        /// Endpoint otimizado para listagem com paginação e filtros
        /// </summary>
        [HttpPost("paged")]
        public async Task<IActionResult> GetPaged([FromBody] PagedRequestDTO request)
        {
            try
            {
                if (request.PageSize > 100) // Limitar tamanho máximo da página
                {
                    request.PageSize = 100;
                }

                var result = await _serviceBusiness.GetPagedListAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erro interno do servidor");
            }
        }
        
        [HttpPost]
        [Route("add")]
        public async Task<ServiceDTO> AddService(ServiceDTO service)
        {
            var serviceDto = await _serviceBusiness.SaveAsync(service);
            return serviceDto;
        }
        
        [HttpGet]
        [Route("id/{guid}")]
        public async Task<ServiceDTO> GetService(
            [FromRoute] Guid guid)
        {
            ServiceDTO service = await _serviceBusiness.GetByIdAsync(guid);
            return service;
        }

        [HttpPut]
        [Route("update")]
        public async Task<ServiceDTO> UpdateService(ServiceDTO service)
        {
            await _serviceBusiness.UpdateAsync(service);
            return service;
        }

        [HttpDelete]
        [Route("delete/{id:int}")]
        public async Task<ServiceDTO> DeleteService(
            [FromRoute] int id
            )
        {
            var service = await _serviceBusiness.DeleteAsync(id);
            return service;
        }

        /// <summary>
        /// Adiciona dados de teste (temporário)
        /// </summary>
        /// <returns>Lista de serviços criados</returns>
        [HttpPost]
        [Route("seed-data")]
        public async Task<List<ServiceDTO>> SeedTestData()
        {
            var services = new List<ServiceDTO>
            {
                new ServiceDTO
                {
                    Name = "Troca de Óleo",
                    Description = "Troca completa do óleo do motor com filtro",
                    ValueAmount = 120.00m,
                    Icon = "fa-oil-can",
                    Active = true,
                    Deleted = false
                },
                new ServiceDTO
                {
                    Name = "Alinhamento e Balanceamento",
                    Description = "Alinhamento das rodas e balanceamento dos pneus",
                    ValueAmount = 80.00m,
                    Icon = "fa-cog",
                    Active = true,
                    Deleted = false
                },
                new ServiceDTO
                {
                    Name = "Revisão Completa",
                    Description = "Revisão geral do veículo com checklist completo",
                    ValueAmount = 350.00m,
                    Icon = "fa-clipboard-check",
                    Active = true,
                    Deleted = false
                },
                new ServiceDTO
                {
                    Name = "Troca de Pastilhas de Freio",
                    Description = "Substituição das pastilhas de freio dianteiras e traseiras",
                    ValueAmount = 200.00m,
                    Icon = "fa-brake-warning",
                    Active = true,
                    Deleted = false
                },
                new ServiceDTO
                {
                    Name = "Lavagem Completa",
                    Description = "Lavagem externa e interna do veículo com enceramento",
                    ValueAmount = 45.00m,
                    Icon = "fa-car-wash",
                    Active = true,
                    Deleted = false
                }
            };

            var createdServices = new List<ServiceDTO>();
            foreach (var service in services)
            {
                var created = await _serviceBusiness.SaveAsync(service);
                createdServices.Add(created);
            }

            return createdServices;
        }
    }
}
