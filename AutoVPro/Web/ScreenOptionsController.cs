using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [Route("api/screenoptions")]
    [ApiController]
    public class ScreenOptionsController : ControllerBase
    {
        private readonly IScreenOptionsBusiness<ScreenOptionsDTO> _screenOptionsBusiness;
        
        public ScreenOptionsController(
            IScreenOptionsBusiness<ScreenOptionsDTO> screenOptionsBusiness)
        {
            _screenOptionsBusiness = screenOptionsBusiness;
        }

        [HttpPost]
        [Route("add")]
        public async Task<ScreenOptionsDTO> SaveAsync([FromBody] ScreenOptionsDTO screenOptionsDTO)
        {
            var screenOptions = await _screenOptionsBusiness.SaveAsync(screenOptionsDTO);
            return screenOptions;
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<ScreenOptionsDTO>> GetAllAsync(int? screenId)
        {
            var screenOptions = await _screenOptionsBusiness.GetAllAsync(screenId);
            return screenOptions;
        }   
        
        [HttpDelete]
        [Route("delete/{id:int}")]
        public async Task<bool> DeleteAsync([FromRoute] int id)
        {
            return await _screenOptionsBusiness.DeleteAsync(id);
        }
    }
}
