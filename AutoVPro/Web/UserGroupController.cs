using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/usergroup")]
    public class UserGroupController : ControllerBase
    {
        private readonly IUserGroupBusiness _userGroupBusiness;
        public UserGroupController(
            IUserGroupBusiness userGroupBusiness
            )
        {
            _userGroupBusiness = userGroupBusiness;       
        }
        
        [HttpPost]
        [Route("add")]
        public async Task<UserGroupDTO> AddUserGroup(UserGroupDTO userGroup)
        {
            var userGroupDto = await _userGroupBusiness.SaveAsync(userGroup);
            return userGroupDto;
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<UserGroupDTO>> ListUserGroup()
        {
            List<UserGroupDTO> lst = await _userGroupBusiness.GetAllAsync();
            return lst;   
        }

        /// <summary>
        /// Endpoint otimizado para listagem com paginação e filtros
        /// </summary>
        [HttpGet("paged")]
        public async Task<IActionResult> GetPaged([FromQuery] PagedRequestDTO request)
        {
            try
            {
                if (request.PageSize > 100) // Limitar tamanho máximo da página
                {
                    request.PageSize = 100;
                }

                var result = await _userGroupBusiness.GetPagedListAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erro interno do servidor");
            }
        }

        [HttpGet]
        [Route("id/{guid}")]
        public async Task<UserGroupDTO> GetUserGroup(
            [FromRoute] Guid guid)
        {
            UserGroupDTO user = await _userGroupBusiness.GetByIdAsync(guid);
            return user;
        }

        [HttpPut]
        [Route("update")]
        public async Task<UserGroupDTO> UpdateUserGroup(UserGroupDTO userGroup)
        {
            await _userGroupBusiness.UpdateAsync(userGroup);
            return userGroup;
        }
        
        [HttpDelete]
        [Route("delete/{id:int}")]
        public async Task<UserGroupDTO> DeleteUserGroup(
            [FromRoute] int id
            )
        {
            var userGroup = await _userGroupBusiness.DeleteAsync(id);
            return userGroup;      
        }
    }
}
