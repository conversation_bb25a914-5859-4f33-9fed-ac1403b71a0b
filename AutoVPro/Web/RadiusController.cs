using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web;

[Route("api/radius")]
[ApiController]
public class RadiusController : ControllerBase
{
    private readonly IRadiusBusiness _radiusBusiness;

    public RadiusController(IRadiusBusiness radiusBusiness)
    {
        _radiusBusiness = radiusBusiness;
    }

    [HttpPost]
    [Route("add")]
    public async Task<IActionResult> Add([FromBody] RadiusDTO radiusDTO)
    {
        try
        {
            if (radiusDTO == null)
            {
                return BadRequest("Dados do raio de atendimento são obrigatórios");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var exists = await _radiusBusiness.ExistsAsync(radiusDTO.RaioAtendimento);
            if (exists)
            {
                return Conflict(new {
                    message = $"Raio de atendimento {radiusDTO.RaioAtendimento} já existe na base de dados",
                    value = radiusDTO.RaioAtendimento,
                    suggestion = "Tente um valor diferente ou use o endpoint GET para ver os valores existentes"
                });
            }

            var result = await _radiusBusiness.SaveAsync(radiusDTO);
            return Ok(new {
                message = "Raio de atendimento adicionado com sucesso",
                data = result
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        try
        {
            var result = await _radiusBusiness.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(long id)
    {
        try
        {
            var result = await _radiusBusiness.GetByIdAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            if (ex.Message.Contains("não encontrado"))
            {
                return NotFound($"Raio de atendimento não encontrado: {ex.Message}");
            }
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(long id, [FromBody] RadiusDTO radiusDTO)
    {
        try
        {
            if (radiusDTO == null)
            {
                return BadRequest("Dados do raio de atendimento são obrigatórios");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            radiusDTO.Id = id;

            var result = await _radiusBusiness.UpdateAsync(radiusDTO);
            return Ok(result);
        }
        catch (Exception ex)
        {
            if (ex.Message.Contains("não encontrado"))
            {
                return NotFound($"Raio de atendimento não encontrado: {ex.Message}");
            }
            if (ex.Message.Contains("Já existe"))
            {
                return Conflict(ex.Message);
            }
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpDelete("delete/{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var result = await _radiusBusiness.DeleteAsync(id);
            return Ok(new { message = "Raio de atendimento deletado com sucesso", data = result });
        }
        catch (Exception ex)
        {
            if (ex.Message.Contains("não encontrado"))
            {
                return NotFound($"Raio de atendimento não encontrado: {ex.Message}");
            }
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpGet("exists/{raioAtendimento}")]
    public async Task<IActionResult> CheckExists(int raioAtendimento)
    {
        try
        {
            var exists = await _radiusBusiness.ExistsAsync(raioAtendimento);
            return Ok(new {
                raioAtendimento = raioAtendimento,
                exists = exists,
                message = exists ? "Raio de atendimento já existe" : "Raio de atendimento disponível"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpGet("test")]
    public IActionResult Test()
    {
        return Ok(new { message = "RadiusController funcionando!", timestamp = DateTime.Now });
    }
}
