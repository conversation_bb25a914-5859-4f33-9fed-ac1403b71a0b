using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/attendance")]
    public class AttendanceController : ControllerBase
    {
        private readonly IAttendanceBusiness _attendanceBusiness;
        
        public AttendanceController(IAttendanceBusiness attendanceBusiness)
        {
            _attendanceBusiness = attendanceBusiness;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<IActionResult> ListAttendances()
        {
            try
            {
                List<AttendanceDTO> lst = await _attendanceBusiness.GetAllAsync();
                return Ok(lst);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("paged")]
        public async Task<IActionResult> GetPaged([FromQuery] AttendancePagedRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Parâmetros de paginação são obrigatórios");
                }

                if (request.PageSize > 100) 
                {
                    request.PageSize = 100;
                }
                
                if (request.PageNumber < 1)
                {
                    request.PageNumber = 1;
                }

                var result = await _attendanceBusiness.GetPagedListAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpPost]
        [Route("add")]
        public async Task<IActionResult> AddAttendance([FromBody] AttendanceDTO attendance)
        {
            try
            {
                if (attendance == null)
                {
                    return BadRequest("Dados do atendimento são obrigatórios");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var attendanceDto = await _attendanceBusiness.SaveAsync(attendance);
                return Ok(attendanceDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("id/{guid}")]
        public async Task<IActionResult> GetAttendance([FromRoute] Guid guid)
        {
            try
            {
                AttendanceDTO attendance = await _attendanceBusiness.GetByIdAsync(guid);
                return Ok(attendance);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound($"Atendimento não encontrado: {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpPut]
        [Route("update")]
        public async Task<IActionResult> UpdateAttendance([FromBody] AttendanceDTO attendance)
        {
            try
            {
                if (attendance == null)
                {
                    return BadRequest("Dados do atendimento são obrigatórios");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _attendanceBusiness.UpdateAsync(attendance);
                return Ok(result);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound($"Atendimento não encontrado: {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpDelete]
        [Route("delete/{id:int}")]
        public async Task<IActionResult> DeleteAttendance([FromRoute] int id)
        {
            try
            {
                var attendance = await _attendanceBusiness.DeleteAsync(id);
                return Ok(new { message = "Atendimento deletado com sucesso", data = attendance });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound($"Atendimento não encontrado: {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("customer/{customerId:int}")]
        public async Task<IActionResult> GetByCustomer([FromRoute] int customerId)
        {
            try
            {
                var attendances = await _attendanceBusiness.GetByCustomerIdAsync(customerId);
                return Ok(attendances);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("status/{status:int}")]
        public async Task<IActionResult> GetByStatus([FromRoute] int status)
        {
            try
            {
                var attendances = await _attendanceBusiness.GetByStatusAsync(status);
                return Ok(attendances);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("professional/{professionalId:int}")]
        public async Task<IActionResult> GetByProfessional([FromRoute] int professionalId)
        {
            try
            {
                var attendances = await _attendanceBusiness.GetByProfessionalIdAsync(professionalId);
                return Ok(attendances);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpPut]
        [Route("{id:long}/status/{status:int}")]
        public async Task<IActionResult> UpdateStatus([FromRoute] long id, [FromRoute] int status)
        {
            try
            {
                var attendance = await _attendanceBusiness.UpdateStatusAsync(id, status);
                return Ok(new { message = "Status atualizado com sucesso", data = attendance });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound($"Atendimento não encontrado: {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpPut]
        [Route("{id:long}/assign/{professionalId:int}")]
        public async Task<IActionResult> AssignProfessional([FromRoute] long id, [FromRoute] int professionalId)
        {
            try
            {
                var attendance = await _attendanceBusiness.AssignProfessionalAsync(id, professionalId);
                return Ok(new { message = "Profissional atribuído com sucesso", data = attendance });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound($"Atendimento não encontrado: {ex.Message}");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Endpoint dedicado para criação de solicitações de serviço via site público
        /// Move a lógica de resolução de IDs e validação para o backend
        /// </summary>
        [HttpPost]
        [Route("service-request")]
        public async Task<IActionResult> CreateServiceRequest([FromBody] ServiceRequestDTO serviceRequest)
        {
            try
            {
                if (serviceRequest == null)
                {
                    return BadRequest("Dados da solicitação de serviço são obrigatórios");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var attendance = await _attendanceBusiness.CreateServiceRequestAsync(serviceRequest);
                return Ok(new { 
                    success = true, 
                    message = "Solicitação enviada com sucesso! Entraremos em contato em breve.", 
                    requestId = attendance.Guid.ToString(),
                    data = attendance 
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"Erro interno do servidor: {ex.Message}" });
            }
        }

        // TODO: Future payment integration endpoints
        // [HttpPost]
        // [Route("{id:long}/payment")]
        // public async Task<AttendancePaymentDTO> ProcessPayment([FromRoute] long id, [FromBody] PaymentRequestDTO payment)
        // {
        //     // Implementation will be added when payment is enabled
        //     throw new NotImplementedException("Payment processing will be implemented in the future");
        // }
    }
}