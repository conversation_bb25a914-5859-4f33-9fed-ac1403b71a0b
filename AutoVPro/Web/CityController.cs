using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/city")]
    public class CityController : ControllerBase
    {
        private readonly AutoProDbContext _context;
        
        public CityController(AutoProDbContext context)
        {
            _context = context;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<CityDTO>> ListCities()
        {
            var cities = await _context.Cities
                .Where(c => c.Active && !c.Deleted)
                .OrderBy(c => c.City)
                .Select(c => new CityDTO
                {
                    Id = c.Id,
                    City = c.City,
                    Guid = c.Guid,
                    Active = c.Active,
                    Deleted = c.Deleted,
                    CreatedOn = c.CreatedOn,
                    UpdatedOn = c.UpdatedOn
                })
                .ToListAsync();
                
            return cities;
        }
        
        [HttpGet]
        [Route("{id:long}")]
        public async Task<ActionResult<CityDTO>> GetCity([FromRoute] long id)
        {
            var city = await _context.Cities
                .Where(c => c.Id == id && c.Active && !c.Deleted)
                .Select(c => new CityDTO
                {
                    Id = c.Id,
                    City = c.City,
                    Guid = c.Guid,
                    Active = c.Active,
                    Deleted = c.Deleted,
                    CreatedOn = c.CreatedOn,
                    UpdatedOn = c.UpdatedOn
                })
                .FirstOrDefaultAsync();
                
            if (city == null)
                return NotFound($"City with id {id} not found");
                
            return city;
        }
        
        [HttpGet]
        [Route("search/{name}")]
        public async Task<List<CityDTO>> SearchCities([FromRoute] string name)
        {
            var cities = await _context.Cities
                .Where(c => c.City.Contains(name) && c.Active && !c.Deleted)
                .OrderBy(c => c.City)
                .Select(c => new CityDTO
                {
                    Id = c.Id,
                    City = c.City,
                    Guid = c.Guid,
                    Active = c.Active,
                    Deleted = c.Deleted,
                    CreatedOn = c.CreatedOn,
                    UpdatedOn = c.UpdatedOn
                })
                .ToListAsync();
                
            return cities;
        }

    }
}