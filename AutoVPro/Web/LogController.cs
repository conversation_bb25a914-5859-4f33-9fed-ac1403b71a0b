using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [Route("api/log")]
    [ApiController]
    public class LogController : ControllerBase
    {
        private readonly ILogBusiness<LogDTO> _logBusiness; 
        
        public LogController(ILogBusiness<LogDTO> loginBusiness)
        {
            _logBusiness = loginBusiness;  
        }
        
        [HttpPost]
        [Route("addLog")]

        public async Task<LogDTO> SaveAsync([FromBody] LogDTO logDTO)
        {
            try
            {
                return await _logBusiness.SaveAsync(logDTO);
            }
            catch(Exception ex)
            {
                throw new Exception($"Ocorreram erros ao salvar o log.{ex.Message}");
            }
        }    
    }
}
