using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/subitem")]
    public class SubItemController : ControllerBase
    {
        private readonly ISubItemBusiness _subItemBusiness;
        
        public SubItemController(ISubItemBusiness subItemBusiness)
        {
            _subItemBusiness = subItemBusiness;       
        }
        
        [HttpGet]
        public async Task<IActionResult> ListSubItems()
        {
            try
            {
                List<SubItemDTO> subItems = await _subItemBusiness.GetAllAsync();
                return Ok(subItems);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        /// <summary>
        /// Endpoint otimizado para listagem com paginação e filtros
        /// </summary>
        [HttpGet("paged")]
        public async Task<IActionResult> GetPaged([FromQuery] PagedRequestDTO request)
        {
            try
            {
                if (request.PageSize > 100) // Limitar tamanho máximo da página
                {
                    request.PageSize = 100;
                }

                var result = await _subItemBusiness.GetPagedListAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("{id:long}")]
        public async Task<IActionResult> GetSubItem([FromRoute] long id)
        {
            try
            {
                var subItem = await _subItemBusiness.GetByIdAsync(id);
                if (subItem == null)
                    return NotFound($"SubItem com ID {id} não encontrado");
                    
                return Ok(subItem);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpPost]
        [Route("add")]
        public async Task<IActionResult> AddSubItem([FromBody] SubItemDTO subItemDto)
        {
            try
            {
                if (subItemDto == null)
                    return BadRequest("Dados do subitem são obrigatórios");
                
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);
                
                var createdSubItem = await _subItemBusiness.SaveAsync(subItemDto);
                return CreatedAtAction(nameof(GetSubItem), new { id = createdSubItem.Id }, createdSubItem);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpPut]
        [Route("update/{id:int}")]
        public async Task<IActionResult> UpdateSubItem([FromBody] SubItemDTO subItemDto)
        {
            try
            {
                if (subItemDto == null)
                    return BadRequest("Dados do subitem são obrigatórios");
                
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);
                
                var updatedSubItem = await _subItemBusiness.UpdateAsync(subItemDto);
                return Ok(updatedSubItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpDelete]
        [Route("delete/{id:int}")]
        public async Task<IActionResult> DeleteSubItem([FromRoute] int id)
        {
            try
            {
                var deletedSubItem = await _subItemBusiness.DeleteAsync(id);
                return Ok(deletedSubItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("search/{name}")]
        public async Task<IActionResult> SearchSubItems([FromRoute] string name)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                    return BadRequest("Nome para busca é obrigatório");
                
                var subItems = await _subItemBusiness.SearchByNameAsync(name);
                return Ok(subItems);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Lista subitens por status (ativo/inativo)
        /// </summary>
        /// <param name="isActive">Status do subitem</param>
        /// <returns>Lista de subitens filtrados por status</returns>
        [HttpGet]
        [Route("status/{isActive:bool}")]
        public async Task<IActionResult> GetSubItemsByStatus([FromRoute] bool isActive)
        {
            try
            {
                var subItems = await _subItemBusiness.GetByStatusAsync(isActive);
                return Ok(subItems);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
        
        [HttpGet]
        [Route("item/{itemId:long}")]
        public async Task<IActionResult> GetSubItemsByItemId([FromRoute] long itemId)
        {
            try
            {
                var subItems = await _subItemBusiness.GetByItemIdAsync(itemId);
                return Ok(subItems);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }
    }
}
