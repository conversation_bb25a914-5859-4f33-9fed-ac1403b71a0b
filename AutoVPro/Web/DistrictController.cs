using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/district")]
    public class DistrictController : ControllerBase
    {
        private readonly AutoProDbContext _context;
        
        public DistrictController(AutoProDbContext context)
        {
            _context = context;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<DistrictDTO>> ListDistricts()
        {
            var districts = await _context.Districts
                .Where(d => d.Active && !d.Deleted)
                .OrderBy(d => d.District)
                .Select(d => new DistrictDTO
                {
                    Id = d.Id,
                    District = d.District,
                    Code = d.Code,
                    Guid = d.Guid,
                    Active = d.Active,
                    Deleted = d.Deleted,
                    CreatedOn = d.CreatedOn,
                    UpdatedOn = d.UpdatedOn
                })
                .ToListAsync();
                
            return districts;
        }
        
        [HttpGet]
        [Route("{id:long}")]
        public async Task<ActionResult<DistrictDTO>> GetDistrict([FromRoute] long id)
        {
            var district = await _context.Districts
                .Where(d => d.Id == id && d.Active && !d.Deleted)
                .Select(d => new DistrictDTO
                {
                    Id = d.Id,
                    District = d.District,
                    Code = d.Code,
                    Guid = d.Guid,
                    Active = d.Active,
                    Deleted = d.Deleted,
                    CreatedOn = d.CreatedOn,
                    UpdatedOn = d.UpdatedOn
                })
                .FirstOrDefaultAsync();
                
            if (district == null)
                return NotFound($"District with id {id} not found");
                
            return district;
        }
        
        [HttpGet]
        [Route("search/{name}")]
        public async Task<List<DistrictDTO>> SearchDistricts([FromRoute] string name)
        {
            var districts = await _context.Districts
                .Where(d => d.District.Contains(name) && d.Active && !d.Deleted)
                .OrderBy(d => d.District)
                .Select(d => new DistrictDTO
                {
                    Id = d.Id,
                    District = d.District,
                    Code = d.Code,
                    Guid = d.Guid,
                    Active = d.Active,
                    Deleted = d.Deleted,
                    CreatedOn = d.CreatedOn,
                    UpdatedOn = d.UpdatedOn
                })
                .ToListAsync();
                
            return districts;
        }
    }
}