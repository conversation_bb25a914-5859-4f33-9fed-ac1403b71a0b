using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/customer")]
    public class CustomerController : ControllerBase
    {
        private readonly ICustomerBusiness _customerBusiness;
        
        public CustomerController(ICustomerBusiness customerBusiness)
        {
            _customerBusiness = customerBusiness;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<CustomerDTO>> ListCustomers()
        {
            List<CustomerDTO> lst = await _customerBusiness.GetAllAsync();
            return lst;
        }
        
        [HttpPost]
        [Route("add")]
        public async Task<IActionResult> AddCustomer([FromBody] CustomerDTO customer)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var customerDto = await _customerBusiness.SaveAsync(customer);
                return Ok(customerDto);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
        
        [HttpGet]
        [Route("id/{guid}")]
        public async Task<CustomerDTO> GetCustomer([FromRoute] Guid guid)
        {
            CustomerDTO customer = await _customerBusiness.GetByIdAsync(guid);
            return customer;
        }

        [HttpPut]
        [Route("update")]
        public async Task<CustomerDTO> UpdateCustomer(CustomerDTO customer)
        {
            await _customerBusiness.UpdateAsync(customer);
            return customer;
        }

        [HttpDelete]
        [Route("delete/{id:int}")]
        public async Task<CustomerDTO> DeleteCustomer([FromRoute] int id)
        {
            var customer = await _customerBusiness.DeleteAsync(id);
            return customer;
        }
        
        [HttpGet]
        [Route("cpf/{cpf}")]
        public async Task<CustomerDTO> GetByCpf([FromRoute] string cpf)
        {
            var customer = await _customerBusiness.GetByCpfAsync(cpf);
            return customer;
        }
        
        [HttpGet]
        [Route("cnpj/{cnpj}")]
        public async Task<CustomerDTO> GetByCnpj([FromRoute] string cnpj)
        {
            var customer = await _customerBusiness.GetByCnpjAsync(cnpj);
            return customer;
        }
        
        [HttpPost]
        [Route("create-or-update")]
        public async Task<IActionResult> CreateOrUpdate([FromBody] CustomerDTO customer)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var customerDto = await _customerBusiness.CreateOrUpdateAsync(customer);
                return Ok(customerDto);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet]
        [Route("paginated")]
        public async Task<PagedResultDTO<CustomerDTO>> GetPagedCustomers(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? search = null,
            [FromQuery] bool? active = null)
        {
            var result = await _customerBusiness.GetPagedAsync(page, pageSize, search, active);
            return result;
        }

        [HttpGet]
        [Route("search/{term}")]
        public async Task<List<CustomerDTO>> SearchCustomers([FromRoute] string term)
        {
            var customers = await _customerBusiness.SearchAsync(term);
            return customers;
        }

        [HttpGet]
        [Route("active/{status}")]
        public async Task<List<CustomerDTO>> GetByStatus([FromRoute] bool status)
        {
            var customers = await _customerBusiness.GetByStatusAsync(status);
            return customers;
        }

        [HttpGet]
        [Route("count")]
        public async Task<int> GetTotalCount()
        {
            var count = await _customerBusiness.GetTotalCountAsync();
            return count;
        }
    }
}