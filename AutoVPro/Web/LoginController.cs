using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Serilog;

namespace AutoVPro.Web
{
    [Route("api/login")]
    [ApiController]
    public class LoginController : ControllerBase
    {
        private readonly ILoginBusiness _loginBusiness;
        private readonly IConfiguration _configuration;

        
        public LoginController(
            ILoginBusiness loginBusiness,
            IConfiguration configuration
            )
        {
            _loginBusiness = loginBusiness;
            _configuration = configuration;
        }

        [HttpPost]
        [Route("logar")]
        public async Task<IActionResult> Logar(
            [FromBody] LoginDTO loginDTO)
        {
            if(string.IsNullOrWhiteSpace(loginDTO.Email) || string.IsNullOrWhiteSpace(loginDTO.Password)) 
                return BadRequest("Email and password are required");

            var user =  await _loginBusiness.Logar(loginDTO);
            
            if(user == null)
                return Unauthorized("Login or Password is incorrect");

            var claims = new[]
            {
                new Claim(ClaimTypes.Name, loginDTO.Email),
                new Claim("department", "general")
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                issuer: _configuration["Jwt:Issuer"],
                audience: null,
                claims: claims,
                expires: DateTime.Now.AddMinutes(30),
                signingCredentials: creds
            );
            
            Log.Information("Login successful", DateTime.Now, loginDTO.Email);

            return Ok(new
            {
                Token = new JwtSecurityTokenHandler().WriteToken(token),
                loginDTO.Email,
                loginDTO.Guid,
                loginDTO.UserGroupId,
            });
        }
    }
}
