using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [Route("api/user")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserBusiness _userBusiness;

        public UserController(IUserBusiness userBusiness)
        {
            _userBusiness = userBusiness;
        }

        [HttpPost]
        [Route("add")]
        public async Task<IActionResult> Add([FromBody] UserDTO userDto)
        {
            try
            {
                if (userDto == null)
                {
                    return BadRequest("Dados do usuário são obrigatórios");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _userBusiness.SaveAsync(userDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erro interno do servidor");
            }
        }

        [HttpPut]
        [Route("update")]
        public async Task<IActionResult> UpdateAsync([FromBody] UserUpdateDTO userUpdateDto)
        {
            try
            {
                if (userUpdateDto == null)
                {
                    return BadRequest("Dados do usuário são obrigatórios");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _userBusiness.UpdateAsync(userUpdateDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("não encontrado"))
                {
                    return NotFound("Usuário não encontrado");
                }
                return StatusCode(500, "Erro interno do servidor");
            }
        }

        [HttpGet]
        [Route("list")]
        public async Task<IActionResult> ListAsync()
        {
            try
            {
                var result = await _userBusiness.GetAllAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erro interno do servidor");
            }
        }

        /// <summary>
        /// Endpoint otimizado para listagem com paginação e filtros
        /// </summary>
        [HttpGet("paged")]
        public async Task<IActionResult> GetPaged([FromQuery] PagedRequestDTO request)
        {
            try
            {
                if (request.PageSize > 100) // Limitar tamanho máximo da página
                {
                    request.PageSize = 100;
                }

                var result = await _userBusiness.GetPagedListAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Erro interno do servidor");
            }
        }

        [HttpGet]
        [Route("{guid}")]
        public async Task<IActionResult> GetByIdAsync([FromRoute] Guid guid)
        {
            try
            {
                var result = await _userBusiness.GetByIdAsync(guid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("não encontrado") || ex.Message.Contains("inativo"))
                {
                    return NotFound("Usuário não encontrado ou inativo");
                }
                return StatusCode(500, "Erro interno do servidor");
            }
        }

        [HttpDelete]
        [Route("{id:int}")]
        public async Task<IActionResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _userBusiness.DeleteAsync(id);
                return Ok(new { message = "Usuário deletado com sucesso", data = result });
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("não encontrado"))
                {
                    return NotFound("Usuário não encontrado");
                }
                return StatusCode(500, "Erro interno do servidor");
            }
        }
    }
}
