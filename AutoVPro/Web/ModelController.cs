using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/model")]
    public class ModelController : ControllerBase
    {
        private readonly AutoProDbContext _context;
        
        public ModelController(AutoProDbContext context)
        {
            _context = context;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<ModelDTO>> ListModels()
        {
            var models = await _context.Models
                .Include(m => m.Brand)
                .OrderBy(m => m.Brand.Brand)
                .ThenBy(m => m.Model)
                .Select(m => new ModelDTO
                {
                    Id = m.Id,
                    Model = m.Model,
                    BrandId = m.BrandId,
                    Brand = new BrandDTO
                    {
                        Id = m.Brand.Id,
                        Brand = m.Brand.Brand
                    }
                })
                .ToListAsync();
                
            return models;
        }
        
        [HttpGet]
        [Route("{id:long}")]
        public async Task<ActionResult<ModelDTO>> GetModel([FromRoute] long id)
        {
            var model = await _context.Models
                .Include(m => m.Brand)
                .Where(m => m.Id == id)
                .Select(m => new ModelDTO
                {
                    Id = m.Id,
                    Model = m.Model,
                    BrandId = m.BrandId,
                    Brand = new BrandDTO
                    {
                        Id = m.Brand.Id,
                        Brand = m.Brand.Brand
                    }
                })
                .FirstOrDefaultAsync();
                
            if (model == null)
                return NotFound($"Model with id {id} not found");
                
            return model;
        }
        
        [HttpGet]
        [Route("search/{name}")]
        public async Task<List<ModelDTO>> SearchModels([FromRoute] string name)
        {
            var models = await _context.Models
                .Include(m => m.Brand)
                .Where(m => m.Model.Contains(name))
                .OrderBy(m => m.Brand.Brand)
                .ThenBy(m => m.Model)
                .Select(m => new ModelDTO
                {
                    Id = m.Id,
                    Model = m.Model,
                    BrandId = m.BrandId,
                    Brand = new BrandDTO
                    {
                        Id = m.Brand.Id,
                        Brand = m.Brand.Brand
                    }
                })
                .ToListAsync();
                
            return models;
        }
        
        [HttpGet]
        [Route("brand/{brandId:long}")]
        public async Task<List<ModelDTO>> GetModelsByBrand([FromRoute] long brandId)
        {
            var models = await _context.Models
                .Include(m => m.Brand)
                .Where(m => m.BrandId == brandId)
                .OrderBy(m => m.Model)
                .Select(m => new ModelDTO
                {
                    Id = m.Id,
                    Model = m.Model,
                    BrandId = m.BrandId,
                    Brand = new BrandDTO
                    {
                        Id = m.Brand.Id,
                        Brand = m.Brand.Brand
                    }
                })
                .ToListAsync();
                
            return models;
        }
    }
}