using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO
{
    /// <summary>
    /// DTO otimizado para listagem de cautelaristas sem campos pesados de Base64
    /// </summary>
    public class CautelaristListDTO
    {
        public long Id { get; set; }
        
        [Required]
        [StringLength(255)]
        public string Name { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Cellphone { get; set; }
        
        [StringLength(255)]
        public string? Email { get; set; }
        
        [Required]
        [StringLength(200)]
        public string TypeOfContact { get; set; }
        
        public bool IsAprove { get; set; }
        
        public bool Origem { get; set; }
        
        [StringLength(1000)]
        public string? Observacoes { get; set; }
    }
    
    /// <summary>
    /// DTO para resposta paginada
    /// </summary>
    public class PagedResultDTO<T>
    {
        public List<T> Items { get; set; }
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);
        
        public PagedResultDTO()
        {
            Items = new List<T>();
        }
    }
    
    /// <summary>
    /// DTO para requisição de paginação
    /// </summary>
    public class PagedRequestDTO
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }
}