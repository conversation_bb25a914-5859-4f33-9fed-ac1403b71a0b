using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO;

public record CustomerDTO()
{
    public long Id { get; set; }
    
    public Guid Guid { get; set; }
    
    [Required(ErrorMessage = "The Name field is required.")]
    [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters.")]
    public string Name { get; set; }
    
    [Required(ErrorMessage = "The Type field is required.")]
    [StringLength(255, ErrorMessage = "Type cannot exceed 255 characters.")]
    public string Type { get; set; } // PF ou PJ
    
    [Required(ErrorMessage = "The FiscalNumber field is required.")]
    [StringLength(255, ErrorMessage = "FiscalNumber cannot exceed 255 characters.")]
    public string FiscalNumber { get; set; } // CPF ou CNPJ
    
    public DateTime? BirthDate { get; set; }
    
    [Required(ErrorMessage = "The Address field is required.")]
    [StringLength(255, ErrorMessage = "Address cannot exceed 255 characters.")]
    public string Address { get; set; }
    
    [Required(ErrorMessage = "The Number field is required.")]
    [StringLength(255, ErrorMessage = "Number cannot exceed 255 characters.")]
    public string Number { get; set; }
    
    [StringLength(255, ErrorMessage = "AddressComplement cannot exceed 255 characters.")]
    public string AddressComplement { get; set; } = "";
    
    [Required(ErrorMessage = "The DistrictId field is required.")]
    public long DistrictId { get; set; }
    
    [Required(ErrorMessage = "The CityId field is required.")]
    public long CityId { get; set; }
    
    [Required(ErrorMessage = "The StateId field is required.")]
    public long StateId { get; set; }
    
    public bool Active { get; set; } = true;
    
    public bool Deleted { get; set; } = false;
    
    public DateTime CreatedOn { get; set; }
    
    public DateTime UpdatedOn { get; set; }
    
    // Navigation properties DTOs
    public DistrictDTO? District { get; set; }
    public CityDTO? City { get; set; }
    public StateDTO? State { get; set; }
}