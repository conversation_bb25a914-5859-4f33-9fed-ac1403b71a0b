using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO;

public class SubItemDTO
{
    public long Id { get; set; }

    [Required(ErrorMessage = "O ItemModelId é obrigatório")]
    public long ItemModelId { get; set; }

    [Required(ErrorMessage = "O nome é obrigatório")]
    [StringLength(100, ErrorMessage = "O nome deve ter no máximo 100 caracteres")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "A descrição é obrigatória")]
    [StringLength(255, ErrorMessage = "A descrição deve ter no máximo 255 caracteres")]
    public string Description { get; set; }

    public bool IsActive { get; set; } = true;

    public bool IsDeleted { get; set; } = false;

    public ItemDTO? ItemModel { get; set; }
}
