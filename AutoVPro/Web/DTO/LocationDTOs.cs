namespace AutoVPro.Web.DTO;

public record ModelDTO()
{
    public long Id { get; set; }
    public string Model { get; set; }
    public long BrandId { get; set; }
    public BrandDTO? Brand { get; set; }
}

public record DistrictDTO()
{
    public long Id { get; set; }
    public string District { get; set; }
    public string Code { get; set; }
    public Guid Guid { get; set; }
    public bool Active { get; set; }
    public bool Deleted { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime UpdatedOn { get; set; }
}

public record CityDTO()
{
    public long Id { get; set; }
    public string City { get; set; }
    public Guid Guid { get; set; }
    public bool Active { get; set; }
    public bool Deleted { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime UpdatedOn { get; set; }
}

public record StateDTO()
{
    public long Id { get; set; }
    public string State { get; set; }
    public Guid Guid { get; set; }
    public bool Active { get; set; }
    public bool Deleted { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime UpdatedOn { get; set; }
}

