using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO;

public class UserUpdateDTO
{
    [Required]
    public long Id { get; set; }
    
    [Required]
    public Guid Guid { get; set; }
    
    [Required]
    [EmailAddress]
    public string Email { get; set; }
    
    // Password é opcional em updates - se não fornecido, mantém o atual
    public string? Password { get; set; }
    
    [Required]
    public int UserGroupId { get; set; }

    public bool Active { get; set; }
    
    public bool Deleted { get; set; }
}