using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO;

/// <summary>
/// DTO específico para solicitações de serviço vindas do site público
/// Centraliza a validação e estrutura de dados no backend
/// </summary>
public record ServiceRequestDTO()
{
    // Informações do Cliente
    [Required(ErrorMessage = "Tipo de cliente é obrigatório")]
    public string ClientType { get; set; } // "fisica" | "juridica"
    
    [Required(ErrorMessage = "Nome completo é obrigatório")]
    [MinLength(3, ErrorMessage = "Nome deve ter pelo menos 3 caracteres")]
    public string NomeCompleto { get; set; }
    
    [Required(ErrorMessage = "CPF é obrigatório")]
    public string Cpf { get; set; }
    
    [Required(ErrorMessage = "Email é obrigatório")]
    [EmailAddress(ErrorMessage = "Email inválido")]
    public string Email { get; set; }
    
    [Required(ErrorMessage = "Celular é obrigatório")]
    public string Celular { get; set; }
    
    [Required(ErrorMessage = "CEP é obrigatório")]
    public string Cep { get; set; }
    
    [Required(ErrorMessage = "Endereço é obrigatório")]
    public string Endereco { get; set; }
    
    [Required(ErrorMessage = "Número é obrigatório")]
    public string Numero { get; set; }
    
    public string? Complemento { get; set; }
    
    [Required(ErrorMessage = "Bairro é obrigatório")]
    public string Bairro { get; set; }
    
    [Required(ErrorMessage = "Cidade é obrigatória")]
    public string Cidade { get; set; }
    
    [Required(ErrorMessage = "Estado é obrigatório")]
    public string Estado { get; set; }
    
    public bool CriarUsuario { get; set; } = true;

    // Campos específicos para Pessoa Jurídica
    public string? NomeFantasia { get; set; }
    public string? RazaoSocial { get; set; }
    public string? Cnpj { get; set; }
    public string? InscricaoEstadual { get; set; }

    // Informações do Veículo
    [Required(ErrorMessage = "Tipo de endereço do veículo é obrigatório")]
    public string EnderecoVeiculo { get; set; } // "proprio" | "outro"
    
    [Required(ErrorMessage = "Placa é obrigatória")]
    public string Placa { get; set; }
    
    [Required(ErrorMessage = "Marca é obrigatória")]
    public string Marca { get; set; }
    
    [Required(ErrorMessage = "Modelo é obrigatório")]
    public string Modelo { get; set; }
    
    [Required(ErrorMessage = "Cor é obrigatória")]
    public string Cor { get; set; }
    
    [Required(ErrorMessage = "Nome do proprietário é obrigatório")]
    [MinLength(3, ErrorMessage = "Nome do proprietário deve ter pelo menos 3 caracteres")]
    public string NomeProprietario { get; set; }
    
    [Required(ErrorMessage = "Celular do proprietário é obrigatório")]
    public string CelularProprietario { get; set; }
    
    [Required(ErrorMessage = "Tipo de serviço é obrigatório")]
    public string TipoServico { get; set; } // "cautelar" | "vistoria"

    // Campos específicos para endereço do veículo (quando EnderecoVeiculo === "outro")
    public string? CepVeiculo { get; set; }
    public string? EnderecoVeiculoCompleto { get; set; }
    public string? NumeroVeiculo { get; set; }
    public string? ComplementoVeiculo { get; set; }
    public string? BairroVeiculo { get; set; }
    public string? CidadeVeiculo { get; set; }
    public string? EstadoVeiculo { get; set; }

    // Pagamento
    [Required(ErrorMessage = "Método de pagamento é obrigatório")]
    public string MetodoPagamento { get; set; }
    
    public string? Observacoes { get; set; }
}

/// <summary>
/// DTO para resposta da criação de service request
/// </summary>  
public record ServiceRequestResponseDTO()
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public string? RequestId { get; set; }
    public List<string>? Errors { get; set; }
}