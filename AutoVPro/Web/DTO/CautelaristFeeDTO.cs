using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO;

public class CautelaristFeeDTO
{
    public long Id { get; set; }

    [Required(ErrorMessage = "A taxa (Fee) é obrigatória")]
    [Range(0.01, double.MaxValue, ErrorMessage = "A taxa deve ser maior que zero")]
    public decimal Fee { get; set; }

    public bool IsActive { get; set; } = true;

    public bool IsDeleted { get; set; } = false;
}
