namespace AutoVPro.Web.DTO;

public record AttendanceDTO()
{
    public long Id { get; set; }
    
    public Guid Guid { get; set; }
    
    public DateTime DateRequest { get; set; }
    
    public DateTime? DateAttendance { get; set; }
    
    public DateTime? DateFinish { get; set; }
    
    public long CustomerId { get; set; }
    
    public long? ProfessionalId { get; set; }
    
    public string ReferenceCar { get; set; }
    
    public long BrandId { get; set; }
    
    public long ModelId { get; set; }
    
    public string PostalCode { get; set; }
    
    public string Address { get; set; }
    
    public string Number { get; set; }
    
    public string AddressComplement { get; set; }
    
    public long DistrictId { get; set; }
    
    public long CityId { get; set; }
    
    public long StateId { get; set; }
    
    public int Status { get; set; }
    
    // Campos adicionais do Service Request
    public string? VehicleOwnerName { get; set; }
    
    public string? VehicleOwnerPhone { get; set; }
    
    public bool HasCautelarService { get; set; }
    
    public bool HasVistoriaService { get; set; }
    
    public string? CustomerNotes { get; set; }
    
    // Campos para endereço alternativo do veículo
    public bool VehicleAtDifferentAddress { get; set; }
    
    public string? VehiclePostalCode { get; set; }
    
    public string? VehicleAddress { get; set; }
    
    public string? VehicleNumber { get; set; }
    
    public string? VehicleAddressComplement { get; set; }
    
    public long? VehicleDistrictId { get; set; }
    
    public long? VehicleCityId { get; set; }
    
    public long? VehicleStateId { get; set; }
    
    // Navigation properties DTOs
    public CustomerDTO? Customer { get; set; }
    public CautelaristDTO? Professional { get; set; }
    public BrandDTO? Brand { get; set; }
    public ModelDTO? Model { get; set; }
    public DistrictDTO? District { get; set; }
    public CityDTO? City { get; set; }
    public StateDTO? State { get; set; }
    
    // Navigation properties para endereço do veículo
    public DistrictDTO? VehicleDistrict { get; set; }
    public CityDTO? VehicleCity { get; set; }
    public StateDTO? VehicleState { get; set; }
    
    // TODO: Future payment integration
    // public List<AttendancePaymentDTO> Payments { get; set; }
}

// Enum para Status
public enum AttendanceStatus
{
    Requested = 1,      // Solicitado
    InProgress = 2,     // Em andamento
    Completed = 3,      // Concluído
    Cancelled = 4,      // Cancelado
    // TODO: Future payment statuses
    // AwaitingPayment = 5,
    // Paid = 6
}

/// <summary>
/// DTO otimizado para listagem de atendimentos com dados essenciais
/// </summary>
public class AttendanceListDTO
{
    public long Id { get; set; }
    public Guid Guid { get; set; }
    public DateTime DateRequest { get; set; }
    public DateTime? DateAttendance { get; set; }
    public DateTime? DateFinish { get; set; }
    public int Status { get; set; }
    
    // Dados básicos do cliente
    public long CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public string? CustomerType { get; set; }
    public string? CustomerFiscalNumber { get; set; }
    
    // Dados básicos do profissional
    public long? ProfessionalId { get; set; }
    public string? ProfessionalName { get; set; }
    
    // Informações do veículo
    public string ReferenceCar { get; set; }
    public long BrandId { get; set; }
    public string? BrandName { get; set; }
    public long ModelId { get; set; }
    public string? ModelName { get; set; }
    
    // Serviços
    public bool HasCautelarService { get; set; }
    public bool HasVistoriaService { get; set; }
    
    // Endereço principal
    public string Address { get; set; }
    public string Number { get; set; }
    public string? AddressComplement { get; set; }
    public string PostalCode { get; set; }
    public string? DistrictName { get; set; }
    public string? CityName { get; set; }
    public string? StateName { get; set; }
    
    // Notas do cliente
    public string? CustomerNotes { get; set; }
}

/// <summary>
/// DTO para requisição de paginação específica de Attendance
/// </summary>
public class AttendancePagedRequestDTO
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
    public int? StatusFilter { get; set; }
    public string? DateRangeStart { get; set; }
    public string? DateRangeEnd { get; set; }
}