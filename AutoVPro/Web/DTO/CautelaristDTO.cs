using System.ComponentModel.DataAnnotations;
using AutoVPro.Attributes;

namespace AutoVPro.Web.DTO;

public class CautelaristDTO
{
    public long Id { get; set; }

    [Required(ErrorMessage = "Nome é obrigatório")]
    [StringLength(200, ErrorMessage = "Nome deve ter no máximo 200 caracteres")]
    public string Name { get; set; }

    [Required(ErrorMessage = "Celular é obrigatório")]
    [StringLength(20, ErrorMessage = "Celular deve ter no máximo 20 caracteres")]
    public string Cellphone { get; set; }

    [Required(ErrorMessage = "Tipo de contato é obrigatório")]
    [StringLength(50, ErrorMessage = "Tipo de contato deve ter no máximo 50 caracteres")]
    public string TypeOfContact { get; set; }

    [Required(ErrorMessage = "Documento da CNH é obrigatório")]
    [Base64Validation(new[] { "application/pdf" }, 5 * 1024 * 1024)] // 5MB max
    public string CnhDocumentBase64 { get; set; }

    [Required(ErrorMessage = "Comprovante de residência é obrigatório")]
    [Base64Validation(new[] { "application/pdf" }, 5 * 1024 * 1024)] // 5MB max
    public string ProofOfResidenceBase64 { get; set; }

    [Required(ErrorMessage = "Documento de qualificações é obrigatório")]
    [Base64Validation(new[] { "application/pdf" }, 5 * 1024 * 1024)] // 5MB max
    public string QualificationsDocumentBase64 { get; set; }

    [Required(ErrorMessage = "Foto da CNH é obrigatória")]
    [Base64Validation(new[] { "image/jpeg", "image/png", "image/jpg" }, 2 * 1024 * 1024)] // 2MB max for images
    public string CnhImageBase64 { get; set; }

    [Required(ErrorMessage = "Foto das qualificações é obrigatória")]
    [Base64Validation(new[] { "image/jpeg", "image/png", "image/jpg" }, 2 * 1024 * 1024)] // 2MB max for images
    public string QualificationsImageBase64 { get; set; }

    public bool IsAprove { get; set; } = false;

    [EmailAddress(ErrorMessage = "Email deve ter um formato válido")]
    [StringLength(255, ErrorMessage = "Email deve ter no máximo 255 caracteres")]
    public string? Email { get; set; }

    [StringLength(1000, ErrorMessage = "Observações devem ter no máximo 1000 caracteres")]
    public string? Observacoes { get; set; }

    public bool Origem { get; set; } = false;
}