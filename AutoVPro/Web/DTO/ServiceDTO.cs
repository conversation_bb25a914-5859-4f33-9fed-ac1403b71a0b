using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO;

public record ServiceDTO()
{
    public long Id { get; set; }

    public Guid Guid { get; set; }

    public string Name { get; set; }

    public string Icon { get; set; }

    public string Description { get; set; }

    public decimal ValueAmount { get; set; }

    public bool Active { get; set; }

    public bool Deleted { get; set; }
}

/// <summary>
/// DTO otimizado para listagem de serviços sem campos pesados
/// </summary>
public class ServiceListDTO
{
    public long Id { get; set; }
    
    public string Guid { get; set; }
    
    [Required]
    [StringLength(255)]
    public string Name { get; set; }
    
    [StringLength(255)]
    public string Icon { get; set; }
    
    [StringLength(255)]
    public string Description { get; set; }
    
    public decimal ValueAmount { get; set; }
    
    public bool Active { get; set; }
    
    public DateTime? CreatedOn { get; set; }
    
    public DateTime? UpdatedOn { get; set; }
}
