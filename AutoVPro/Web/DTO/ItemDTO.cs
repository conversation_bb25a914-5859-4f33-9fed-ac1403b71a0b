using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO;

public class ItemDTO
{
    public long Id { get; set; }

    [Required(ErrorMessage = "O nome é obrigatório")]
    [StringLength(100, ErrorMessage = "O nome deve ter no máximo 100 caracteres")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "A descrição é obrigatória")]
    [StringLength(255, ErrorMessage = "A descrição deve ter no máximo 255 caracteres")]
    public string Description { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public bool IsDeleted { get; set; } = false;

    public List<SubItemDTO>? SubItems { get; set; }
}
