using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO
{
    /// <summary>
    /// DTO otimizado para listagem de usuários sem campos sensíveis
    /// </summary>
    public class UserListDTO
    {
        public long Id { get; set; }
        
        public Guid Guid { get; set; }
        
        [Required]
        [StringLength(255)]
        public string Email { get; set; }
        
        public int UserGroupId { get; set; }
        
        public string? UserGroupName { get; set; }
        
        public bool Active { get; set; }
        
        public DateTime? CreatedOn { get; set; }
        
        public DateTime? UpdatedOn { get; set; }
    }
}