using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Web.DTO
{
    /// <summary>
    /// DTO otimizado para listagem de grupos de usuário sem campos pesados
    /// </summary>
    public class UserGroupListDTO
    {
        public int Id { get; set; }
        
        public string Guid { get; set; }
        
        [Required]
        [StringLength(255)]
        public string NomeGrupoUser { get; set; }
        
        public bool Active { get; set; }
        
        public DateTime CreatedOn { get; set; }
        
        public DateTime? UpdatedOn { get; set; }
    }
}