using AutoVPro.Database.AutovproContext;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/sql")]
    public class SqlController : ControllerBase
    {
        private readonly AutoProDbContext _context;
        
        public SqlController(AutoProDbContext context)
        {
            _context = context;       
        }
        
        [HttpPost]
        [Route("execute-manual-migration")]
        public async Task<IActionResult> ExecuteManualMigration()
        {
            try
            {
                var sql = @"
                -- Verificar e adicionar colunas se não existirem
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleOwnerName')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleOwnerName nvarchar(255) NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleOwnerPhone')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleOwnerPhone nvarchar(20) NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'HasCautelarService')
                BEGIN
                    ALTER TABLE Attendance ADD HasCautelarService bit NOT NULL DEFAULT 0;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'HasVistoriaService')
                BEGIN
                    ALTER TABLE Attendance ADD HasVistoriaService bit NOT NULL DEFAULT 0;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'CustomerNotes')
                BEGIN
                    ALTER TABLE Attendance ADD CustomerNotes nvarchar(500) NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleAtDifferentAddress')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleAtDifferentAddress bit NOT NULL DEFAULT 0;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehiclePostalCode')
                BEGIN
                    ALTER TABLE Attendance ADD VehiclePostalCode nvarchar(10) NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleAddress')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleAddress nvarchar(255) NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleNumber')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleNumber nvarchar(10) NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleAddressComplement')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleAddressComplement nvarchar(255) NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleDistrictId')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleDistrictId bigint NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleCityId')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleCityId bigint NULL;
                END

                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Attendance') AND name = 'VehicleStateId')
                BEGIN
                    ALTER TABLE Attendance ADD VehicleStateId bigint NULL;
                END
                ";

                await _context.Database.ExecuteSqlRawAsync(sql);
                
                return Ok(new { message = "Migração manual executada com sucesso!", success = true });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = $"Erro ao executar migração: {ex.Message}", success = false });
            }
        }
    }
}