using AutoVPro.Database.AutovproContext;
using AutoVPro.Database.Model;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AutoVPro.Web
{
    [ApiController]
    [Route("api/brand")]
    public class BrandController : ControllerBase
    {
        private readonly AutoProDbContext _context;
        
        public BrandController(AutoProDbContext context)
        {
            _context = context;       
        }
        
        [HttpGet]
        [Route("list")]
        public async Task<List<BrandDTO>> ListBrands()
        {
            var brands = await _context.Brands
                .OrderBy(b => b.Brand)
                .Select(b => new BrandDTO
                {
                    Id = b.Id,
                    Brand = b.Brand
                })
                .ToListAsync();
                
            return brands;
        }
        
        [HttpGet]
        [Route("{id:long}")]
        public async Task<ActionResult<BrandDTO>> GetBrand([FromRoute] long id)
        {
            var brand = await _context.Brands
                .Where(b => b.Id == id)
                .Select(b => new BrandDTO
                {
                    Id = b.Id,
                    Brand = b.Brand
                })
                .FirstOrDefaultAsync();
                
            if (brand == null)
                return NotFound($"Brand with id {id} not found");
                
            return brand;
        }
        
        [HttpGet]
        [Route("search/{name}")]
        public async Task<List<BrandDTO>> SearchBrands([FromRoute] string name)
        {
            var brands = await _context.Brands
                .Where(b => b.Brand.Contains(name))
                .OrderBy(b => b.Brand)
                .Select(b => new BrandDTO
                {
                    Id = b.Id,
                    Brand = b.Brand
                })
                .ToListAsync();
                
            return brands;
        }
        
        [HttpGet]
        [Route("{brandId:long}/models")]
        public async Task<List<ModelDTO>> GetModelsByBrand([FromRoute] long brandId)
        {
            var models = await _context.Models
                .Where(m => m.BrandId == brandId)
                .Include(m => m.Brand)
                .OrderBy(m => m.Model)
                .Select(m => new ModelDTO
                {
                    Id = m.Id,
                    Model = m.Model,
                    BrandId = m.BrandId,
                    Brand = new BrandDTO
                    {
                        Id = m.Brand.Id,
                        Brand = m.Brand.Brand
                    }
                })
                .ToListAsync();
                
            return models;
        }
    }
}