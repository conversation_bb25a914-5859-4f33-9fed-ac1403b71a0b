using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web;

[ApiController]
[Route("api/cautelaristfee")]
public class CautelaristFeeController : ControllerBase
{
    private readonly ICautelaristFeeBusiness _cautelaristFeeBusiness;

    public CautelaristFeeController(ICautelaristFeeBusiness cautelaristFeeBusiness)
    {
        _cautelaristFeeBusiness = cautelaristFeeBusiness;
    }

    [HttpPost("add")]
    public async Task<IActionResult> Create([FromBody] CautelaristFeeDTO cautelaristFeeDTO)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var result = await _cautelaristFeeBusiness.SaveAsync(cautelaristFeeDTO);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        try
        {
            var result = await _cautelaristFeeBusiness.GetAllAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(long id)
    {
        try
        {
            var result = await _cautelaristFeeBusiness.GetByIdAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(long id, [FromBody] CautelaristFeeDTO cautelaristFeeDTO)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            cautelaristFeeDTO.Id = id;
            var result = await _cautelaristFeeBusiness.UpdateAsync(cautelaristFeeDTO);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(long id)
    {
        try
        {
            var result = await _cautelaristFeeBusiness.DeleteAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

    [HttpGet("active")]
    public async Task<IActionResult> GetActiveFees()
    {
        try
        {
            var result = await _cautelaristFeeBusiness.GetActiveFeesAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
        }
    }

}
