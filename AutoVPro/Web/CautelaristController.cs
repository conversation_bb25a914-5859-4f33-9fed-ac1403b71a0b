using AutoVPro.Business;
using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.web
{

    [Route("api/cautelarist")]
    [ApiController]
    public class CautelaristController : ControllerBase
    {
        private readonly IBusiness<CautelaristDTO> _cautelaristBusiness;

        public CautelaristController(IBusiness<CautelaristDTO> cautelaristBusiness)
        {
            _cautelaristBusiness = cautelaristBusiness;
        }

        [HttpPost]
        [Route("add")]
        public async Task<IActionResult> Add([FromBody] CautelaristDTO cautelaristDTO)
        {
            try
            {
                if (cautelaristDTO == null)
                {
                    return BadRequest("Dados do cautelarista são obrigatórios");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _cautelaristBusiness.SaveAsync(cautelaristDTO);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var result = await _cautelaristBusiness.GetAllAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        /// <summary>
        /// Endpoint otimizado para listagem com paginação
        /// </summary>
        [HttpGet("paged")]
        public async Task<IActionResult> GetPaged([FromQuery] PagedRequestDTO request)
        {
            try
            {
                if (request.PageSize > 100) // Limitar tamanho máximo da página
                {
                    request.PageSize = 100;
                }

                var business = (CautelaristBusiness)_cautelaristBusiness;
                var result = await business.GetPagedListAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(long id)
        {
            try
            {
                var business = (CautelaristBusiness)_cautelaristBusiness;
                var result = await business.GetByIdAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("não encontrado"))
                {
                    return NotFound($"Cautelarista não encontrado: {ex.Message}");
                }
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(long id, [FromBody] CautelaristDTO cautelaristDTO)
        {
            try
            {
                if (cautelaristDTO == null)
                {
                    return BadRequest("Dados do cautelarista são obrigatórios");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Garantir que o Id do DTO seja o mesmo da URL
                cautelaristDTO.Id = id;

                var result = await _cautelaristBusiness.UpdateAsync(cautelaristDTO);
                return Ok(result);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("não encontrado"))
                {
                    return NotFound($"Cautelarista não encontrado: {ex.Message}");
                }
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var result = await _cautelaristBusiness.DeleteAsync(id);
                return Ok(new { message = "Cautelarista deletado com sucesso", data = result });
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("não encontrado"))
                {
                    return NotFound($"Cautelarista não encontrado: {ex.Message}");
                }
                return StatusCode(500, $"Erro interno do servidor: {ex.Message}");
            }
        }

        /// <summary>
        /// Busca apenas os documentos Base64 de um cautelarista específico para uso no modal de edição
        /// </summary>
        [HttpGet("{id}/documents")]
        public async Task<IActionResult> GetDocuments(long id)
        {
            try
            {
                var business = (CautelaristBusiness)_cautelaristBusiness;
                var documents = await business.GetDocumentsAsync(id);
                
                if (documents == null)
                {
                    return NotFound("Cautelarista não encontrado");
                }

                return Ok(documents);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao buscar documentos: {ex.Message}");
            }
        }

        /// <summary>
        /// Atualiza o status de aprovação de um cautelarista
        /// </summary>
        /// <param name="id">ID do cautelarista</param>
        /// <param name="approvalStatus">Novo status de aprovação</param>
        /// <returns>Cautelarista com status atualizado</returns>
        [HttpPatch("{id}/approval")]
        public async Task<IActionResult> UpdateApprovalStatus(long id, [FromBody] ApprovalStatusDTO approvalStatus)
        {
            try
            {
                if (approvalStatus == null)
                {
                    return BadRequest("Status de aprovação é obrigatório");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var business = (CautelaristBusiness)_cautelaristBusiness;
                var updatedCautelarist = await business.UpdateApprovalStatusAsync(id, approvalStatus.IsAprove);

                return Ok(new
                {
                    message = $"Status de aprovação atualizado para: {(approvalStatus.IsAprove ? "Aprovado" : "Não Aprovado")}",
                    data = updatedCautelarist
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao atualizar status de aprovação: {ex.Message}");
            }
        }

        [HttpGet("{id}/fees")]
        public async Task<IActionResult> GetCautelaristFees(long id)
        {
            try
            {
                // Retornar informação sobre o relacionamento sem buscar o cautelarista
                return Ok($"Cautelarista ID {id}. Use /api/cautelaristfee/by-cautelarist/{id} para ver as taxas.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao buscar taxas do cautelarista: {ex.Message}");
            }
        }

}
}