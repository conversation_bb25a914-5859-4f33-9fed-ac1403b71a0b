using AutoVPro.Business.Interfaces;
using AutoVPro.Web.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AutoVPro.Web
{
    [Route("api/screen")]
    [ApiController]
    public class ScreenController : ControllerBase
    {
        private readonly IScreenBusiness<ScreenDTO> _screenBusiness;

        public ScreenController(
            IScreenBusiness<ScreenDTO> screenBusiness
        )
        {
            _screenBusiness = screenBusiness;
        }

        [HttpPost]
        public async Task<ScreenDTO> Save([FromBody] ScreenDTO screenDTO)
        {
            var screen = await _screenBusiness.SaveAsync(screenDTO);
            return screen;
        }

        [HttpGet]
        public async Task<List<ScreenDTO>> GetAllAsync()
        {
            var screen = await _screenBusiness.GetAllAsync();
            return screen;
        }
    }
}

