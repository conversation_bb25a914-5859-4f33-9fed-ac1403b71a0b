using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Attributes;

public class ValidRadiusAttribute : ValidationAttribute
{
    private readonly int[] _validValues = { 3, 5, 7, 10, 20 };

    public override bool IsValid(object? value)
    {
        if (value == null)
            return false;

        if (value is int intValue)
        {
            return _validValues.Contains(intValue);
        }

        return false;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"O campo {name} deve ser um dos seguintes valores: {string.Join(", ", _validValues)}";
    }
}
