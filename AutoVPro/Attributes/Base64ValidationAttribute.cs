using System.ComponentModel.DataAnnotations;

namespace AutoVPro.Attributes;

public class Base64ValidationAttribute : ValidationAttribute
{
    private readonly string[] _allowedMimeTypes;
    private readonly long _maxSizeInBytes;

    public Base64ValidationAttribute(string[]? allowedMimeTypes = null, long maxSizeInBytes = 10 * 1024 * 1024) // 10MB default
    {
        _allowedMimeTypes = allowedMimeTypes ?? new[] { "application/pdf" };
        _maxSizeInBytes = maxSizeInBytes;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
        {
            return new ValidationResult("O arquivo é obrigatório.");
        }

        var base64String = value.ToString();

        try
        {
            // Remove data URL prefix if present (data:application/pdf;base64,)
            if (base64String.Contains(","))
            {
                var parts = base64String.Split(',');
                if (parts.Length == 2)
                {
                    var mimeTypePart = parts[0];
                    base64String = parts[1];

                    // Validate MIME type if specified
                    if (_allowedMimeTypes != null && _allowedMimeTypes.Length > 0)
                    {
                        var isValidMimeType = false;
                        foreach (var allowedType in _allowedMimeTypes)
                        {
                            if (mimeTypePart.Contains(allowedType))
                            {
                                isValidMimeType = true;
                                break;
                            }
                        }

                        if (!isValidMimeType)
                        {
                            return new ValidationResult($"Tipo de arquivo não permitido. Tipos aceitos: {string.Join(", ", _allowedMimeTypes)}");
                        }
                    }
                }
            }

            // Validate base64 format
            var bytes = Convert.FromBase64String(base64String);

            // Validate file size
            if (bytes.Length > _maxSizeInBytes)
            {
                var maxSizeMB = _maxSizeInBytes / (1024 * 1024);
                return new ValidationResult($"O arquivo é muito grande. Tamanho máximo permitido: {maxSizeMB}MB");
            }

            // Validate PDF signature if it's a PDF
            if (_allowedMimeTypes.Contains("application/pdf"))
            {
                if (bytes.Length >= 4)
                {
                    var pdfSignature = System.Text.Encoding.ASCII.GetString(bytes, 0, 4);
                    if (pdfSignature != "%PDF")
                    {
                        return new ValidationResult("O arquivo não é um PDF válido.");
                    }
                }
            }

            return ValidationResult.Success;
        }
        catch (FormatException)
        {
            return new ValidationResult("Formato de arquivo inválido. O arquivo deve estar em formato base64.");
        }
        catch (Exception ex)
        {
            return new ValidationResult($"Erro ao validar arquivo: {ex.Message}");
        }
    }
}
