<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>


    <ItemGroup>
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
        <PackageReference Include="Serilog" Version="4.3.0" />
        <PackageReference Include="Serilog.Formatting.Compact" Version="3.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    </ItemGroup>



</Project>
