using System.Text;

namespace AutoVPro.Helpers;

public static class FileHelper
{
    public static async Task<string> ConvertToBase64Async(IFormFile file)
    {
        if (file == null || file.Length == 0)
            throw new ArgumentException("Arquivo não pode ser nulo ou vazio");

        using var memoryStream = new MemoryStream();
        await file.CopyToAsync(memoryStream);
        var fileBytes = memoryStream.ToArray();
        return Convert.ToBase64String(fileBytes);
    }

    public static async Task<string> ConvertToBase64WithDataUrlAsync(IFormFile file)
    {
        if (file == null || file.Length == 0)
            throw new ArgumentException("Arquivo não pode ser nulo ou vazio");

        var base64String = await ConvertToBase64Async(file);
        return $"data:{file.ContentType};base64,{base64String}";
    }

    public static byte[] ConvertFromBase64(string base64String)
    {
        if (string.IsNullOrWhiteSpace(base64String))
            throw new ArgumentException("String base64 não pode ser nula ou vazia");

        if (base64String.Contains(","))
        {
            var parts = base64String.Split(',');
            if (parts.Length == 2)
            {
                base64String = parts[1];
            }
        }

        return Convert.FromBase64String(base64String);
    }

    public static bool IsValidBase64(string base64String)
    {
        if (string.IsNullOrWhiteSpace(base64String))
            return false;

        try
        {
            
            if (base64String.Contains(","))
            {
                var parts = base64String.Split(',');
                if (parts.Length == 2)
                {
                    base64String = parts[1];
                }
            }

            Convert.FromBase64String(base64String);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public static string? ExtractMimeType(string base64String)
    {
        if (string.IsNullOrWhiteSpace(base64String) || !base64String.StartsWith("data:"))
            return null;

        var endIndex = base64String.IndexOf(";");
        if (endIndex == -1)
            return null;

        return base64String.Substring(5, endIndex - 5);
    }

    public static bool IsValidPdf(byte[] fileBytes)
    {
        if (fileBytes == null || fileBytes.Length < 4)
            return false;

        var pdfSignature = Encoding.ASCII.GetString(fileBytes, 0, 4);
        return pdfSignature == "%PDF";
    }

    public static bool IsValidImage(byte[] fileBytes)
    {
        if (fileBytes == null || fileBytes.Length < 4)
            return false;

        if (fileBytes.Length >= 2 && fileBytes[0] == 0xFF && fileBytes[1] == 0xD8)
            return true;

        if (fileBytes.Length >= 8 && 
            fileBytes[0] == 0x89 && fileBytes[1] == 0x50 && 
            fileBytes[2] == 0x4E && fileBytes[3] == 0x47)
            return true;

        return false;
    }
}
