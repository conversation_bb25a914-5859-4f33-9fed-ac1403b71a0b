<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>AutoService - Car Repair and Car Service</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/icon/favicon.png">
  <link rel="apple-touch-icon-precomposed" href="assets/icon/apple-touch-icon-158-precomposed.png">

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,400i,600,600i,700,700i|Poppins:300,400,500,600,700&subset=latin" rel="stylesheet">

  <!-- CSS Stylesheets carregados no head para renderização otimizada -->
  <!-- CSS Base e Framework -->
  <link rel="stylesheet" href="assets/css/bootstrap.css">
  <link rel="stylesheet" href="assets/css/spacing-utilities.css">
  <link rel="stylesheet" href="assets/css/flexbox-utilities.css">

  <!-- Google Maps API -->
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDrYQFWTLnuAz2NBv9Rfezj8fmFrXOVboI"></script>

  <!-- Nota: Assets CSS/JS agora carregados via angular.json para melhor integração com Angular Router -->
</head>
<body class="page no-sidebar site-layout-full-width header-style-3 menu-has-search menu-has-cart">
  <app-root></app-root>

  <!-- Scripts agora carregados via angular.json -->

  <!-- Script de segurança para remover loading -->
  <script>
    // Timeout de segurança para garantir que o loading seja removido
    setTimeout(function() {
      // Remover todos os elementos de loading
      var loadingElements = document.querySelectorAll('.animsition-loading, .tp-loader');
      loadingElements.forEach(function(element) {
        element.remove();
      });

      // Garantir visibilidade do wrapper
      var wrapper = document.getElementById('wrapper');
      if (wrapper) {
        wrapper.style.opacity = '1';
        wrapper.style.visibility = 'visible';
        wrapper.classList.add('fade-in');
      }

      // Remover classe de loading do body
      document.body.classList.remove('animsition-loading', 'loading-active');

      console.log('Timeout de segurança: loading removido forçadamente');
    }, 4000); // 4 segundos

    // Também tentar remover quando a página estiver completamente carregada
    window.addEventListener('load', function() {
      setTimeout(function() {
        var loadingElements = document.querySelectorAll('.animsition-loading, .tp-loader');
        loadingElements.forEach(function(element) {
          element.remove();
        });

        var wrapper = document.getElementById('wrapper');
        if (wrapper) {
          wrapper.style.opacity = '1';
          wrapper.style.visibility = 'visible';
        }

        document.body.classList.remove('loading-active');
        console.log('Window load: loading removido');
      }, 1000);
    });
  </script>
</body>
</html>
