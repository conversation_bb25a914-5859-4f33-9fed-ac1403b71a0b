import { Routes } from '@angular/router';
import { HomeComponent } from './home/<USER>';

export const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'home', component: HomeComponent },

  // Páginas principais da navegação
  { path: 'empresa', loadComponent: () => import('./pages/about-page/about-page.component').then(m => m.AboutPageComponent) },
  { path: 'servicos', loadComponent: () => import('./pages/services-page/services-page.component').then(m => m.ServicesPageComponent) },
  { path: 'faqs', loadComponent: () => import('./pages/faqs-page/faqs-page.component').then(m => m.FaqsPageComponent) },
  { path: 'clientes-parceiros', loadComponent: () => import('./pages/clients-partners-page/clients-partners-page.component').then(m => m.ClientsPartnersPageComponent) },
  { path: 'trabalhe-conosco', loadComponent: () => import('./pages/careers-page/careers-page.component').then(m => m.CareersPageComponent) },
  { path: 'contato', loadComponent: () => import('./pages/contact-page/contact-page.component').then(m => m.ContactPageComponent) },

  // Página de solicitação de serviço
  { path: 'solicitacao-servico', loadComponent: () => import('./pages/service-request-page/service-request-page.component').then(m => m.ServiceRequestPageComponent) },

  // Páginas especializadas (manter compatibilidade com URLs antigas)
  { path: 'about', redirectTo: 'empresa' },
  { path: 'services', redirectTo: 'servicos' },
  { path: 'contact', redirectTo: 'contato' },
  { path: 'about-detail', loadComponent: () => import('./pages/about-detail-page/about-detail-page.component').then(m => m.AboutDetailPageComponent) },
  { path: 'appointment', loadComponent: () => import('./pages/appointment-page/appointment-page.component').then(m => m.AppointmentPageComponent) },
  { path: 'clients-partners', redirectTo: 'clientes-parceiros' },

  { path: '**', redirectTo: '' }
];
