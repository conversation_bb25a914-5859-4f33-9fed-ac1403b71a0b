import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface ServiceModel {
  id: number;
  guid: string;
  name: string;
  icon: string;
  description: string;
  valueAmount: number;
  active: boolean;
  deleted: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ServiceService {
  private apiUrl = `${environment.apiUrl}/api/service`;

  constructor(private http: HttpClient) { }

  /**
   * Lista todos os serviços ativos
   */
  getServices(): Observable<ServiceModel[]> {
    return this.http.get<ServiceModel[]>(`${this.apiUrl}/list`);
  }

  /**
   * Obtém um serviço por ID
   */
  getServiceById(guid: string): Observable<ServiceModel> {
    return this.http.get<ServiceModel>(`${this.apiUrl}/id/${guid}`);
  }

  /**
   * Adiciona um novo serviço
   */
  addService(service: ServiceModel): Observable<ServiceModel> {
    return this.http.post<ServiceModel>(`${this.apiUrl}/add`, service);
  }

  /**
   * Atualiza um serviço existente
   */
  updateService(service: ServiceModel): Observable<ServiceModel> {
    return this.http.put<ServiceModel>(`${this.apiUrl}/update`, service);
  }

  /**
   * Remove um serviço (soft delete)
   */
  deleteService(id: number): Observable<ServiceModel> {
    return this.http.delete<ServiceModel>(`${this.apiUrl}/delete/${id}`);
  }
}
