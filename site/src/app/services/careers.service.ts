import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface CautelaristDTO {
  name: string;
  cellphone: string;
  typeOfContact: string;
  email?: string;
  observacoes?: string;
  cnhDocumentBase64: string;
  proofOfResidenceBase64: string;
  qualificationsDocumentBase64: string;
  cnhImageBase64: string;
  qualificationsImageBase64: string;
  isAprove?: boolean;
  origem?: boolean;
}


@Injectable({
  providedIn: 'root'
})
export class CareersService {
  private apiUrl = `${environment.apiUrl}/api`;

  constructor(private http: HttpClient) { }

  /**
   * Submete uma candidatura de cautelarista
   */
  submitApplication(candidatura: CautelaristDTO): Observable<any> {
    return this.http.post(`${this.apiUrl}/cautelarist/add`, candidatura);
  }

  /**
   * Converte um arquivo para base64 com o prefixo adequado
   */
  fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = error => reject(error);
      reader.readAsDataURL(file);
    });
  }

  /**
   * Converte múltiplos arquivos para base64
   */
  async convertFilesToBase64(files: { 
    cnhDocument?: File;
    proofOfResidence?: File;
    qualificationsDocument?: File;
    cnhImage?: File;
    qualificationsImage?: File;
  }): Promise<{ [key: string]: string }> {
    const base64Results: { [key: string]: string } = {};
    
    for (const [key, file] of Object.entries(files)) {
      if (file) {
        try {
          const base64 = await this.fileToBase64(file);
          base64Results[key] = base64;
        } catch (error) {
          console.error(`Erro ao converter arquivo ${key} para base64:`, error);
          throw new Error(`Falha na conversão do arquivo ${key}`);
        }
      }
    }
    
    return base64Results;
  }
}