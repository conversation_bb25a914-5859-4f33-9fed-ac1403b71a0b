import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NavigationService } from './core/services/navigation.service';
import { ScrollManagementService } from './core/services/scroll-management.service';
import { ToastContainerComponent } from './shared/toast/toast-container.component';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, ToastContainerComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  title = 'angular-site';

  constructor(
    private navigationService: NavigationService,
    private scrollManagementService: ScrollManagementService
  ) {}

  ngOnInit() {
    // Serviços inicializados automaticamente:
    // NavigationService: Reinicialização de plugins jQuery e otimização de assets
    // ScrollManagementService: Gerenciamento automático de scroll para o topo
  }
}
