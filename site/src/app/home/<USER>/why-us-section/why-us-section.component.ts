import { Component, OnInit, After<PERSON><PERSON>wInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';

declare var $: any;

@Component({
  selector: 'app-why-us-section',
  imports: [],
  templateUrl: './why-us-section.component.html',
  styleUrl: './why-us-section.component.css'
})
export class WhyUsSectionComponent implements OnInit, AfterViewInit, OnDestroy {
  private scrollListener: any;
  private animationTriggered = false;

  ngOnInit() {
    // Componente inicializado
  }

  ngAfterViewInit() {
    // Aguardar um pouco para garantir que o DOM esteja pronto
    setTimeout(() => {
      this.setupProgressBars();
      this.setupScrollListener();
    }, 500);
  }

  ngOnDestroy() {
    if (this.scrollListener) {
      window.removeEventListener('scroll', this.scrollListener);
    }
  }

  private setupProgressBars(): void {
    try {
      // Verificar se as progress bars existem
      const progressBars = document.querySelectorAll('.wprt-progress .progress-bg');

      if (progressBars.length > 0) {
        // Verificar se já estão visíveis
        this.checkAndAnimateProgressBars();
      }
    } catch (error) {
      console.error('❌ WhyUsSection: Erro ao configurar progress bars:', error);
    }
  }

  private setupScrollListener(): void {
    this.scrollListener = () => {
      if (!this.animationTriggered) {
        this.checkAndAnimateProgressBars();
      }
    };

    window.addEventListener('scroll', this.scrollListener, { passive: true });
  }

  private checkAndAnimateProgressBars(): void {
    try {
      const progressBars = document.querySelectorAll('.wprt-progress .progress-bg');

      progressBars.forEach((bar: Element) => {
        const rect = bar.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;

        // Se o elemento está visível na viewport (com margem de 20%)
        if (rect.top >= 0 && rect.top <= windowHeight * 0.8) {
          if (!this.animationTriggered) {
            this.animateProgressBar(bar);
            this.animationTriggered = true;
          }
        }
      });
    } catch (error) {
      console.error('❌ WhyUsSection: Erro ao verificar progress bars:', error);
    }
  }

  private animateProgressBar(bar: Element): void {
    try {
      const $bar = $(bar);
      const percent = parseInt($bar.data('percent')) || 100;

      // Resetar antes de animar
      $bar.find('.progress-animate').css('width', '0%');
      $bar.parent('.wprt-progress').find('.perc').removeClass('show').css('width', '0%');

      // Animar
      $bar.find('.progress-animate').animate({
        "width": percent + '%'
      }, 1000, "easeInCirc");

      $bar.parent('.wprt-progress').find('.perc').addClass('show').animate({
        "width": percent + '%'
      }, 1000, "easeInCirc");

    } catch (error) {
      console.error('❌ WhyUsSection: Erro ao animar progress bar:', error);
    }
  }
}
