/* Fix para tooltip 100% não ser cortado */
.wprt-progress {
  overflow: visible !important;
  margin-bottom: 35px;
}

.wprt-progress .perc-wrap {
  position: relative;
  z-index: 10;
}

.wprt-progress .perc {
  position: relative;
}

/* Ajuste específico para tooltips de 100% */
.wprt-progress .perc > span {
  white-space: nowrap;
  position: relative;
  z-index: 10;
}

/* Garantir que o tooltip não seja cortado quando estiver no final */
.wprt-progress.style-1 .perc.show {
  margin-left: 0;
  position: relative;
  right: -10px;
}