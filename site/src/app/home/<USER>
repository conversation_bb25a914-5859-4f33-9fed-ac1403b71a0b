import { Component, OnInit, After<PERSON><PERSON>w<PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { HeaderComponent } from '../shared/header/header.component';
import { FooterComponent } from '../shared/footer/footer.component';
import { HeroSliderComponent } from './sections/hero-slider/hero-slider.component';
import { ServicesSectionComponent } from './sections/services-section/services-section.component';
import { WhyUsSectionComponent } from './sections/why-us-section/why-us-section.component';
import { FactsCounterComponent } from './sections/facts-counter/facts-counter.component';
import { CertifiedSectionComponent } from './sections/certified-section/certified-section.component';
import { AboutSectionComponent } from './sections/about-section/about-section.component';
import { PartnersSectionComponent } from './sections/partners-section/partners-section.component';
import { PromotionBannerComponent } from '../shared/promotion-banner/promotion-banner.component';
import { ServiceRequestModalComponent } from '../shared/service-request-modal/service-request-modal.component';

declare var $: any;



@Component({
  selector: 'app-home',
  imports: [
    HeaderComponent,
    FooterComponent,
    HeroSliderComponent,
    ServicesSectionComponent,
    WhyUsSectionComponent,
    FactsCounterComponent,
    CertifiedSectionComponent,
    AboutSectionComponent,
    PartnersSectionComponent,
    PromotionBannerComponent,
    ServiceRequestModalComponent
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  private initializationTimeout: any;

  constructor() {}

  ngOnInit() {
    // Componente inicializado
  }

  ngAfterViewInit() {
    // Aguardar um pouco para garantir que o DOM esteja pronto
    this.initializationTimeout = setTimeout(() => {
      this.initializePlugins();
    }, 100);
  }

  private initializePlugins(): void {
    try {
      // Usar sistema de integração existente se disponível
      if (typeof (window as any).AngularJQueryIntegration !== 'undefined') {
        const integration = (window as any).AngularJQueryIntegration;
        integration.initialized = false; // Forçar reinicialização
        integration.init();
        // Plugins reinicializados via AngularJQueryIntegration
      } else if (typeof (window as any).initializeJQueryPlugins === 'function') {
        (window as any).initializeJQueryPlugins();
        // Plugins inicializados via função global
      } else {
        // Sistema de integração jQuery não encontrado
      }

      // Verificação adicional específica para progress bars
      setTimeout(() => {
        this.ensureProgressBarsWork();
      }, 1000);

    } catch (error) {
      console.error('❌ Home: Erro na inicialização dos plugins:', error);
    }
  }

  private ensureProgressBarsWork(): void {
    try {
      // Verificar se as progress bars existem
      const progressBars = document.querySelectorAll('.progress-bg[data-inviewport="yes"]');

      if (progressBars.length > 0) {
        // Forçar inicialização das progress bars se necessário
        if (typeof (window as any).progressBar === 'function') {
          (window as any).progressBar();
        }

        if (typeof (window as any).inViewport === 'function') {
          (window as any).inViewport();
        }

        // Tentar função auxiliar para forçar animação
        if (typeof (window as any).forceProgressBarsAnimation === 'function') {
          setTimeout(() => {
            (window as any).forceProgressBarsAnimation();
          }, 1500);
        }

        // Verificar se alguma progress bar está visível e forçar trigger
        progressBars.forEach((bar: Element) => {
          const rect = bar.getBoundingClientRect();
          const windowHeight = window.innerHeight || document.documentElement.clientHeight;

          if (rect.top >= 0 && rect.top <= windowHeight * 0.9) {
            // Usar jQuery para trigger
            if (typeof $ !== 'undefined') {
              $(bar).trigger('on-appear');
            } else {
              (bar as any).dispatchEvent(new CustomEvent('on-appear'));
            }
          }
        });
      }
    } catch (error) {
      // Erro na verificação das progress bars
    }
  }

  ngOnDestroy() {
    if (this.initializationTimeout) {
      clearTimeout(this.initializationTimeout);
    }
  }




}
