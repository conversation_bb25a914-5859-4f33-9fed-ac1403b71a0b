/* Garantir visibilidade da página HOME */
:host {
  display: block;
  min-height: 100vh;
}

/* Fallback para animsition - garantir que a página seja sempre visível */
#wrapper {
  opacity: 1 !important;
  visibility: visible !important;
  min-height: 100vh;
}

/* Garantir que o conteúdo principal seja visível */
#main-content {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fallback para Revolution Slider */
.rev_slider_wrapper {
  opacity: 1 !important;
  visibility: visible !important;
}

.rev_slider {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Garantir que seções sejam visíveis mesmo sem animações */
.wprt-animation-block {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Loading state melhorado */
.animsition-loading {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
}

/* Garantir que elementos críticos sejam sempre visíveis */
.page-content {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fallback para casos onde jQuery falha completamente */
.no-js #wrapper,
.no-jquery #wrapper {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Melhorar performance de renderização */
#wrapper,
#main-content,
.page-content {
  will-change: auto;
  transform: translateZ(0);
}

/* Garantir que imagens sejam carregadas corretamente */
img {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fallback para counter animations */
.wprt-counter .number {
  opacity: 1 !important;
  visibility: visible !important;
}