/* Main Careers Section */
.careers-form-section {
  background-color: #fff;
}

/* Page Title */
.careers-main-title {
  font-size: 30px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  letter-spacing: 1px;
}

.careers-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 32px;
}

/* Steps Section */
.careers-steps {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.steps-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 25px;
  border-bottom: 1px solid #eee;
}

.step-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.step-number {
  width: 50px;
  height: 50px;
  /* background: #1c63b8; */
  border: 2px solid #1c63b8;
  color: #1c63b8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 20px;
  margin-right: 15px;
  flex-shrink: 0;
}
.step-content h4 {
  font-size: 14px;
  font-weight: 500;
  color: #777;
  margin: 0;
  line-height: 1.4;
}

/* Form Container */
.careers-form-container {
  background: #F7F7F7;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Form Sections */
.form-section {
  margin-bottom: 40px;
}

.form-section:last-of-type {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 25px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Form Controls */
.form-group {
  margin-bottom: 20px;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #1c63b8;
  box-shadow: 0 0 0 2px rgba(28, 99, 184, 0.1);
}

.form-control::placeholder {
  color: #999;
}

/* Contact Preference */
.contact-preference {
  margin-top: 0;
}

.preference-label {
  display: block;
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.radio-group {
  display: flex;
  gap: 15px;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}

.radio-option input[type="radio"] {
  margin: 0 6px 0 0;
  accent-color: #1c63b8;
  width: 16px;
  height: 16px;
}

.radio-text {
  color: #333;
  font-size: 14px;
}

/* Upload Section */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.upload-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.upload-item.observations-item {
  flex-direction: column;
  align-items: stretch;
  margin-top: 10px;
}

.upload-input-container {
  flex: 1;
  position: relative;
}

.upload-input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: #f8f9fa;
  color: #999;
  cursor: not-allowed;
  transition: all 0.3s ease;
  margin: 0;
}

.upload-input:not(:placeholder-shown) {
  background: #e8f5e8;
  border-color: #28a745;
  color: #155724;
  font-weight: 500;
  padding-right: 40px;
}

.upload-success-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #28a745;
  font-size: 16px;
  animation: fadeInScale 0.3s ease-in-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translateY(-50%) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

.upload-btn {
  background: #ffa500;
  color: white;
  border: none;
  height: 54px;
  padding: 12px 25px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 100px;
  width: 100%;
  max-width: 180px;
  white-space: nowrap;
}

.upload-btn:hover {
  background: #e6940a;
}

/* Observations textarea */
.observations-textarea {
  resize: vertical;
  min-height: 100px;
  margin-top: 0;
}

/* Submit Button */
.form-submit {
  text-align: center;
  margin-top: 30px;
}

.submit-btn {
  background: #1c63b8;
  color: white;
  border: none;
  padding: 15px 50px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.submit-btn:hover:not(:disabled) {
  background: #164a8a;
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .careers-form-container {
    padding: 25px;
  }

  .careers-steps {
    padding: 20px;
  }

  .careers-main-title {
    font-size: 28px;
  }

  .radio-group {
    flex-direction: column;
    gap: 8px;
  }

  .upload-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .upload-btn {
    align-self: flex-start;
    min-width: 120px;
  }

  .contact-preference {
    margin-top: 15px;
  }
}