<!-- Header -->
<app-header headerType="auto"></app-header>

<!-- Featured Title -->
<app-featured-title
  [title]="pageTitle"
  [breadcrumbs]="breadcrumbs"
  alignment="left">
</app-featured-title>

<!-- Main Content -->
<div id="main-content" class="site-main clearfix">
  <div id="content-wrap">
    <div id="site-content" class="site-content clearfix">
      <div id="inner-content" class="inner-content-wrap">
        <div class="page-content">

          <!-- CAREERS FORM SECTION -->
          <div class="careers-form-section">
            <div class="container pt-80">
              <div class="row">
                <div class="col-md-12">

                  <!-- Page Title -->
                  <div class="text-center">
                    <h1 class="careers-main-title">TRABALHE CONOSCO</h1>
                    <p class="careers-subtitle mb-1">Se você é cautelarista, certificado e tem interesse de trabalhar conosco, preencha as informações abaixo.</p>
                  </div>
                </div>
              </div>

              <div class="row">
                <!-- Left Column - Steps -->
                <div class="col-md-4">
                  <div class="careers-steps">
                    <h3 class="steps-title">PREENCHA AS INFORMAÇÕES</h3>

                    <div class="step-item">
                      <div class="step-number">1</div>
                      <div class="step-content">
                        <h4>Informações de Contato</h4>
                      </div>
                    </div>

                    <div class="step-item">
                      <div class="step-number">2</div>
                      <div class="step-content">
                        <h4>Upload de Documentos Pessoais</h4>
                      </div>
                    </div>

                    <div class="step-item">
                      <div class="step-number">3</div>
                      <div class="step-content">
                        <h4>Selfie Documentos</h4>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Right Column - Form -->
                <div class="col-md-8">
                  <div class="careers-form-container">
                    <form [formGroup]="careersForm" (ngSubmit)="onSubmit()">

                      <!-- Contact Information Section -->
                      <div class="form-section">
                        <h3 class="section-title">INFORMAÇÕES DE CONTATO</h3>

                        <div class="row">
                          <div class="col-md-6">
                            <div class="form-group">
                              <input type="text"
                                     class="form-control"
                                     placeholder="Nome Completo*"
                                     formControlName="name">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="form-group">
                              <input type="text"
                                     class="form-control"
                                     placeholder="Celular*"
                                     formControlName="cellphone">
                            </div>
                          </div>
                        </div>

                        <div class="row">
                          <div class="col-md-6">
                            <div class="form-group">
                              <input type="email"
                                     class="form-control"
                                     placeholder="E-mail"
                                     formControlName="email">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="contact-preference">
                              <label class="preference-label">Como prefere ser contatado?</label>
                              <div class="radio-group">
                                <label class="radio-option">
                                  <input type="radio" name="typeOfContact" value="celular" formControlName="typeOfContact">
                                  <span class="radio-text">Celular</span>
                                </label>
                                <label class="radio-option">
                                  <input type="radio" name="typeOfContact" value="email" formControlName="typeOfContact">
                                  <span class="radio-text">E-mail</span>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="row">
                          <div class="col-md-12">
                            <div class="form-group">
                              <textarea class="form-control"
                                        placeholder="Observações (opcional)"
                                        formControlName="observacoes"
                                        rows="3"
                                        maxlength="1000"></textarea>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Documentation Section -->
                      <div class="form-section">
                        <h3 class="section-title">DOCUMENTAÇÃO</h3>

                        <div class="upload-section">
                          <div class="upload-item">
                            <div class="upload-input-container">
                              <input type="text"
                                     class="upload-input"
                                     [value]="getFileName('cnhDocument')"
                                     placeholder="CNH (PDF)"
                                     readonly>
                              <i class="fa fa-check-circle upload-success-icon" *ngIf="uploadedFiles['cnhDocument']"></i>
                            </div>
                            <button type="button" class="upload-btn" (click)="triggerFileUpload('cnhDocument')">
                              UPLOAD
                            </button>
                            <input type="file" #cnhDocumentFile (change)="onFileSelected($event, 'cnhDocument')" style="display: none;" accept=".pdf">
                          </div>

                          <div class="upload-item">
                            <div class="upload-input-container">
                              <input type="text"
                                     class="upload-input"
                                     [value]="getFileName('proofOfResidence')"
                                     placeholder="COMPROVANTE DE RESIDÊNCIA (PDF)"
                                     readonly>
                              <i class="fa fa-check-circle upload-success-icon" *ngIf="uploadedFiles['proofOfResidence']"></i>
                            </div>
                            <button type="button" class="upload-btn" (click)="triggerFileUpload('proofOfResidence')">
                              UPLOAD
                            </button>
                            <input type="file" #proofOfResidenceFile (change)="onFileSelected($event, 'proofOfResidence')" style="display: none;" accept=".pdf">
                          </div>

                          <div class="upload-item">
                            <div class="upload-input-container">
                              <input type="text"
                                     class="upload-input"
                                     [value]="getFileName('qualificationsDocument')"
                                     placeholder="CERTIFICADO HABILITAÇÕES (PDF)"
                                     readonly>
                              <i class="fa fa-check-circle upload-success-icon" *ngIf="uploadedFiles['qualificationsDocument']"></i>
                            </div>
                            <button type="button" class="upload-btn" (click)="triggerFileUpload('qualificationsDocument')">
                              UPLOAD
                            </button>
                            <input type="file" #qualificationsDocumentFile (change)="onFileSelected($event, 'qualificationsDocument')" style="display: none;" accept=".pdf">
                          </div>
                        </div>
                      </div>

                      <!-- Selfie Documents Section -->
                      <div class="form-section">
                        <h3 class="section-title">SELFIE COM DOCUMENTOS</h3>

                        <div class="upload-section">
                          <div class="upload-item">
                            <div class="upload-input-container">
                              <input type="text"
                                     class="upload-input"
                                     [value]="getFileName('cnhImage')"
                                     placeholder="SELFIE COM CNH (JPG/PNG)"
                                     readonly>
                              <i class="fa fa-check-circle upload-success-icon" *ngIf="uploadedFiles['cnhImage']"></i>
                            </div>
                            <button type="button" class="upload-btn" (click)="triggerFileUpload('cnhImage')">
                              UPLOAD
                            </button>
                            <input type="file" #cnhImageFile (change)="onFileSelected($event, 'cnhImage')" style="display: none;" accept=".jpg,.jpeg,.png">
                          </div>

                          <div class="upload-item">
                            <div class="upload-input-container">
                              <input type="text"
                                     class="upload-input"
                                     [value]="getFileName('qualificationsImage')"
                                     placeholder="SELFIE COM CERTIFICADO (JPG/PNG)"
                                     readonly>
                              <i class="fa fa-check-circle upload-success-icon" *ngIf="uploadedFiles['qualificationsImage']"></i>
                            </div>
                            <button type="button" class="upload-btn" (click)="triggerFileUpload('qualificationsImage')">
                              UPLOAD
                            </button>
                            <input type="file" #qualificationsImageFile (change)="onFileSelected($event, 'qualificationsImage')" style="display: none;" accept=".jpg,.jpeg,.png">
                          </div>

                        </div>
                      </div>

                      <!-- Submit Button -->
                      <div class="form-submit">
                        <button type="submit" class="submit-btn" [disabled]="!careersForm.valid || isSubmitting">
                          <span *ngIf="!isSubmitting">ENVIAR</span>
                          <span *ngIf="isSubmitting">ENVIANDO...</span>
                        </button>
                      </div>

                    </form>
                  </div>
                </div>
              </div>

            </div>
            <div class="pb-70"></div>
          </div>
          <!-- END CAREERS FORM SECTION -->

          <!-- PROMOTION BANNER -->
          <app-promotion-banner
            title="Junte-se à nossa equipe e faça parte do futuro da vistoria automotiva"
            buttonText="SOLICITE AGORA"
            buttonAction="service-request">
          </app-promotion-banner>
          <!-- END PROMOTION BANNER -->

        </div><!-- /.page-content -->
      </div><!-- /#inner-content -->
    </div><!-- /#site-content -->
  </div><!-- /#content-wrap -->
</div><!-- /#main-content -->

<!-- Footer -->
<app-footer></app-footer>
