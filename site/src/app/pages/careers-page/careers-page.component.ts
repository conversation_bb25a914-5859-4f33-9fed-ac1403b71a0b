import { Component, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { FeaturedTitleComponent } from '../../shared/featured-title/featured-title.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';
import { CareersService, CautelaristDTO } from '../../services/careers.service';
import { ToastService } from '../../shared/toast/toast.service';

@Component({
  selector: 'app-careers-page',
  imports: [CommonModule, ReactiveFormsModule, HeaderComponent, FooterComponent, FeaturedTitleComponent, PromotionBannerComponent],
  templateUrl: './careers-page.component.html',
  styleUrl: './careers-page.component.css'
})
export class CareersPageComponent implements AfterViewInit {
  @ViewChild('cnhDocumentFile') cnhDocumentFileInput!: ElementRef;
  @ViewChild('proofOfResidenceFile') proofOfResidenceFileInput!: ElementRef;
  @ViewChild('qualificationsDocumentFile') qualificationsDocumentFileInput!: ElementRef;
  @ViewChild('cnhImageFile') cnhImageFileInput!: ElementRef;
  @ViewChild('qualificationsImageFile') qualificationsImageFileInput!: ElementRef;

  pageTitle = "TRABALHE CONOSCO";
  breadcrumbs = [
    { label: 'Home', url: '/' },
    { label: 'Trabalhe Conosco', url: '/trabalhe-conosco' }
  ];

  careersForm: FormGroup;
  uploadedFiles: { [key: string]: File } = {};
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private careersService: CareersService,
    private toastService: ToastService
  ) {
    this.careersForm = this.fb.group({
      name: ['', [Validators.required]],
      cellphone: ['', [Validators.required]],
      typeOfContact: ['', [Validators.required]],
      email: [''],
      observacoes: ['']
    });
  }

  ngAfterViewInit() {
    // Aplicar máscaras nos campos após a view ser inicializada
    this.applyMasks();
  }

  triggerFileUpload(fileType: string): void {
    switch (fileType) {
      case 'cnhDocument':
        this.cnhDocumentFileInput.nativeElement.click();
        break;
      case 'proofOfResidence':
        this.proofOfResidenceFileInput.nativeElement.click();
        break;
      case 'qualificationsDocument':
        this.qualificationsDocumentFileInput.nativeElement.click();
        break;
      case 'cnhImage':
        this.cnhImageFileInput.nativeElement.click();
        break;
      case 'qualificationsImage':
        this.qualificationsImageFileInput.nativeElement.click();
        break;
    }
  }

  onFileSelected(event: any, fileType: string): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadedFiles[fileType] = file;
      console.log(`Arquivo ${fileType} selecionado:`, file.name);

      // Feedback visual para o usuário
      this.showFileUploadSuccess(fileType, file.name);
    }
  }

  getFileName(fileType: string): string {
    if (this.uploadedFiles[fileType]) {
      return this.uploadedFiles[fileType].name;
    }
    return '';
  }

  private showFileUploadSuccess(fileType: string, fileName: string): void {
    // Aqui você pode adicionar uma notificação toast se desejar
    console.log(`✅ Arquivo ${fileName} carregado com sucesso para ${fileType}`);
  }

  async onSubmit(): Promise<void> {
    if (this.careersForm.valid && this.validateFiles()) {
      this.isSubmitting = true;

      try {
        // Converter arquivos para base64
        const base64Files = await this.careersService.convertFilesToBase64({
          cnhDocument: this.uploadedFiles['cnhDocument'],
          proofOfResidence: this.uploadedFiles['proofOfResidence'],
          qualificationsDocument: this.uploadedFiles['qualificationsDocument'],
          cnhImage: this.uploadedFiles['cnhImage'],
          qualificationsImage: this.uploadedFiles['qualificationsImage']
        });

        // Montar DTO para envio
        const candidatura: CautelaristDTO = {
          name: this.careersForm.get('name')?.value,
          cellphone: this.careersForm.get('cellphone')?.value,
          typeOfContact: this.careersForm.get('typeOfContact')?.value,
          email: this.careersForm.get('email')?.value || undefined,
          observacoes: this.careersForm.get('observacoes')?.value || undefined,
          cnhDocumentBase64: base64Files['cnhDocument'],
          proofOfResidenceBase64: base64Files['proofOfResidence'],
          qualificationsDocumentBase64: base64Files['qualificationsDocument'],
          cnhImageBase64: base64Files['cnhImage'],
          qualificationsImageBase64: base64Files['qualificationsImage'],
          isAprove: false,
          origem: true // True para "trabalhe conosco"
        };

        // Enviar candidatura
        await this.careersService.submitApplication(candidatura).toPromise();

        this.toastService.success(
          'Candidatura enviada com sucesso! Entraremos em contato em breve.',
          'Candidatura Enviada',
          {
            duration: 8000,
            actions: [
              {
                label: 'Enviar Outra',
                action: () => {
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }
              }
            ]
          }
        );

        // Reset do formulário
        this.careersForm.reset();
        this.uploadedFiles = {};
      } catch (error) {
        console.error('Erro ao enviar candidatura:', error);
        this.toastService.error(
          'Erro ao enviar candidatura. Por favor, tente novamente.',
          'Erro no Envio',
          {
            duration: 0,
            actions: [
              {
                label: 'Tentar Novamente',
                action: () => {
                  this.onSubmit();
                },
                style: 'primary'
              }
            ]
          }
        );
      } finally {
        this.isSubmitting = false;
      }
    } else {
      this.toastService.warning(
        'Por favor, preencha todos os campos obrigatórios e faça upload de todos os documentos.',
        'Campos Obrigatórios',
        {
          duration: 6000,
          actions: [
            {
              label: 'Verificar Formulário',
              action: () => {
                // Scroll para o primeiro campo com erro
                const firstError = document.querySelector('.ng-invalid');
                if (firstError) {
                  firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
              }
            }
          ]
        }
      );
    }
  }

  private validateFiles(): boolean {
    const requiredFiles = ['cnhDocument', 'proofOfResidence', 'qualificationsDocument', 'cnhImage', 'qualificationsImage'];
    return requiredFiles.every(file => this.uploadedFiles[file] !== undefined);
  }

  // Métodos de máscara
  private applyMasks() {
    // Usar múltiplas tentativas para garantir que os elementos estejam disponíveis
    this.tryApplyMasks(0);
  }

  private tryApplyMasks(attempt: number) {
    const maxAttempts = 5;
    const delay = 200 * (attempt + 1); // Aumentar delay progressivamente

    setTimeout(() => {
      const fieldsApplied = [
        this.applyMaskToField('cellphone', this.celularMask)
      ];

      const successfullyApplied = fieldsApplied.filter(Boolean).length;

      // Se nem todos os campos foram encontrados e ainda temos tentativas, tentar novamente
      if (successfullyApplied < fieldsApplied.length && attempt < maxAttempts) {
        console.log(`Tentativa ${attempt + 1}: ${successfullyApplied}/${fieldsApplied.length} máscaras aplicadas. Tentando novamente...`);
        this.tryApplyMasks(attempt + 1);
      } else {
        console.log(`Máscaras aplicadas no formulário Trabalhe Conosco: ${successfullyApplied}/${fieldsApplied.length} campos`);
      }
    }, delay);
  }

  private applyMaskToField(fieldId: string, maskFunction: (value: string) => string): boolean {
    const element = document.querySelector(`input[formControlName="${fieldId}"]`) as HTMLInputElement;
    if (element) {
      // Remover listeners anteriores se existirem
      element.removeEventListener('input', element.dataset['maskListener'] as any);

      const maskListener = (e: Event) => {
        const target = e.target as HTMLInputElement;
        const cursorPosition = target.selectionStart;
        const oldValue = target.value;
        const newValue = maskFunction(target.value);

        if (oldValue !== newValue) {
          target.value = newValue;

          // Calcular posição correta do cursor baseada nos dígitos digitados
          if (cursorPosition !== null) {
            const newPosition = this.calculateCursorPosition(oldValue, newValue, cursorPosition);
            target.setSelectionRange(newPosition, newPosition);
          }

          // Disparar evento de mudança para o Angular detectar
          target.dispatchEvent(new Event('input', { bubbles: true }));
        }
      };

      element.addEventListener('input', maskListener);
      element.dataset['maskListener'] = maskListener.toString();

      return true;
    }
    return false;
  }

  /**
   * Calcula a posição correta do cursor após aplicar a máscara
   * Baseado na quantidade de dígitos antes da posição original do cursor
   */
  private calculateCursorPosition(oldValue: string, newValue: string, oldCursorPosition: number): number {
    // Contar quantos dígitos existem antes da posição do cursor no valor antigo
    const digitsBeforeCursor = oldValue.substring(0, oldCursorPosition).replace(/\D/g, '').length;

    // Encontrar a posição no novo valor onde temos a mesma quantidade de dígitos
    let digitCount = 0;
    let newPosition = 0;

    for (let i = 0; i < newValue.length; i++) {
      if (/\d/.test(newValue[i])) {
        digitCount++;
        if (digitCount > digitsBeforeCursor) {
          newPosition = i;
          break;
        }
      }
      newPosition = i + 1;
    }

    return Math.min(newPosition, newValue.length);
  }

  private celularMask = (value: string): string => {
    const cleanValue = value.replace(/\D/g, '');
    if (cleanValue.length <= 10) {
      // Telefone fixo: (XX) XXXX-XXXX
      return cleanValue
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .replace(/(-\d{4})\d+?$/, '$1')
        .substring(0, 14);
    } else {
      // Celular: (XX) XXXXX-XXXX
      return cleanValue
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{5})(\d)/, '$1-$2')
        .replace(/(-\d{4})\d+?$/, '$1')
        .substring(0, 15);
    }
  }
}
