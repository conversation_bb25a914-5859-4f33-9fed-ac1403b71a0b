/* Espaçamento geral da página */
.row-intro,
.row-clients,
.row-partners,
.row-testimonials {
  padding-bottom: 30px;
}

.row-partners {
  padding-top: 30px;
  padding-bottom: 50px;
}

/* Reduzir espaçamento dos headings */
.wprt-headings {
  margin-bottom: 30px !important;
}

.wprt-headings .heading {
  margin-bottom: 15px !important;
}

.wprt-headings .sub-heading {
  margin-bottom: 0 !important;
}

/* Grid fix - garantir que as colunas tenham altura igual */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col-md-4 {
  display: flex;
  flex-direction: column;
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 30px;
}

.client-item,
.partner-item {
  padding: 30px 20px;
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.client-item:hover,
.partner-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.client-logo,
.partner-logo {
  margin-bottom: 20px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.client-logo img,
.partner-logo img {
  max-height: 60px;
  max-width: 150px;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

/* Fallback SVG styles */
.fallback-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  animation: fadeIn 0.5s ease-in-out;
}

.fallback-svg svg {
  opacity: 0.85;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
  max-width: 100%;
  height: auto;
}

.client-item:hover .fallback-svg svg,
.partner-item:hover .fallback-svg svg {
  opacity: 1;
  transform: scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.15));
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 0.85;
    transform: scale(1);
  }
}

.client-name,
.partner-name {
  margin-bottom: 10px;
  color: #333;
}

.client-category {
  display: block;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 15px;
  font-weight: 600;
}

.client-description,
.partner-description {
  color: #777;
  font-size: 14px;
  line-height: 1.6;
  flex-grow: 1;
  margin-bottom: 0;
}

.testimonial-item {
  padding: 30px 20px;
  background: #f9f9f9;
  border-radius: 5px;
  border-left: 4px solid #1c63b8;
}

.testimonial-text {
  font-style: italic;
  color: #555;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.6;
}

.author-name {
  margin-bottom: 5px;
  color: #333;
}

.author-company {
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stars {
  font-size: 16px;
}

.stars i {
  margin-right: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .row {
    flex-direction: column;
  }

  .col-md-4 {
    width: 100%;
    max-width: 100%;
    margin-bottom: 20px;
  }

  .client-item,
  .partner-item {
    padding: 20px 15px;
  }

  .client-logo,
  .partner-logo {
    height: 60px;
  }

  .client-logo img,
  .partner-logo img {
    max-height: 45px;
  }

  .fallback-svg svg {
    max-width: 120px;
    height: auto;
  }
}

@media (min-width: 769px) and (max-width: 991px) {
  .col-md-4 {
    width: 50%;
    max-width: 50%;
  }
}

@media (min-width: 992px) {
  .col-md-4 {
    width: 33.333333%;
    max-width: 33.333333%;
  }
}