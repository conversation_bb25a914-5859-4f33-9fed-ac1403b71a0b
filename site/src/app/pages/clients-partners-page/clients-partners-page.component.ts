import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { FeaturedTitleComponent } from '../../shared/featured-title/featured-title.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';

@Component({
  selector: 'app-clients-partners-page',
  imports: [CommonModule, HeaderComponent, FooterComponent, FeaturedTitleComponent, PromotionBannerComponent],
  templateUrl: './clients-partners-page.component.html',
  styleUrl: './clients-partners-page.component.css'
})
export class ClientsPartnersPageComponent {
  pageTitle = "CLIENTES E PARCEIROS";
  breadcrumbs = [
    { label: 'Home', url: '/' },
    { label: 'Clientes e Parceiros', url: '/clientes-parceiros' }
  ];

  clients = [
    {
      name: 'Empresa de Logística ABC',
      logo: 'assets/img/fallback/client-1.png', // Imagem que não existe para forçar SVG
      description: 'Manutenção de frota com mais de 50 veículos comerciais',
      category: 'LOGÍSTICA'
    },
    {
      name: 'Transportadora XYZ',
      logo: 'assets/img/fallback/client-2.png', // Imagem que não existe para forçar SVG
      description: 'Serviços especializados em caminhões e utilitários',
      category: 'TRANSPORTE'
    },
    {
      name: 'Concessionária Premium',
      logo: 'assets/img/fallback/client-3.png', // Imagem que não existe para forçar SVG
      description: 'Parceria em serviços pós-venda e garantia',
      category: 'AUTOMOTIVO'
    },
    {
      name: 'Empresa de Delivery',
      logo: 'assets/img/fallback/client-4.png', // Imagem que não existe para forçar SVG
      description: 'Manutenção preventiva de motocicletas e carros',
      category: 'DELIVERY'
    },
    {
      name: 'Cooperativa de Táxi',
      logo: 'assets/img/fallback/client-5.png', // Imagem que não existe para forçar SVG
      description: 'Atendimento especializado para veículos de transporte',
      category: 'TRANSPORTE'
    },
    {
      name: 'Locadora de Veículos',
      logo: 'assets/img/fallback/client-6.png', // Imagem que não existe para forçar SVG
      description: 'Manutenção de frota diversificada',
      category: 'LOCAÇÃO'
    }
  ];

  partners = [
    {
      name: 'Bosch',
      logo: 'assets/img/fallback/partner-1.png', // Imagem que não existe para forçar SVG
      description: 'Peças e sistemas automotivos de alta qualidade'
    },
    {
      name: 'NGK',
      logo: 'assets/img/fallback/partner-2.png', // Imagem que não existe para forçar SVG
      description: 'Velas de ignição e sensores automotivos'
    },
    {
      name: 'Mobil 1',
      logo: 'assets/img/fallback/partner-3.png', // Imagem que não existe para forçar SVG
      description: 'Óleos lubrificantes premium'
    },
    {
      name: 'Continental',
      logo: 'assets/img/fallback/partner-4.png', // Imagem que não existe para forçar SVG
      description: 'Pneus e sistemas de freios'
    },
    {
      name: 'Denso',
      logo: 'assets/img/fallback/partner-5.png', // Imagem que não existe para forçar SVG
      description: 'Componentes eletrônicos automotivos'
    },
    {
      name: 'Mann Filter',
      logo: 'assets/img/fallback/partner-6.png', // Imagem que não existe para forçar SVG
      description: 'Filtros automotivos de alta performance'
    }
  ];

  // Método para lidar com erro de carregamento de imagem
  onImageError(event: any, type: 'client' | 'partner'): void {
    const target = event.target;
    target.style.display = 'none';

    // Encontrar o container da logo e adicionar SVG fallback
    const logoContainer = target.parentElement;
    if (logoContainer && !logoContainer.querySelector('.fallback-svg')) {
      // Gerar um índice aleatório para variar os SVGs
      const randomIndex = Math.floor(Math.random() * 3);
      const svgElement = this.createFallbackSVG(type, randomIndex);
      logoContainer.appendChild(svgElement);
    }
  }

  // Criar SVG de fallback
  private createFallbackSVG(type: 'client' | 'partner', variation: number = 0): HTMLElement {
    const svgContainer = document.createElement('div');
    svgContainer.className = 'fallback-svg';

    if (type === 'client') {
      const clientSVGs = [
        // Variação 1 - Empresa de Logística (caminhões)
        `<svg width="140" height="60" viewBox="0 0 140 60" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="10" width="130" height="40" rx="8" fill="#f8f9fa" stroke="#1c63b8" stroke-width="2"/>
          <rect x="15" y="20" width="28" height="15" rx="2" fill="#1c63b8"/>
          <rect x="45" y="25" width="10" height="10" rx="1" fill="#1c63b8"/>
          <circle cx="22" cy="38" r="4" fill="#1c63b8"/>
          <circle cx="36" cy="38" r="4" fill="#1c63b8"/>
          <circle cx="48" cy="38" r="4" fill="#1c63b8"/>
          <rect x="65" y="20" width="55" height="4" rx="2" fill="#1c63b8"/>
          <rect x="65" y="28" width="40" height="3" rx="1" fill="#6c757d"/>
          <rect x="65" y="34" width="50" height="3" rx="1" fill="#6c757d"/>
        </svg>`,

        // Variação 2 - Empresa de Delivery (moto)
        `<svg width="140" height="60" viewBox="0 0 140 60" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="10" width="130" height="40" rx="8" fill="#f8f9fa" stroke="#1c63b8" stroke-width="2"/>
          <rect x="15" y="22" width="15" height="8" rx="2" fill="#1c63b8"/>
          <rect x="32" y="20" width="3" height="12" rx="1" fill="#1c63b8"/>
          <circle cx="20" cy="35" r="4" fill="#1c63b8"/>
          <circle cx="40" cy="35" r="4" fill="#1c63b8"/>
          <path d="M25 18 L30 15 L35 18" stroke="#1c63b8" stroke-width="2" fill="none"/>
          <rect x="55" y="20" width="65" height="4" rx="2" fill="#1c63b8"/>
          <rect x="55" y="28" width="45" height="3" rx="1" fill="#6c757d"/>
          <rect x="55" y="34" width="55" height="3" rx="1" fill="#6c757d"/>
        </svg>`,

        // Variação 3 - Concessionária (carro)
        `<svg width="140" height="60" viewBox="0 0 140 60" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="10" width="130" height="40" rx="8" fill="#f8f9fa" stroke="#1c63b8" stroke-width="2"/>
          <path d="M15 28 L20 22 L35 22 L40 28 L40 35 L15 35 Z" fill="#1c63b8"/>
          <circle cx="22" cy="38" r="3" fill="#1c63b8"/>
          <circle cx="33" cy="38" r="3" fill="#1c63b8"/>
          <rect x="18 22" width="15" height="4" rx="1" fill="#f8f9fa"/>
          <rect x="55" y="20" width="65" height="4" rx="2" fill="#1c63b8"/>
          <rect x="55" y="28" width="45" height="3" rx="1" fill="#6c757d"/>
          <rect x="55" y="34" width="55" height="3" rx="1" fill="#6c757d"/>
        </svg>`
      ];

      svgContainer.innerHTML = clientSVGs[variation % clientSVGs.length];
    } else {
      const partnerSVGs = [
        // Variação 1 - Bosch (peças automotivas)
        `<svg width="140" height="60" viewBox="0 0 140 60" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="5" width="130" height="50" rx="12" fill="#1c63b8"/>
          <circle cx="30" cy="30" r="12" fill="#fff"/>
          <circle cx="30" cy="30" r="6" fill="#1c63b8"/>
          <rect x="24" y="18" width="2" height="8" fill="#fff"/>
          <rect x="34" y="18" width="2" height="8" fill="#fff"/>
          <rect x="24" y="34" width="2" height="8" fill="#fff"/>
          <rect x="34" y="34" width="2" height="8" fill="#fff"/>
          <rect x="55" y="18" width="65" height="6" rx="3" fill="#fff"/>
          <rect x="55" y="28" width="50" height="4" rx="2" fill="#fff"/>
          <rect x="55" y="36" width="55" height="4" rx="2" fill="#fff"/>
        </svg>`,

        // Variação 2 - NGK (velas e sensores)
        `<svg width="140" height="60" viewBox="0 0 140 60" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="5" width="130" height="50" rx="12" fill="#f8f9fa" stroke="#1c63b8" stroke-width="2"/>
          <rect x="25" y="18" width="4" height="24" rx="2" fill="#1c63b8"/>
          <circle cx="27" cy="15" r="2" fill="#1c63b8"/>
          <rect x="35" y="20" width="4" height="20" rx="2" fill="#1c63b8"/>
          <circle cx="37" cy="17" r="2" fill="#1c63b8"/>
          <rect x="55" y="18" width="65" height="6" rx="3" fill="#1c63b8"/>
          <rect x="55" y="28" width="50" height="4" rx="2" fill="#1c63b8"/>
          <rect x="55" y="36" width="55" height="4" rx="2" fill="#1c63b8"/>
        </svg>`,

        // Variação 3 - Mobil/Continental (óleos e pneus)
        `<svg width="140" height="60" viewBox="0 0 140 60" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="5" width="130" height="50" rx="12" fill="#1c63b8"/>
          <circle cx="30" cy="30" r="12" fill="#fff" stroke="#1c63b8" stroke-width="2"/>
          <circle cx="30" cy="30" r="8" fill="none" stroke="#1c63b8" stroke-width="2"/>
          <circle cx="30" cy="30" r="4" fill="#1c63b8"/>
          <rect x="55" y="18" width="65" height="6" rx="3" fill="#fff"/>
          <rect x="55" y="28" width="50" height="4" rx="2" fill="#fff"/>
          <rect x="55" y="36" width="55" height="4" rx="2" fill="#fff"/>
        </svg>`
      ];

      svgContainer.innerHTML = partnerSVGs[variation % partnerSVGs.length];
    }

    return svgContainer;
  }

  testimonials = [
    {
      name: 'João Silva',
      company: 'Transportadora XYZ',
      text: 'Excelente atendimento e qualidade nos serviços. Nossa frota está sempre em perfeito estado.',
      rating: 5
    },
    {
      name: 'Maria Santos',
      company: 'Empresa de Logística ABC',
      text: 'Parceria de longa data. Confiamos totalmente na expertise da equipe.',
      rating: 5
    },
    {
      name: 'Carlos Oliveira',
      company: 'Concessionária Premium',
      text: 'Profissionalismo e agilidade que nossos clientes merecem.',
      rating: 5
    }
  ];
}
