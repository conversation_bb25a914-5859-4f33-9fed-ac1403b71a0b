import { Component, OnInit, AfterViewInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FeaturedTitleComponent, BreadcrumbItem } from '../../shared/featured-title/featured-title.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';
import { TabsComponent, TabItem } from '../../shared/tabs/tabs.component';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { ServiceService, ServiceModel } from '../../services/service.service';
import { environment } from '../../../environments/environment';

declare var $: any;

@Component({
  selector: 'app-services-page',
  imports: [
    CommonModule,
    HeaderComponent,
    FooterComponent,
    FeaturedTitleComponent,
    TabsComponent,
    PromotionBannerComponent
  ],
  templateUrl: './services-page.component.html',
  styleUrl: './services-page.component.css'
})
export class ServicesPageComponent implements OnInit, AfterViewInit, OnDestroy {
  private initializationTimeout: any;
  services: ServiceModel[] = [];
  loading = true;
  error: string | null = null;

  constructor(
    private router: Router,
    private serviceService: ServiceService
  ) {}

  ngOnInit() {
    console.log('🔧 Services: Componente inicializado');
    this.loadServices();
  }

  private loadServices(): void {
    this.loading = true;
    this.error = null;

    this.serviceService.getServices().subscribe({
      next: (services) => {
        this.services = services;
        this.loading = false;
        console.log('✅ Services: Serviços carregados:', services);
      },
      error: (error) => {
        this.error = 'Erro ao carregar serviços. Tente novamente mais tarde.';
        this.loading = false;
        console.error('❌ Services: Erro ao carregar serviços:', error);
      }
    });
  }

  ngAfterViewInit() {
    this.initializationTimeout = setTimeout(() => {
      this.initializePlugins();
    }, 100);
  }

  ngOnDestroy() {
    if (this.initializationTimeout) {
      clearTimeout(this.initializationTimeout);
    }
  }

  private initializePlugins(): void {
    try {
      if (typeof (window as any).AngularJQueryIntegration !== 'undefined') {
        const integration = (window as any).AngularJQueryIntegration;
        integration.initialized = false;
        integration.init();
        console.log('✅ Services: Plugins reinicializados');
      } else if (typeof (window as any).initializeJQueryPlugins === 'function') {
        (window as any).initializeJQueryPlugins();
        console.log('✅ Services: Plugins inicializados via função global');
      }
    } catch (error) {
      console.error('❌ Services: Erro na inicialização dos plugins:', error);
    }
  }

  openServiceModal(event: Event) {
    event.preventDefault();
    this.router.navigate(['/solicitacao-servico']);
  }

  /**
   * Retorna a URL da imagem do serviço ou uma imagem padrão
   */
  getServiceImage(service: ServiceModel): string {
    if (service.icon && service.icon.trim() !== '') {
      // Verificar se é um arquivo de imagem (upload)
      if (this.isImageFile(service.icon)) {
        return `${environment.uploadUrl}/service-icons/${service.icon}`;
      }
      // Se não for um arquivo de imagem, pode ser uma URL completa
      if (service.icon.startsWith('http') && !service.icon.includes('example.com')) {
        return service.icon;
      }
    }
    // Usar uma das imagens existentes como padrão
    return 'assets/img/services/service-1-600-438.jpg';
  }

  /**
   * Verifica se o ícone é um arquivo de imagem
   */
  isImageFile(icon: string): boolean {
    if (!icon) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.svg'];
    return imageExtensions.some(ext => icon.toLowerCase().endsWith(ext));
  }

  /**
   * Manipula erro de carregamento de imagem
   */
  onImageError(event: any): void {
    event.target.src = 'assets/img/services/service-1-600-438.jpg';
  }

  // Dados para o Featured Title
  pageTitle = 'Nossos Serviços';
  breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'Nossos Serviços', isActive: true }
  ];

  // Dados para os serviços principais
  mainServices = [
    {
      image: 'assets/img/services/service-4-600-394.jpg',
      title: 'CAUTELAR VEICULAR',
      description: 'A análise cautelar é um procedimento detalhado que verifica a procedência do veículo. Avaliamos o histórico de sinistros, leilões, roubos/furtos, alienações, adulterações em chassis e motores, entre outros pontos cruciais.'
    },
    {
      image: 'assets/img/services/service-5-600-394.jpg',
      title: 'VISTORIA VEICULAR',
      description: 'A vistoria é uma inspeção visual e técnica do estado físico do veículo. Verificamos a originalidade de peças, nível de conservação, pinturas, soldas, e integridade estrutural. É um serviço indispensável para avaliar o valor justo e a segurança do carro.'
    }
  ];

  // Dados para as abas
  tabItems: TabItem[] = [
    {
      title: 'TRANSPARÊNCIA',
      content: `
        <p>Entendemos como é difícil encontrar profissionais confiáveis para análise veicular. Nossa missão é oferecer total transparência em todos os nossos serviços, garantindo que você tenha todas as informações necessárias para tomar a melhor decisão.</p>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Relatórios detalhados e de fácil compreensão</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Acesso completo ao histórico documental do veículo</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Comunicação clara sobre todos os achados</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Sem custos ocultos ou surpresas</span>
            </span>
          </div>
        </div>
      `,
      isActive: true
    },
    {
      title: 'EXPERIÊNCIA',
      content: `
        <p>Com anos de experiência no mercado automotivo, nossa equipe possui o conhecimento técnico necessário para identificar problemas que outros podem não perceber. Investimos constantemente em capacitação e atualização profissional.</p>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Profissionais certificados e especializados</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Conhecimento em todas as marcas e modelos</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Atualização constante com novas tecnologias</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Histórico comprovado de satisfação dos clientes</span>
            </span>
          </div>
        </div>
      `,
      isActive: false
    },
    {
      title: 'TECNOLOGIA',
      content: `
        <p>Utilizamos as mais modernas ferramentas e tecnologias disponíveis no mercado para garantir a precisão e confiabilidade de nossas análises. Nossos equipamentos são constantemente atualizados para oferecer os melhores resultados.</p>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Equipamentos de última geração para análise</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Acesso a bases de dados atualizadas</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Relatórios digitais com fotos e evidências</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Sistemas integrados para máxima precisão</span>
            </span>
          </div>
        </div>
      `,
      isActive: false
    },
    {
      title: 'CONFIABILIDADE',
      content: `
        <h5>Confie em Nossa Expertise</h5>
        <p>Sabemos como é importante ter a certeza de que está fazendo um bom negócio. Por isso, oferecemos serviços completos e confiáveis, com garantia de qualidade em todas as nossas análises. Nossa reputação foi construída com base na confiança dos nossos clientes.</p>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Análise completa: cautelar e vistoria em um só lugar</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Garantia de qualidade em todos os serviços</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Atendimento personalizado e suporte contínuo</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-1">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check"></i></span>
              <span class="text font-size-15">Compromisso com a satisfação do cliente</span>
            </span>
          </div>
        </div>
      `,
      isActive: false
    }
  ];

  // Dados para os serviços detalhados
  detailedServices = [
    {
      icon: 'as-icon-air-filter2',
      title: 'AUTOMOTIVE FILTERS',
      description: 'We provide top-notch service for import and domestic car repairs. Brakes Repair, Engine Repairs, Electrical Systems.'
    },
    {
      icon: 'as-icon-battery3',
      title: 'BATTERIES SERVICES',
      description: 'We provide top-notch service for import and domestic car repairs. Brakes Repair, Engine Repairs, Electrical Systems.'
    },
    {
      icon: 'as-icon-brake-1',
      title: 'BRAKE FLUID EXCHANGE',
      description: 'We provide top-notch service for import and domestic car repairs. Brakes Repair, Engine Repairs, Electrical Systems.'
    },
    {
      icon: 'as-icon-chassis2',
      title: 'WHEEL ALIGNMENT',
      description: 'We provide top-notch service for import and domestic car repairs. Brakes Repair, Engine Repairs, Electrical Systems.'
    },
    {
      icon: 'as-icon-car-lights',
      title: 'CHECK ENGINE LIGHT',
      description: 'We provide top-notch service for import and domestic car repairs. Brakes Repair, Engine Repairs, Electrical Systems.'
    },
    {
      icon: 'as-icon-speedometer2',
      title: 'CAR TURN-UPS',
      description: 'We provide top-notch service for import and domestic car repairs. Brakes Repair, Engine Repairs, Electrical Systems.'
    }
  ];

  // Dados para os pacotes de preços
  pricePackages = [
    {
      name: 'SILVER PACKAGE',
      price: '$98.88*',
      features: [
        { name: 'Conventional Oil Change', included: true },
        { name: 'Fuel System Cleaning', included: true },
        { name: 'Coolant Exchange', included: true },
        { name: 'Transmission Fluid Service', included: true },
        { name: 'Battery Check & Tire Rotation', included: false },
        { name: 'Visual Brake Inspection', included: false }
      ],
      buttonClass: 'wprt-button outline ol-accent',
      buttonText: 'CONTACT US',
      isPopular: false
    },
    {
      name: 'PLATINUM PACKAGE',
      price: '$244.87*',
      features: [
        { name: 'Conventional Oil Change', included: true },
        { name: 'Fuel System Cleaning', included: true },
        { name: 'Coolant Exchange', included: true },
        { name: 'Transmission Fluid Service', included: true },
        { name: 'Battery Check & Tire Rotation', included: true },
        { name: 'Visual Brake Inspection', included: true },
        { name: 'Performance Snapshot Evaluation', included: true }
      ],
      buttonClass: 'wprt-button accent',
      buttonText: 'CONTACT US',
      isPopular: true
    },
    {
      name: 'GOLD PACKAGE',
      price: '$163.98*',
      features: [
        { name: 'Conventional Oil Change', included: true },
        { name: 'Fuel System Cleaning', included: true },
        { name: 'Coolant Exchange', included: true },
        { name: 'Transmission Fluid Service', included: true },
        { name: 'Battery Check & Tire Rotation', included: true },
        { name: 'Visual Brake Inspection', included: false }
      ],
      buttonClass: 'wprt-button outline ol-accent',
      buttonText: 'CONTACT US',
      isPopular: false
    }
  ];
}
