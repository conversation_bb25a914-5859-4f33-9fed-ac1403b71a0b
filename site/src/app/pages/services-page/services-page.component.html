<!-- Header -->
<app-header headerType="auto"></app-header>

<!-- Featured Title -->
<app-featured-title
  [title]="pageTitle"
  [breadcrumbs]="breadcrumbs"
  alignment="left">
</app-featured-title>

<!-- Main Content -->
<div id="main-content" class="site-main clearfix">
  <div id="content-wrap">
    <div id="site-content" class="site-content clearfix">
      <div id="inner-content" class="inner-content-wrap">
        <div class="page-content">

          <!-- SERVICES -->
          <div class="row-services">
            <div class="container pt-80">
              <div class="row">
                <div class="col-md-12">
                  <div class="wprt-headings style-1 clearfix text-center mb-50">
                    <h2 class="heading clearfix">NOSSOS SERVIÇOS</h2>
                    <div class="sep clearfix"></div>
                    <p class="sub-heading clearfix">Realizamos vistorias e análises cautelares completas para garantir que você saiba tudo sobre o veículo antes de comprar. Transparência e confiança em cada etapa do processo.</p>
                  </div><!-- /.wprt-headings -->
                </div><!-- /.col-md-12 -->

              </div><!-- /.row -->

              <div class="row d-flex justify-content-center">
                <div class="col-lg-5 col-md-6 col-sm-8 mb-30" *ngFor="let service of mainServices">
                  <div class="wprt-image-box style-1 clearfix text-center">
                    <div class="item">
                      <div class="inner">
                        <div class="thumb">
                          <img [src]="service.image" alt="Image">
                        </div>

                        <div class="text-wrap">
                          <h3 class="title">
                            <a target="_blank" href="#">{{ service.title }}</a>
                          </h3>

                          <div class="desc">{{ service.description }}</div>

                          <div class="elm-btn">
                            <a class="small wprt-button accent" href="#" (click)="openServiceModal($event)">SOLICITE AGORA</a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div><!-- /.wprt-image-box -->

                </div><!-- /.col-md-4 -->
              </div><!-- /.row -->
            </div><!-- /.container -->
            <div class="pb-90"></div>
          </div>
          <!-- END SERVICES -->

          <!-- SERVICES WITH TABS -->
          <div class="row-services bg-light-grey">
            <div class="container pt-100">
              <div class="row">
                <div class="col-md-7">
                  <div class="wprt-headings style-1 clearfix mb-30">
                    <h2 class="heading clearfix">CONFIE EM NOSSOS SERVIÇOS</h2>
                    <div class="sep clearfix"></div>
                  </div><!-- /.wprt-headings -->

                  <app-tabs
                    [items]="tabItems"
                    [style]="'style-1'">
                  </app-tabs>
                </div><!-- /.col-md-7 -->

                <div class="col-md-5 pt-20">
                  <img src="assets/img/technicial.png" alt="Image" />
                </div><!-- /.col-md-5 -->
              </div><!-- /.row -->
            </div><!-- /.container -->
          </div>
          <!-- END SERVICES WITH TABS -->

          <!-- DIFFERENCE SECTION -->
          <div class="row-services">
            <div class="container pt-80">
              <div class="row">
                <div class="col-md-12">
                  <div class="wprt-headings style-1 clearfix text-center mb-50">
                    <h2 class="heading clearfix">DIFERENÇA ENTRE CAUTELAR E VISTORIA</h2>
                    <div class="sep clearfix"></div>
                  </div><!-- /.wprt-headings -->
                </div><!-- /.col-md-12 -->

                <div class="col-md-8 col-md-offset-2">
                  <div class="wprt-content-box style-1 clearfix">
                    <div class="inner">
                      <p class="text-center font-size-16 line-height-26">
                        Enquanto a <strong>vistoria</strong> foca na parte física e estrutural do veículo,
                        a <strong>cautelar</strong> vai além, analisando toda a parte documental, histórica e legal do carro.
                        Ambas são complementares e garantem uma visão completa do bem que será adquirido.
                      </p>
                      <div class="wprt-spacer clearfix" data-desktop="30" data-mobi="30" data-smobi="30"></div>
                      <div class="text-center">
                        <p class="font-size-15 color-accent">
                          <strong>O relatório cautelar é essencial para quem deseja fazer um bom negócio e evitar surpresas.</strong>
                        </p>
                      </div>
                    </div>
                  </div><!-- /.wprt-content-box -->
                </div><!-- /.col-md-8 -->
              </div><!-- /.row -->
            </div><!-- /.container -->
            <div class="pb-80"></div>
          </div>
          <!-- END DIFFERENCE SECTION -->

          <!-- DYNAMIC SERVICES FROM API -->
          <div class="row-services bg-light-grey">
            <div class="container pt-80">
              <div class="row">
                <div class="col-md-12">
                  <div class="wprt-headings style-1 clearfix text-center mb-50">
                    <h2 class="heading clearfix">NOSSOS SERVIÇOS E PREÇOS</h2>
                    <div class="sep clearfix"></div>
                    <p class="sub-heading clearfix">Confira nossa lista completa de serviços com preços transparentes</p>
                  </div><!-- /.wprt-headings -->
                </div><!-- /.col-md-12 -->
              </div><!-- /.row -->

              <!-- Loading State -->
              <div class="row" *ngIf="loading">
                <div class="col-md-12 text-center">
                  <div class="wprt-spacer clearfix" data-desktop="50" data-mobi="50" data-smobi="50"></div>
                  <p class="font-size-16">Carregando serviços...</p>
                  <div class="wprt-spacer clearfix" data-desktop="50" data-mobi="50" data-smobi="50"></div>
                </div>
              </div>

              <!-- Error State -->
              <div class="row" *ngIf="error && !loading">
                <div class="col-md-12 text-center">
                  <div class="wprt-spacer clearfix" data-desktop="50" data-mobi="50" data-smobi="50"></div>
                  <p class="font-size-16 color-red">{{ error }}</p>
                  <div class="wprt-spacer clearfix" data-desktop="50" data-mobi="50" data-smobi="50"></div>
                </div>
              </div>

              <!-- Services List -->
              <div class="row" *ngIf="!loading && !error && services.length > 0">
                <div class="col-lg-4 col-md-6 col-sm-12 mb-40" *ngFor="let service of services">
                  <div class="service-card-modern">
                    <div class="service-image-container">
                      <img
                        [src]="getServiceImage(service)"
                        [alt]="service.name"
                        class="service-image"
                        (error)="onImageError($event)">
                      <div class="service-overlay">
                        <div class="service-icon">
                          <i class="fa fa-wrench"></i>
                        </div>
                      </div>
                    </div>
                    <div class="service-content">
                      <h3 class="service-title">{{ service.name }}</h3>
                      <p class="service-description">{{ service.description }}</p>
                      <div class="service-footer">
                        <div class="service-price">
                          <span class="price-label">A partir de</span>
                          <span class="price-value">R$ {{ service.valueAmount | number:'1.2-2' }}</span>
                        </div>
                        <div class="service-action">
                          <button
                            class="btn-solicitar"
                            (click)="openServiceModal($event)"
                            type="button">
                            <i class="fa fa-calendar-check-o"></i>
                            SOLICITAR
                          </button>
                        </div>
                      </div>
                    </div>
                  </div><!-- /.service-card-modern -->
                </div><!-- /.col -->
              </div><!-- /.row -->

              <!-- No Services State -->
              <div class="row" *ngIf="!loading && !error && services.length === 0">
                <div class="col-md-12 text-center">
                  <div class="wprt-spacer clearfix" data-desktop="50" data-mobi="50" data-smobi="50"></div>
                  <p class="font-size-16">Nenhum serviço disponível no momento.</p>
                  <div class="wprt-spacer clearfix" data-desktop="50" data-mobi="50" data-smobi="50"></div>
                </div>
              </div>

            </div><!-- /.container -->
            <div class="pb-80"></div>
          </div>
          <!-- END DYNAMIC SERVICES FROM API -->

          <!-- PROMOTION BANNER -->
          <app-promotion-banner
            title="Oferecemos garantia e qualidade em todos os nossos serviços"
            buttonText="SOLICITE AGORA"
            buttonAction="service-request">
          </app-promotion-banner>
          <!-- END PROMOTION BANNER -->

        </div><!-- /.page-content -->
      </div><!-- /#inner-content -->
    </div><!-- /#site-content -->
  </div><!-- /#content-wrap -->
</div><!-- /#main-content -->

<!-- Footer -->
<app-footer></app-footer>
