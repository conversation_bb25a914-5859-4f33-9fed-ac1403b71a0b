/* Clean Service Cards */
.service-card-modern {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #f1f3f4;
}

.service-card-modern:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #ffc107;
}

/* Service Image */
.service-image-container {
  position: relative;
  height: 220px;
  overflow: hidden;
  background: #f8f9fa;
}

.service-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card-modern:hover .service-image {
  transform: scale(1.05);
}

.service-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 193, 7, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-card-modern:hover .service-overlay {
  opacity: 1;
}

.service-icon {
  color: white;
  font-size: 2.5rem;
}

/* Service Content */
.service-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 15px;
  line-height: 1.3;
}

.service-description {
  color: #4a5568;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

/* Service Footer */
.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid #e2e8f0;
}

.service-price {
  display: flex;
  flex-direction: column;
}

.price-label {
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 4px;
  font-weight: 500;
}

.price-value {
  font-size: 1.6rem;
  font-weight: 700;
  color: #28a745;
}

/* Clean Button */
.btn-solicitar {
  background: #ffc107;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-solicitar:hover {
  background: #e0a800;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
  color: white;
  text-decoration: none;
}

.btn-solicitar i {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-card-modern {
    margin-bottom: 30px;
  }

  .service-image-container {
    height: 180px;
  }

  .service-content {
    padding: 20px;
  }

  .service-title {
    font-size: 1.2rem;
  }

  .service-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .btn-solicitar {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .service-image-container {
    height: 160px;
  }

  .service-content {
    padding: 15px;
  }

  .price-value {
    font-size: 1.3rem;
  }
}

/* Clean Main Service Cards (wprt-image-box) */
.wprt-image-box.style-1 {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #f1f3f4;
}

.wprt-image-box.style-1:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #ffc107;
}

.wprt-image-box .item .inner .thumb {
  height: 220px;
  overflow: hidden;
  position: relative;
}

.wprt-image-box .item .inner .thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.wprt-image-box.style-1:hover .thumb img {
  transform: scale(1.05);
}

.wprt-image-box .text-wrap {
  padding: 25px;
}

.wprt-image-box .text-wrap .title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 15px;
  line-height: 1.3;
}

.wprt-image-box .text-wrap .title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.wprt-image-box:hover .text-wrap .title a {
  color: #ffc107;
}

.wprt-image-box .text-wrap .desc {
  color: #4a5568;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

.wprt-image-box .elm-btn .wprt-button {
  background: #ffc107;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.wprt-image-box .elm-btn .wprt-button:hover {
  background: #e0a800;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
  color: white;
  text-decoration: none;
}

/* Responsive for Main Service Cards */
@media (max-width: 768px) {
  .wprt-image-box .item .inner .thumb {
    height: 200px;
  }

  .wprt-image-box .text-wrap {
    padding: 25px 20px;
  }

  .wprt-image-box .text-wrap .title {
    font-size: 1.3rem;
  }

  .wprt-image-box .elm-btn .wprt-button {
    padding: 12px 24px;
    font-size: 0.85rem;
  }
}