import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FeaturedTitleComponent, BreadcrumbItem } from '../../shared/featured-title/featured-title.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';

@Component({
  selector: 'app-image-box-page',
  imports: [
    CommonModule,
    HeaderComponent,
    FooterComponent,
    FeaturedTitleComponent,
    PromotionBannerComponent
  ],
  templateUrl: './image-box-page.component.html',
  styleUrl: './image-box-page.component.css'
})
export class ImageBoxPageComponent {

  // Dados para o Featured Title
  pageTitle = 'Our Clients & Partners';
  breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'Clients & Partners', isActive: true }
  ];

  // Dados dos clientes
  clients = [
    {
      name: 'BMW',
      logo: 'assets/img/clients/bmw-logo.png',
      description: 'Authorized BMW service partner'
    },
    {
      name: 'Mercedes-Benz',
      logo: 'assets/img/clients/mercedes-logo.png',
      description: 'Certified Mercedes-Benz technicians'
    },
    {
      name: 'Audi',
      logo: 'assets/img/clients/audi-logo.png',
      description: 'Audi specialist service center'
    },
    {
      name: 'Toyota',
      logo: 'assets/img/clients/toyota-logo.png',
      description: 'Toyota certified repair facility'
    },
    {
      name: 'Honda',
      logo: 'assets/img/clients/honda-logo.png',
      description: 'Honda authorized service provider'
    },
    {
      name: 'Ford',
      logo: 'assets/img/clients/ford-logo.png',
      description: 'Ford certified technicians'
    }
  ];

  // Dados dos parceiros
  partners = [
    {
      name: 'Bosch',
      logo: 'assets/img/partners/bosch-logo.png',
      description: 'Premium automotive parts supplier'
    },
    {
      name: 'Castrol',
      logo: 'assets/img/partners/castrol-logo.png',
      description: 'High-quality motor oils and lubricants'
    },
    {
      name: 'Michelin',
      logo: 'assets/img/partners/michelin-logo.png',
      description: 'Premium tire solutions'
    },
    {
      name: 'Brembo',
      logo: 'assets/img/partners/brembo-logo.png',
      description: 'High-performance brake systems'
    }
  ];
}
