<!-- Header -->
<app-header headerType="auto"></app-header>

<!-- Featured Title -->
<app-featured-title
  [title]="pageTitle"
  [breadcrumbs]="breadcrumbs"
  alignment="left">
</app-featured-title>

<!-- Main Content -->
<div id="main-content" class="site-main clearfix">
  <div id="content-wrap">
    <div id="site-content" class="site-content clearfix">
      <div id="inner-content" class="inner-content-wrap">
        <div class="page-content">

          <!-- SERVICE REQUEST FORM SECTION -->
          <div class="careers-form-section">
            <div class="container pt-80">
              <div class="row">
                <div class="col-md-12">

                  <!-- Page Title -->
                  <div class="text-center">
                    <h1 class="careers-main-title">SOLICITAÇÃO DE SERVIÇO</h1>
                    <p class="careers-subtitle mb-1">Preen<PERSON> as informações abaixo para solicitar nossos serviços de vistoria cautelar.</p>
                  </div>
                </div>
              </div>

              <div class="row">
                <!-- Left Column - Steps -->
                <div class="col-md-4">
                  <div class="careers-steps">
                    <h3 class="steps-title">PREENCHA AS INFORMAÇÕES</h3>

                    <div class="step-item" [class.active]="currentStep === 1" [class.completed]="currentStep > 1">
                      <div class="step-number">1</div>
                      <div class="step-content">
                        <h4>Informações do Cliente</h4>
                      </div>
                    </div>

                    <div class="step-item" [class.active]="currentStep === 2" [class.completed]="currentStep > 2">
                      <div class="step-number">2</div>
                      <div class="step-content">
                        <h4>Informações do Veículo</h4>
                      </div>
                    </div>

                    <div class="step-item" [class.active]="currentStep === 3">
                      <div class="step-number">3</div>
                      <div class="step-content">
                        <h4>Pagamento</h4>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Right Column - Form -->
                <div class="col-md-8">
                  <div class="careers-form-container">

                    <!-- Etapa 1: Informações do Cliente -->
                    <div *ngIf="currentStep === 1" class="step-content">
                      <form [formGroup]="clientForm">
                        <!-- Contact Information Section -->
                        <div class="form-section">
                          <h3 class="section-title">INFORMAÇÕES DO CLIENTE</h3>

                        <!-- Tipo de Pessoa -->
                        <div class="form-group mb-3">
                          <label class="form-label">Tipo Pessoa:</label>
                          <div class="radio-group">
                            <label class="radio-option">
                              <input type="radio" formControlName="clientType" value="fisica">
                              <span class="radio-custom"></span>
                              Pessoa Física
                            </label>
                            <label class="radio-option">
                              <input type="radio" formControlName="clientType" value="juridica">
                              <span class="radio-custom"></span>
                              Pessoa Jurídica
                            </label>
                          </div>
                        </div>

                        <!-- Campos para Pessoa Física -->
                        <div *ngIf="!isClientTypePessoaJuridica()">
                          <!-- Linha 1: Nome Completo, CPF -->
                          <div class="row">
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="nomeCompleto">Nome Completo</label>
                                <input type="text" id="nomeCompleto" class="form-control" formControlName="nomeCompleto"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'nomeCompleto')">
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="cpf">CPF</label>
                                <input type="text" id="cpf" class="form-control" formControlName="cpf"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'cpf')">
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Campos para Pessoa Jurídica -->
                        <div *ngIf="isClientTypePessoaJuridica()">
                          <!-- Linha 1: Nome Fantasia, Razão Social -->
                          <div class="row">
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="nomeFantasia">Nome Fantasia</label>
                                <input type="text" id="nomeFantasia" class="form-control" formControlName="nomeFantasia"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'nomeFantasia')">
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="razaoSocial">Razão Social</label>
                                <input type="text" id="razaoSocial" class="form-control" formControlName="razaoSocial"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'razaoSocial')">
                              </div>
                            </div>
                          </div>

                          <!-- Linha 2: CNPJ, Inscrição Estadual -->
                          <div class="row">
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="cnpj">CNPJ</label>
                                <input type="text" id="cnpj" class="form-control" formControlName="cnpj"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'cnpj')">
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="inscricaoEstadual">Inscrição Estadual</label>
                                <input type="text" id="inscricaoEstadual" class="form-control" formControlName="inscricaoEstadual"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'inscricaoEstadual')">
                              </div>
                            </div>
                          </div>

                          <!-- Linha 3: Nome do Responsável, CPF -->
                          <div class="row">
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="nomeCompleto">Nome do Responsável</label>
                                <input type="text" id="nomeCompleto" class="form-control" formControlName="nomeCompleto"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'nomeCompleto')">
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="cpf">CPF do Responsável</label>
                                <input type="text" id="cpf" class="form-control" formControlName="cpf"
                                  [class.is-invalid]="isFieldInvalid(clientForm, 'cpf')">
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Linha: E-mail, Celular -->
                        <div class="row">
                          <div class="col-md-6">
                            <div class="form-group">
                              <label for="email">E-mail</label>
                              <input type="email" id="email" class="form-control" formControlName="email"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'email')">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="form-group">
                              <label for="celular">Celular</label>
                              <input type="text" id="celular" class="form-control" formControlName="celular"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'celular')">
                            </div>
                          </div>
                        </div>

                        <!-- Linha: CEP, Endereço -->
                        <div class="row">
                          <div class="col-md-3">
                            <div class="form-group">
                              <label for="cep">CEP</label>
                              <input type="text" id="cep" class="form-control" formControlName="cep"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'cep')">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="form-group">
                              <label for="endereco">Endereço</label>
                              <input type="text" id="endereco" class="form-control" formControlName="endereco"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'endereco')">
                            </div>
                          </div>
                          <div class="col-md-3">
                            <div class="form-group">
                              <label for="numero">Número</label>
                              <input type="text" id="numero" class="form-control" formControlName="numero"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'numero')">
                            </div>
                          </div>
                        </div>

                        <!-- Linha: Complemento, Bairro -->
                        <div class="row">
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="complemento">Complemento</label>
                              <input type="text" id="complemento" class="form-control" formControlName="complemento">
                            </div>
                          </div>
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="bairro">Bairro</label>
                              <input type="text" id="bairro" class="form-control" formControlName="bairro"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'bairro')">
                            </div>
                          </div>
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="cidade">Cidade</label>
                              <input type="text" id="cidade" class="form-control" formControlName="cidade"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'cidade')">
                            </div>
                          </div>
                        </div>

                        <!-- Linha: Estado -->
                        <div class="row">
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="estado">Estado</label>
                              <select id="estado" class="form-control" formControlName="estado"
                                [class.is-invalid]="isFieldInvalid(clientForm, 'estado')">
                                <option value="">Selecione o estado</option>
                                <option *ngFor="let estado of estados" [value]="estado.sigla">
                                  {{ estado.nome }}
                                </option>
                              </select>
                            </div>
                          </div>
                          <div class="col-md-8" style="display: flex; justify-content: center;">
                            <div class="form-group mb-0">
                              <div class="checkbox-group">
                                <label class="checkbox-option">
                                  <input type="checkbox" formControlName="criarUsuario">
                                  <span class="checkbox-custom"></span>
                                  Desejo criar um usuário para acompanhar minhas solicitações
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>

                          <!-- Submit Button -->
                          <div class="form-submit">
                            <button type="button" class="submit-btn" (click)="nextStep()" [disabled]="!clientForm.valid">
                              PRÓXIMO
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>

                    <!-- Etapa 2: Informações do Veículo -->
                    <div *ngIf="currentStep === 2" class="step-content">
                      <form [formGroup]="vehicleForm">
                        <!-- Vehicle Information Section -->
                        <div class="form-section">
                          <h3 class="section-title">INFORMAÇÕES DO VEÍCULO</h3>

                        <!-- Linha 1: Placa, Marca, Modelo -->
                        <div class="row">
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="placa">Placa</label>
                              <input type="text" id="placa" class="form-control" formControlName="placa"
                                [class.is-invalid]="isFieldInvalid(vehicleForm, 'placa')">
                            </div>
                          </div>
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="marca">Marca</label>
                              <input type="text" id="marca" class="form-control" formControlName="marca"
                                [class.is-invalid]="isFieldInvalid(vehicleForm, 'marca')">
                            </div>
                          </div>
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="modelo">Modelo</label>
                              <input type="text" id="modelo" class="form-control" formControlName="modelo"
                                [class.is-invalid]="isFieldInvalid(vehicleForm, 'modelo')">
                            </div>
                          </div>
                        </div>

                        <!-- Linha 2: Cor -->
                        <div class="row">
                          <div class="col-md-4">
                            <div class="form-group">
                              <label for="cor">Cor</label>
                              <input type="text" id="cor" class="form-control" formControlName="cor"
                                [class.is-invalid]="isFieldInvalid(vehicleForm, 'cor')">
                            </div>
                          </div>
                        </div>

                        <!-- Linha 3: Nome do Proprietário, Celular do Proprietário -->
                        <div class="row">
                          <div class="col-md-6">
                            <div class="form-group">
                              <label for="nomeProprietario">Nome do Proprietário</label>
                              <input type="text" id="nomeProprietario" class="form-control" formControlName="nomeProprietario"
                                [class.is-invalid]="isFieldInvalid(vehicleForm, 'nomeProprietario')">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="form-group">
                              <label for="celularProprietario">Celular do Proprietário</label>
                              <input type="text" id="celularProprietario" class="form-control" formControlName="celularProprietario"
                                [class.is-invalid]="isFieldInvalid(vehicleForm, 'celularProprietario')">
                            </div>
                          </div>
                        </div>

                        <!-- Endereço do Veículo -->
                        <div class="form-group mb-3">
                          <label class="form-label">Endereço do Veículo:</label>
                          <div class="radio-group">
                            <label class="radio-option">
                              <input type="radio" formControlName="enderecoVeiculo" value="proprio">
                              <span class="radio-custom"></span>
                              Mesmo endereço do cliente
                            </label>
                            <label class="radio-option">
                              <input type="radio" formControlName="enderecoVeiculo" value="outro">
                              <span class="radio-custom"></span>
                              Outro endereço
                            </label>
                          </div>
                        </div>

                        <!-- Campos de Endereço Alternativo (se selecionado "outro endereço") -->
                        <div *ngIf="isVehicleAddressOther()">
                          <!-- Linha: CEP, Endereço -->
                          <div class="row">
                            <div class="col-md-3">
                              <div class="form-group">
                                <label for="cepVeiculo">CEP</label>
                                <input type="text" id="cepVeiculo" class="form-control" formControlName="cepVeiculo"
                                  [class.is-invalid]="isFieldInvalid(vehicleForm, 'cepVeiculo')">
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="form-group">
                                <label for="enderecoVeiculoCompleto">Endereço</label>
                                <input type="text" id="enderecoVeiculoCompleto" class="form-control" formControlName="enderecoVeiculoCompleto"
                                  [class.is-invalid]="isFieldInvalid(vehicleForm, 'enderecoVeiculoCompleto')">
                              </div>
                            </div>
                            <div class="col-md-3">
                              <div class="form-group">
                                <label for="numeroVeiculo">Número</label>
                                <input type="text" id="numeroVeiculo" class="form-control" formControlName="numeroVeiculo"
                                  [class.is-invalid]="isFieldInvalid(vehicleForm, 'numeroVeiculo')">
                              </div>
                            </div>
                          </div>

                          <!-- Linha: Complemento, Bairro -->
                          <div class="row">
                            <div class="col-md-4">
                              <div class="form-group">
                                <label for="complementoVeiculo">Complemento</label>
                                <input type="text" id="complementoVeiculo" class="form-control" formControlName="complementoVeiculo">
                              </div>
                            </div>
                            <div class="col-md-4">
                              <div class="form-group">
                                <label for="bairroVeiculo">Bairro</label>
                                <input type="text" id="bairroVeiculo" class="form-control" formControlName="bairroVeiculo"
                                  [class.is-invalid]="isFieldInvalid(vehicleForm, 'bairroVeiculo')">
                              </div>
                            </div>
                            <div class="col-md-4">
                              <div class="form-group">
                                <label for="cidadeVeiculo">Cidade</label>
                                <input type="text" id="cidadeVeiculo" class="form-control" formControlName="cidadeVeiculo"
                                  [class.is-invalid]="isFieldInvalid(vehicleForm, 'cidadeVeiculo')">
                              </div>
                            </div>
                          </div>

                          <!-- Linha: Estado -->
                          <div class="row">
                            <div class="col-md-4">
                              <div class="form-group">
                                <label for="estadoVeiculo">Estado</label>
                                <select id="estadoVeiculo" class="form-control" formControlName="estadoVeiculo"
                                  [class.is-invalid]="isFieldInvalid(vehicleForm, 'estadoVeiculo')">
                                  <option value="">Selecione o estado</option>
                                  <option *ngFor="let estado of estados" [value]="estado.sigla">
                                    {{ estado.nome }}
                                  </option>
                                </select>
                              </div>
                            </div>
                          </div>
                        </div>

                          <!-- Submit Button -->
                          <div class="form-submit">
                            <button type="button" class="submit-btn secondary" (click)="previousStep()">
                              ANTERIOR
                            </button>
                            <button type="button" class="submit-btn" (click)="nextStep()" [disabled]="!vehicleForm.valid">
                              PRÓXIMO
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>

                    <!-- Etapa 3: Pagamento -->
                    <div *ngIf="currentStep === 3" class="step-content">
                      <form [formGroup]="paymentForm">
                        <!-- Payment Information Section -->
                        <div class="form-section">
                          <h3 class="section-title">PAGAMENTO</h3>

                        <!-- Método de Pagamento -->
                        <div class="form-group">
                          <label class="form-label">Método de Pagamento</label>
                          <div class="radio-group">
                            <label class="radio-option">
                              <input type="radio" formControlName="metodoPagamento" value="debito">
                              <span class="radio-custom"></span>
                              Débito
                            </label>
                            <label class="radio-option">
                              <input type="radio" formControlName="metodoPagamento" value="credito">
                              <span class="radio-custom"></span>
                              Crédito
                            </label>
                            <label class="radio-option">
                              <input type="radio" formControlName="metodoPagamento" value="pix">
                              <span class="radio-custom"></span>
                              PIX
                            </label>
                          </div>
                        </div>

                        <!-- Tipo de Serviço -->
                        <div class="form-group">
                          <label class="form-label">Tipo de Serviço:</label>
                          <div class="radio-group">
                            <div class="service-item">
                              <label class="radio-option">
                                <input type="radio" formControlName="tipoServico" value="cautelar">
                                <span class="radio-custom"></span>
                                Cautelar Veículo
                              </label>
                              <div *ngIf="showCautelarDescription" class="service-description">
                                {{ serviceDescriptions.cautelarVeiculo }}
                              </div>
                            </div>
                            <div class="service-item">
                              <label class="radio-option">
                                <input type="radio" formControlName="tipoServico" value="vistoria">
                                <span class="radio-custom"></span>
                                Vistoria
                              </label>
                              <div *ngIf="showVistoriaDescription" class="service-description">
                                {{ serviceDescriptions.vistoria }}
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Observações -->
                        <div class="form-group">
                          <label for="observacoes">Observações</label>
                          <textarea id="observacoes" class="form-control" rows="4" formControlName="observacoes"
                            placeholder="Informações adicionais sobre a solicitação..."></textarea>
                        </div>

                          <!-- Submit Button -->
                          <div class="form-submit">
                            <button type="button" class="submit-btn secondary" (click)="previousStep()">
                              ANTERIOR
                            </button>
                            <button type="submit" class="submit-btn" (click)="onSubmit()"
                              [disabled]="!paymentForm.valid || isSubmitting">
                              <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
                              {{ isSubmitting ? 'ENVIANDO...' : 'ENVIAR' }}
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>

                  </div>
                </div>
              </div>

            </div>
            <div class="pb-70"></div>
          </div>
          <!-- END SERVICE REQUEST FORM SECTION -->

        </div><!-- /.page-content -->
      </div><!-- /#inner-content -->
    </div><!-- /#site-content -->
  </div><!-- /#content-wrap -->
</div><!-- /#main-content -->

<!-- Footer -->
<app-footer></app-footer>
