import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl, ValidationErrors } from '@angular/forms';
import { Router } from '@angular/router';
import { HeaderComponent } from '../../shared/header/header.component';
import { FeaturedTitleComponent } from '../../shared/featured-title/featured-title.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { ServiceRequestService } from '../../shared/services/service-request.service';
import { ToastService } from '../../shared/toast/toast.service';

// Validadores customizados
export class CustomValidators {
  static cpf(control: AbstractControl): ValidationErrors | null {
    const cpf = control.value?.replace(/\D/g, '');
    if (!cpf || cpf.length !== 11) {
      return { cpf: true };
    }

    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1{10}$/.test(cpf)) {
      return { cpf: true };
    }

    // Validação dos dígitos verificadores
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i);
    }
    let remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(9))) return { cpf: true };

    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i);
    }
    remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(10))) return { cpf: true };

    return null;
  }

  static cep(control: AbstractControl): ValidationErrors | null {
    const cep = control.value?.replace(/\D/g, '');
    if (!cep || cep.length !== 8) {
      return { cep: true };
    }
    return null;
  }

  static placa(control: AbstractControl): ValidationErrors | null {
    const placa = control.value?.replace(/[^A-Za-z0-9]/g, '');
    if (!placa || (placa.length !== 7 && placa.length !== 8)) {
      return { placa: true };
    }
    return null;
  }

  static celular(control: AbstractControl): ValidationErrors | null {
    const celular = control.value?.replace(/\D/g, '');
    if (!celular || celular.length < 10 || celular.length > 11) {
      return { celular: true };
    }
    return null;
  }

  static cnpj(control: AbstractControl): ValidationErrors | null {
    const cnpj = control.value?.replace(/\D/g, '');
    if (!cnpj || cnpj.length !== 14) {
      return { cnpj: true };
    }

    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1{13}$/.test(cnpj)) {
      return { cnpj: true };
    }

    // Validação dos dígitos verificadores
    let tamanho = cnpj.length - 2;
    let numeros = cnpj.substring(0, tamanho);
    let digitos = cnpj.substring(tamanho);
    let soma = 0;
    let pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
      soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
      if (pos < 2) pos = 9;
    }

    let resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado !== parseInt(digitos.charAt(0))) return { cnpj: true };

    tamanho = tamanho + 1;
    numeros = cnpj.substring(0, tamanho);
    soma = 0;
    pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
      soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
      if (pos < 2) pos = 9;
    }

    resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado !== parseInt(digitos.charAt(1))) return { cnpj: true };

    return null;
  }
}

export interface BreadcrumbItem {
  label: string;
  url?: string;
  isActive?: boolean;
}

export interface ServiceRequestData {
  // Informações do Cliente
  clientType: 'fisica' | 'juridica';
  nomeCompleto: string;
  cpf: string;
  email: string;
  celular: string;
  cep: string;
  endereco: string;
  numero: string;
  complemento: string;
  bairro: string;
  cidade: string;
  estado: string;
  criarUsuario: boolean;

  // Campos específicos para Pessoa Jurídica
  nomeFantasia?: string;
  razaoSocial?: string;
  cnpj?: string;
  inscricaoEstadual?: string;

  // Informações do Veículo
  enderecoVeiculo: 'proprio' | 'outro';
  placa: string;
  marca: string;
  modelo: string;
  cor: string;
  nomeProprietario: string;
  celularProprietario: string;
  tipoServico: 'cautelar' | 'vistoria';

  cepVeiculo?: string;
  enderecoVeiculoCompleto?: string;
  numeroVeiculo?: string;
  complementoVeiculo?: string;
  bairroVeiculo?: string;
  cidadeVeiculo?: string;
  estadoVeiculo?: string;

  // Pagamento
  metodoPagamento: string;
  observacoes: string;
}

@Component({
  selector: 'app-service-request-page',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, HeaderComponent, FeaturedTitleComponent, FooterComponent],
  templateUrl: './service-request-page.component.html',
  styleUrls: ['./service-request-page.component.css']
})
export class ServiceRequestPageComponent implements OnInit, OnDestroy, AfterViewInit {
  currentStep: number = 1;
  totalSteps: number = 3;
  isSubmitting: boolean = false;

  clientForm!: FormGroup;
  vehicleForm!: FormGroup;
  paymentForm!: FormGroup;

  // Dados para o Featured Title
  pageTitle = 'Solicitação de Serviço';
  breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'Solicitação de Serviço', isActive: true }
  ];

  // Descrições dos serviços
  serviceDescriptions = {
    cautelarVeiculo: 'A análise cautelar é um procedimento detalhado que verifica a procedência do veículo. Avaliamos o histórico de sinistros, leilões, roubos/furtos, alienações, adulterações em chassis e motores, entre outros pontos cruciais.',
    vistoria: 'A vistoria é uma inspeção visual e técnica do estado físico do veículo. Verificamos a originalidade de peças, nível de conservação, pinturas, soldas, e integridade estrutural. É um serviço indispensável para avaliar o valor justo e a segurança do carro.'
  };

  // Controle de exibição das descrições
  showCautelarDescription: boolean = false;
  showVistoriaDescription: boolean = false;

  // Lista de estados brasileiros
  estados = [
    { sigla: 'AC', nome: 'Acre' },
    { sigla: 'AL', nome: 'Alagoas' },
    { sigla: 'AP', nome: 'Amapá' },
    { sigla: 'AM', nome: 'Amazonas' },
    { sigla: 'BA', nome: 'Bahia' },
    { sigla: 'CE', nome: 'Ceará' },
    { sigla: 'DF', nome: 'Distrito Federal' },
    { sigla: 'ES', nome: 'Espírito Santo' },
    { sigla: 'GO', nome: 'Goiás' },
    { sigla: 'MA', nome: 'Maranhão' },
    { sigla: 'MT', nome: 'Mato Grosso' },
    { sigla: 'MS', nome: 'Mato Grosso do Sul' },
    { sigla: 'MG', nome: 'Minas Gerais' },
    { sigla: 'PA', nome: 'Pará' },
    { sigla: 'PB', nome: 'Paraíba' },
    { sigla: 'PR', nome: 'Paraná' },
    { sigla: 'PE', nome: 'Pernambuco' },
    { sigla: 'PI', nome: 'Piauí' },
    { sigla: 'RJ', nome: 'Rio de Janeiro' },
    { sigla: 'RN', nome: 'Rio Grande do Norte' },
    { sigla: 'RS', nome: 'Rio Grande do Sul' },
    { sigla: 'RO', nome: 'Rondônia' },
    { sigla: 'RR', nome: 'Roraima' },
    { sigla: 'SC', nome: 'Santa Catarina' },
    { sigla: 'SP', nome: 'São Paulo' },
    { sigla: 'SE', nome: 'Sergipe' },
    { sigla: 'TO', nome: 'Tocantins' }
  ];

  constructor(
    private fb: FormBuilder,
    private serviceRequestService: ServiceRequestService,
    private router: Router,
    private toastService: ToastService
  ) {
    this.initializeForms();
  }

  ngOnInit() {
    this.setupFormWatchers();
  }

  ngAfterViewInit() {
    // Aplicar máscaras após a view estar inicializada
    setTimeout(() => {
      this.applyMasks();
    }, 100);
  }

  ngOnDestroy() {
    // Limpar event listeners se necessário
  }

  private initializeForms() {
    // Formulário de Informações do Cliente
    this.clientForm = this.fb.group({
      clientType: ['fisica', Validators.required],
      nomeCompleto: ['', [Validators.required, Validators.minLength(3)]],
      cpf: ['', [Validators.required, CustomValidators.cpf]],
      email: ['', [Validators.required, Validators.email]],
      celular: ['', [Validators.required, CustomValidators.celular]],
      cep: ['', [Validators.required, CustomValidators.cep]],
      endereco: ['', Validators.required],
      numero: ['', Validators.required],
      complemento: [''],
      bairro: ['', Validators.required],
      cidade: ['', Validators.required],
      estado: ['', Validators.required],
      criarUsuario: [true],

      // Campos específicos para Pessoa Jurídica
      nomeFantasia: [''],
      razaoSocial: [''],
      cnpj: [''],
      inscricaoEstadual: ['']
    });

    // Formulário de Informações do Veículo
    this.vehicleForm = this.fb.group({
      placa: ['', [Validators.required, CustomValidators.placa]],
      marca: ['', Validators.required],
      modelo: ['', Validators.required],
      cor: ['', Validators.required],
      nomeProprietario: ['', [Validators.required, Validators.minLength(3)]],
      celularProprietario: ['', [Validators.required, CustomValidators.celular]],
      enderecoVeiculo: ['proprio', Validators.required],
      cepVeiculo: [''],
      enderecoVeiculoCompleto: [''],
      numeroVeiculo: [''],
      complementoVeiculo: [''],
      bairroVeiculo: [''],
      cidadeVeiculo: [''],
      estadoVeiculo: ['']
    });

    // Formulário de Pagamento
    this.paymentForm = this.fb.group({
      metodoPagamento: ['', Validators.required],
      observacoes: [''],
      tipoServico: ['', Validators.required]
    });
  }

  private setupFormWatchers() {
    // Watcher para CEP - buscar endereço automaticamente
    this.clientForm.get('cep')?.valueChanges.subscribe(cep => {
      if (cep && cep.replace(/\D/g, '').length === 8) {
        this.buscarEnderecoPorCep(cep);
      }
    });

    // Watcher para CEP do veículo - buscar endereço automaticamente
    this.vehicleForm.get('cepVeiculo')?.valueChanges.subscribe(cep => {
      if (cep && cep.replace(/\D/g, '').length === 8) {
        this.buscarEnderecoPorCepVeiculo(cep);
      }
    });

    // Watcher para tipo de cliente - ajustar validações e aplicar máscaras
    this.clientForm.get('clientType')?.valueChanges.subscribe(type => {
      this.updateClientTypeValidations(type);

      // Aplicar máscaras nos campos que aparecem/desaparecem baseado no tipo
      setTimeout(() => {
        if (type === 'juridica') {
          // Aplicar máscaras específicas para pessoa jurídica
          this.applyMaskToField('cnpj', this.cnpjMask);
          this.applyMaskToField('inscricaoEstadual', this.inscricaoEstadualMask);
        }
        // CPF sempre está disponível para pessoa física
        this.applyMaskToField('cpf', this.cpfMask);
      }, 100);
    });

    // Watcher para endereço do veículo - ajustar validações
    this.vehicleForm.get('enderecoVeiculo')?.valueChanges.subscribe(tipo => {
      this.updateVehicleAddressValidations(tipo);
    });

    // Watchers para serviços - mostrar/ocultar descrições
    this.paymentForm.get('tipoServico')?.valueChanges.subscribe(tipo => {
      this.showCautelarDescription = tipo === 'cautelar';
      this.showVistoriaDescription = tipo === 'vistoria';
    });

    // Aplicar máscaras nos campos
    this.applyMasks();
  }

  private applyMasks() {
    // Implementar máscaras nativas para CPF, CEP, Celular, Placa, CNPJ
    // Usar múltiplas tentativas para garantir que os elementos estejam disponíveis
    this.tryApplyMasks(0);
  }

  private tryApplyMasks(attempt: number) {
    const maxAttempts = 5;
    const delay = 200 * (attempt + 1); // Aumentar delay progressivamente

    setTimeout(() => {
      const fieldsToApply = [
        { id: 'cpf', mask: this.cpfMask },
        { id: 'celular', mask: this.celularMask },
        { id: 'cep', mask: this.cepMask },
        { id: 'placa', mask: this.placaMask },
        { id: 'celularProprietario', mask: this.celularMask },
        { id: 'cepVeiculo', mask: this.cepMask }
      ];

      // Adicionar campos específicos de pessoa jurídica se necessário
      if (this.clientForm.get('clientType')?.value === 'juridica') {
        fieldsToApply.push(
          { id: 'cnpj', mask: this.cnpjMask },
          { id: 'inscricaoEstadual', mask: this.inscricaoEstadualMask }
        );
      }

      const fieldsApplied = fieldsToApply.map(field =>
        this.applyMaskToField(field.id, field.mask)
      );

      const successfullyApplied = fieldsApplied.filter(Boolean).length;

      // Se nem todos os campos foram encontrados e ainda temos tentativas, tentar novamente
      if (successfullyApplied < fieldsApplied.length && attempt < maxAttempts) {
        this.tryApplyMasks(attempt + 1);
      }
    }, delay);
  }

  private applyMaskToField(fieldId: string, maskFunction: (value: string) => string): boolean {
    const element = document.getElementById(fieldId) as HTMLInputElement;
    if (element) {
      // Remover listeners anteriores se existirem
      if ((element as any)._maskListener) {
        element.removeEventListener('input', (element as any)._maskListener);
      }

      // Criar novo listener
      const maskListener = (event: Event) => {
        const target = event.target as HTMLInputElement;
        const cursorPosition = target.selectionStart;
        const oldValue = target.value;
        const newValue = maskFunction(target.value);

        if (oldValue !== newValue) {
          target.value = newValue;

          // Calcular posição correta do cursor baseada nos dígitos digitados
          if (cursorPosition !== null) {
            const newPosition = this.calculateCursorPosition(oldValue, newValue, cursorPosition);
            target.setSelectionRange(newPosition, newPosition);
          }
        }
      };

      // Adicionar listener e salvar referência
      element.addEventListener('input', maskListener);
      (element as any)._maskListener = maskListener;

      return true;
    }
    return false;
  }

  /**
   * Calcula a posição correta do cursor após aplicar a máscara
   * Baseado na quantidade de dígitos antes da posição original do cursor
   */
  private calculateCursorPosition(oldValue: string, newValue: string, oldCursorPosition: number): number {
    // Contar quantos dígitos existem antes da posição do cursor no valor antigo
    const digitsBeforeCursor = oldValue.substring(0, oldCursorPosition).replace(/\D/g, '').length;

    // Encontrar a posição no novo valor onde temos a mesma quantidade de dígitos
    let digitCount = 0;
    let newPosition = 0;

    for (let i = 0; i < newValue.length; i++) {
      if (/\d/.test(newValue[i])) {
        digitCount++;
        if (digitCount > digitsBeforeCursor) {
          newPosition = i;
          break;
        }
      }
      newPosition = i + 1;
    }

    return Math.min(newPosition, newValue.length);
  }

  // Máscaras de formatação
  private cpfMask = (value: string): string => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d{1,2})/, '$1-$2')
      .replace(/(-\d{2})\d+?$/, '$1');
  };

  private cnpjMask = (value: string): string => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{2})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1/$2')
      .replace(/(\d{4})(\d{1,2})/, '$1-$2')
      .replace(/(-\d{2})\d+?$/, '$1');
  };

  private celularMask = (value: string): string => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{2})(\d)/, '($1) $2')
      .replace(/(\d{5})(\d)/, '$1-$2')
      .replace(/(-\d{4})\d+?$/, '$1');
  };

  private cepMask = (value: string): string => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{5})(\d)/, '$1-$2')
      .replace(/(-\d{3})\d+?$/, '$1');
  };

  private placaMask = (value: string): string => {
    return value
      .replace(/[^a-zA-Z0-9]/g, '')
      .replace(/(\w{3})(\w)/, '$1-$2')
      .replace(/(-\w{4})\w+?$/, '$1')
      .toUpperCase();
  };

  private inscricaoEstadualMask = (value: string): string => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d{1,2})/, '$1-$2')
      .replace(/(-\d{2})\d+?$/, '$1');
  };

  // Métodos de busca de CEP
  private async buscarEnderecoPorCep(cep: string) {
    try {
      const cepLimpo = cep.replace(/\D/g, '');
      const response = await fetch(`https://viacep.com.br/ws/${cepLimpo}/json/`);
      const data = await response.json();

      if (!data.erro) {
        this.clientForm.patchValue({
          endereco: data.logradouro,
          bairro: data.bairro,
          cidade: data.localidade,
          estado: data.uf
        });
      }
    } catch (error) {
      console.error('Erro ao buscar CEP:', error);
    }
  }

  private async buscarEnderecoPorCepVeiculo(cep: string) {
    try {
      const cepLimpo = cep.replace(/\D/g, '');
      const response = await fetch(`https://viacep.com.br/ws/${cepLimpo}/json/`);
      const data = await response.json();

      if (!data.erro) {
        this.vehicleForm.patchValue({
          enderecoVeiculoCompleto: data.logradouro,
          bairroVeiculo: data.bairro,
          cidadeVeiculo: data.localidade,
          estadoVeiculo: data.uf
        });
      }
    } catch (error) {
      console.error('Erro ao buscar CEP do veículo:', error);
    }
  }

  private updateClientTypeValidations(type: 'fisica' | 'juridica') {
    const cpfControl = this.clientForm.get('cpf');
    const nomeFantasiaControl = this.clientForm.get('nomeFantasia');
    const razaoSocialControl = this.clientForm.get('razaoSocial');
    const cnpjControl = this.clientForm.get('cnpj');
    const inscricaoEstadualControl = this.clientForm.get('inscricaoEstadual');

    if (type === 'juridica') {
      // Para Pessoa Jurídica - CNPJ é obrigatório, CPF não é
      cpfControl?.clearValidators();
      cnpjControl?.setValidators([Validators.required, CustomValidators.cnpj]);
      nomeFantasiaControl?.setValidators([Validators.required, Validators.minLength(2)]);
      razaoSocialControl?.setValidators([Validators.required, Validators.minLength(2)]);
      inscricaoEstadualControl?.setValidators([Validators.required]);

      // Limpar CPF quando mudar para jurídica
      cpfControl?.setValue('');
    } else {
      // Para Pessoa Física - CPF é obrigatório, campos de PJ não são
      cpfControl?.setValidators([Validators.required, CustomValidators.cpf]);
      cnpjControl?.clearValidators();
      nomeFantasiaControl?.clearValidators();
      razaoSocialControl?.clearValidators();
      inscricaoEstadualControl?.clearValidators();

      // Limpar campos de PJ quando mudar para física
      cnpjControl?.setValue('');
      nomeFantasiaControl?.setValue('');
      razaoSocialControl?.setValue('');
      inscricaoEstadualControl?.setValue('');
    }

    // Atualizar validações
    cpfControl?.updateValueAndValidity();
    cnpjControl?.updateValueAndValidity();
    nomeFantasiaControl?.updateValueAndValidity();
    razaoSocialControl?.updateValueAndValidity();
    inscricaoEstadualControl?.updateValueAndValidity();
  }

  private updateVehicleAddressValidations(tipo: 'proprio' | 'outro') {
    const cepVeiculoControl = this.vehicleForm.get('cepVeiculo');
    const enderecoVeiculoCompletoControl = this.vehicleForm.get('enderecoVeiculoCompleto');
    const numeroVeiculoControl = this.vehicleForm.get('numeroVeiculo');
    const bairroVeiculoControl = this.vehicleForm.get('bairroVeiculo');
    const cidadeVeiculoControl = this.vehicleForm.get('cidadeVeiculo');
    const estadoVeiculoControl = this.vehicleForm.get('estadoVeiculo');

    if (tipo === 'outro') {
      // Adicionar validações para endereço diferente
      cepVeiculoControl?.setValidators([Validators.required, CustomValidators.cep]);
      enderecoVeiculoCompletoControl?.setValidators([Validators.required]);
      numeroVeiculoControl?.setValidators([Validators.required]);
      bairroVeiculoControl?.setValidators([Validators.required]);
      cidadeVeiculoControl?.setValidators([Validators.required]);
      estadoVeiculoControl?.setValidators([Validators.required]);
    } else {
      // Remover validações para endereço próprio
      cepVeiculoControl?.clearValidators();
      enderecoVeiculoCompletoControl?.clearValidators();
      numeroVeiculoControl?.clearValidators();
      bairroVeiculoControl?.clearValidators();
      cidadeVeiculoControl?.clearValidators();
      estadoVeiculoControl?.clearValidators();

      // Limpar valores dos campos
      this.vehicleForm.patchValue({
        cepVeiculo: '',
        enderecoVeiculoCompleto: '',
        numeroVeiculo: '',
        complementoVeiculo: '',
        bairroVeiculo: '',
        cidadeVeiculo: '',
        estadoVeiculo: ''
      });
    }

    // Atualizar status de validação
    cepVeiculoControl?.updateValueAndValidity();
    enderecoVeiculoCompletoControl?.updateValueAndValidity();
    numeroVeiculoControl?.updateValueAndValidity();
    bairroVeiculoControl?.updateValueAndValidity();
    cidadeVeiculoControl?.updateValueAndValidity();
    estadoVeiculoControl?.updateValueAndValidity();
  }

  // Métodos de navegação entre steps
  nextStep() {
    if (this.validateCurrentStep()) {
      if (this.currentStep < this.totalSteps) {
        this.currentStep++;

        // Aplicar máscaras específicas quando entrar na etapa 2 (veículo)
        if (this.currentStep === 2) {
          setTimeout(() => {
            this.applyMaskToField('celularProprietario', this.celularMask);
            this.applyMaskToField('placa', this.placaMask);
            this.applyMaskToField('cepVeiculo', this.cepMask);
          }, 100);
        }
      }
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  goToStep(step: number) {
    if (step >= 1 && step <= this.totalSteps) {
      this.currentStep = step;
    }
  }

  getProgressPercentage(): number {
    return (this.currentStep / this.totalSteps) * 100;
  }

  // Validação de steps
  validateCurrentStep(): boolean {
    switch (this.currentStep) {
      case 1:
        if (this.clientForm.invalid) {
          this.markFormGroupTouched(this.clientForm);
          return false;
        }
        break;
      case 2:
        if (this.vehicleForm.invalid) {
          this.markFormGroupTouched(this.vehicleForm);
          return false;
        }
        break;
      case 3:
        if (this.paymentForm.invalid) {
          this.markFormGroupTouched(this.paymentForm);
          return false;
        }
        break;
    }
    return true;
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  isFieldInvalid(form: FormGroup, fieldName: string): boolean {
    const field = form.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  // Submissão do formulário
  async onSubmit() {
    if (this.validateCurrentStep() && !this.isSubmitting) {
      this.isSubmitting = true;

      try {
        const formData: ServiceRequestData = {
          ...this.clientForm.value,
          ...this.vehicleForm.value,
          ...this.paymentForm.value
        };

        // Enviar dados usando o serviço
        const response = await this.serviceRequestService.submitServiceRequest(formData).toPromise();

        if (response?.success) {
          // Aguardar um tick para garantir que o DOM está pronto
          setTimeout(() => {
            this.showSuccessMessage(response.message);
            this.resetFormAfterSuccess();
          }, 100);
        } else {
          throw new Error(response?.message || 'Erro desconhecido');
        }

      } catch (error: any) {
        this.showErrorMessage(error.message || 'Erro ao enviar solicitação');
      } finally {
        this.isSubmitting = false;
      }
    }
  }

  private showSuccessMessage(message: string) {
    this.toastService.success(
      message,
      '✅ Solicitação Enviada',
      {
        duration: 6000,
        actions: [
          {
            label: 'Nova Solicitação',
            action: () => {
              this.resetFormAfterSuccess();
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }
          }
        ]
      }
    );
  }

  private showErrorMessage(message: string) {
    this.toastService.error(
      message,
      'Erro na Solicitação',
      {
        duration: 0,
        actions: [
          {
            label: 'Tentar Novamente',
            action: () => {
              // Scroll para o topo do formulário
              window.scrollTo({ top: 0, behavior: 'smooth' });
            },
            style: 'primary'
          }
        ]
      }
    );
  }

  private resetFormAfterSuccess(): void {
    // Reset todos os formulários
    this.clientForm.reset({
      clientType: 'fisica',
      criarUsuario: true
    });
    
    this.vehicleForm.reset({
      enderecoVeiculo: 'proprio'
    });
    
    this.paymentForm.reset({
      tipoServico: ''
    });
    
    // Voltar para o primeiro step
    this.currentStep = 1;
    
    // Limpar estados de validação
    this.markFormGroupUntouched(this.clientForm);
    this.markFormGroupUntouched(this.vehicleForm);
    this.markFormGroupUntouched(this.paymentForm);
  }
  
  private markFormGroupUntouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsUntouched();
      control?.markAsPristine();
    });
  }

  // Métodos auxiliares para o template
  isClientTypePessoaJuridica(): boolean {
    return this.clientForm.get('clientType')?.value === 'juridica';
  }

  isVehicleAddressOther(): boolean {
    return this.vehicleForm.get('enderecoVeiculo')?.value === 'outro';
  }
}
