/* Main Service Request Section */
.careers-form-section {
  background-color: #fff;
}

/* Page Title */
.careers-main-title {
  font-size: 30px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  letter-spacing: 1px;
}

.careers-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 32px;
}

/* Steps Section */
.careers-steps {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.steps-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 25px;
  border-bottom: 1px solid #eee;
  transition: all 0.3s ease;
}

.step-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.step-item.active .step-number {
  background: #1c63b8;
  color: #fff;
  border-color: #1c63b8;
}

.step-item.completed .step-number {
  background: #28a745;
  color: #fff;
  border-color: #28a745;
}

.step-number {
  width: 50px;
  height: 50px;
  border: 2px solid #1c63b8;
  color: #1c63b8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 20px;
  margin-right: 15px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.step-content h4 {
  font-size: 14px;
  font-weight: 500;
  color: #777;
  margin: 0;
  line-height: 1.4;
}

/* Form Container */
.careers-form-container {
  background: #F7F7F7;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Step Content */
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Sections */
.form-section {
  margin-bottom: 40px;
}

.form-section:last-of-type {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 25px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Form Groups */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

/* Form Controls */
.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #fff;
  height: 48px;
}

.form-control:focus {
  outline: none;
  border-color: #1c63b8;
  box-shadow: 0 0 0 0.2rem rgba(28, 99, 184, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control::placeholder {
  color: #999;
}

/* Select Controls */
select.form-control {
  height: 48px;
  padding: 12px 15px;
  padding-right: 40px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  appearance: none;
}

/* Textarea Controls */
textarea.form-control {
  height: auto;
  min-height: 100px;
  padding: 12px 15px;
  resize: vertical;
}

/* Radio Groups */
.radio-group {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 50%;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: #1c63b8;
  background-color: #1c63b8;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Checkbox Groups */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.service-item {
  display: flex;
  flex-direction: column;
}

/* Descrições dos Serviços */
.service-description {
  margin-top: 10px;
  margin-left: 30px;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-left: 4px solid #1c63b8;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.checkbox-option input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom {
  border-color: #1c63b8;
  background-color: #1c63b8;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Submit Button */
.form-submit {
  text-align: center;
  margin-top: 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.submit-btn {
  background: #1c63b8;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.submit-btn:hover:not(:disabled) {
  background: #155a9e;
  transform: translateY(-1px);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.submit-btn.secondary {
  background: #6c757d;
  color: #fff;
}

.submit-btn.secondary:hover:not(:disabled) {
  background: #5a6268;
}

/* Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

.spinner-border {
  display: inline-block;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .careers-main-title {
    font-size: 24px;
  }

  .careers-subtitle {
    font-size: 14px;
  }

  .careers-steps {
    padding: 20px;
    margin-bottom: 20px;
  }

  .careers-form-container {
    padding: 25px;
  }

  .step-item {
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  .step-number {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-right: 12px;
  }

  .step-content h4 {
    font-size: 13px;
  }

  .section-title {
    font-size: 16px;
  }

  .form-control {
    padding: 10px 12px;
    height: 44px;
  }

  select.form-control {
    height: 44px;
    padding: 10px 12px;
    padding-right: 36px;
  }

  textarea.form-control {
    height: auto;
    min-height: 80px;
    padding: 10px 12px;
  }

  .radio-group {
    flex-direction: column;
    gap: 15px;
  }

  .form-submit {
    flex-direction: column;
    gap: 15px;
  }

  .submit-btn {
    width: 100%;
    padding: 12px 20px;
  }
}

@media (max-width: 576px) {
  .careers-main-title {
    font-size: 20px;
  }

  .careers-steps {
    padding: 15px;
  }

  .careers-form-container {
    padding: 20px;
  }

  .step-content {
    min-height: 300px;
  }

  .form-control {
    padding: 10px 12px;
    height: 44px;
  }

  select.form-control {
    height: 44px;
    padding: 10px 12px;
    padding-right: 36px;
  }

  textarea.form-control {
    height: auto;
    min-height: 80px;
    padding: 10px 12px;
  }
}

/* Utility classes */
.mb-1 { margin-bottom: 0.25rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1.5rem; }
.pt-80 { padding-top: 80px; }
.pb-70 { padding-bottom: 70px; }
.me-2 { margin-right: 0.5rem; }

/* Select Controls */
select.form-control {
  height: 48px;
  padding: 12px 15px;
  padding-right: 40px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  appearance: none;
}

/* Textarea Controls */
textarea.form-control {
  height: auto;
  min-height: 100px;
  padding: 12px 15px;
  resize: vertical;
}

/* Radio Groups */
.radio-group {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 50%;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: #1c63b8;
  background-color: #1c63b8;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Checkbox Groups */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.checkbox-option input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom {
  border-color: #1c63b8;
  background-color: #1c63b8;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Form Navigation */
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.form-navigation .btn {
  padding: 12px 30px;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: #1c63b8;
  color: #fff;
}

.btn-primary:hover:not(:disabled) {
  background-color: #155a9e;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #6c757d;
  color: #fff;
}

.btn-secondary:hover {
  background-color: #5a6268;
  transform: translateY(-1px);
}

.btn-success {
  background-color: #28a745;
  color: #fff;
}

.btn-success:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

.spinner-border {
  display: inline-block;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-request-main-title {
    font-size: 24px;
  }

  .service-request-subtitle {
    font-size: 14px;
  }

  .service-request-steps {
    padding: 20px;
    margin-bottom: 20px;
  }

  .service-request-form-container {
    padding: 25px;
  }

  .step-item {
    padding: 12px;
  }

  .step-number {
    width: 30px;
    height: 30px;
    margin-right: 12px;
  }

  .step-content h4 {
    font-size: 13px;
  }

  .section-title {
    font-size: 18px;
  }

  .form-control {
    padding: 10px 12px;
    height: 44px;
  }

  select.form-control {
    height: 44px;
    padding: 10px 12px;
    padding-right: 36px;
  }

  textarea.form-control {
    height: auto;
    min-height: 80px;
    padding: 10px 12px;
  }

  .radio-group {
    flex-direction: column;
    gap: 15px;
  }

  .form-navigation {
    flex-direction: column;
    gap: 15px;
  }

  .form-navigation .btn {
    width: 100%;
    padding: 12px 20px;
  }
}

@media (max-width: 576px) {
  .service-request-main-title {
    font-size: 20px;
  }

  .service-request-steps {
    padding: 15px;
  }

  .service-request-form-container {
    padding: 20px;
  }

  .step-content {
    min-height: 300px;
  }

  .form-control {
    padding: 10px 12px;
    height: 44px;
  }

  select.form-control {
    height: 44px;
    padding: 10px 12px;
    padding-right: 36px;
  }

  textarea.form-control {
    height: auto;
    min-height: 80px;
    padding: 10px 12px;
  }
}

/* Grid fix - garantir que as colunas tenham altura igual */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col-md-4,
.col-md-6,
.col-md-8 {
  display: flex;
  flex-direction: column;
  padding-right: 15px;
  padding-left: 15px;
}

/* Utility classes */
.mb-1 { margin-bottom: 0.25rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1.5rem; }
.pt-80 { padding-top: 80px; }
.pb-70 { padding-bottom: 70px; }
.me-2 { margin-right: 0.5rem; }
