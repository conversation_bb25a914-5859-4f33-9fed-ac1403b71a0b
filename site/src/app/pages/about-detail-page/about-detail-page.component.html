<!-- Header -->
<app-header headerType="auto"></app-header>

<!-- Featured Title -->
<app-featured-title
  [title]="pageTitle"
  [breadcrumbs]="breadcrumbs"
  alignment="left">
</app-featured-title>

<!-- Main Content -->
<div id="main-content" class="site-main clearfix">
  <div id="content-wrap">
    <div id="site-content" class="site-content clearfix">
      <div id="inner-content" class="inner-content-wrap">
        <div class="page-content">
          <div class="container">
            <div class="row pt-90">

              <!-- Sidebar -->
              <div class="col-md-3">
                <nav class="wprt-navbar style-1">
                  <ul class="menu">
                    <li class="font-heading"
                        *ngFor="let item of sidebarMenu"
                        [ngClass]="{'current-nav-item': item.isActive}">
                      <a [href]="item.url" [title]="item.title">{{ item.title }}</a>
                    </li>
                  </ul>
                </nav><!-- /.wprt-navbar -->

                <!-- Promotional Box -->
                <div class="wprt-content-box style-2">
                  <div class="wprt-icon-box style-6 clearfix icon-top w70 accent-bg align-left rounded-100 has-width">
                    <h3 class="heading margin-top-10 font-weight-600">
                      <a href="#">GET 10% OFF TODAY</a>
                    </h3>

                    <p class="desc">A Completely Safe and Advanced Cleaning Solution for both Petrol and Diesel Engines</p>

                    <div class="elm-btn">
                      <a class="small wprt-button accent" href="#">Click for Detail</a>
                    </div>
                  </div><!-- /.wprt-icon-box -->
                </div><!-- /.wprt-content-box -->

              </div><!-- /.col-md-3 -->

              <!-- Main Content -->
              <div class="col-md-9">
                <div class="wprt-content-box style-1">
                  <!-- Gallery Slider -->
                  <div class="wprt-images-grid" data-layout="slider" data-column="1" data-column2="1" data-column3="1" data-column4="1" data-gaph="0" data-gapv="0">
                    <div id="images-wrap" class="cbp">
                      <div class="cbp-item" *ngFor="let image of galleryImages; let i = index"
                           [style.display]="i === currentGalleryIndex ? 'block' : 'none'">
                        <div class="item-wrap">
                          <a class="zoom-popup" [href]="image.fullsize">
                            <i class="rt-icon-zoom-in-2"></i>
                          </a>
                          <img [src]="image.thumbnail" [alt]="image.alt">
                        </div>
                      </div><!-- /.cbp-item -->
                    </div>

                    <!-- Gallery Navigation -->
                    <div class="gallery-nav" style="text-align: center; margin-top: 10px;">
                      <button (click)="prevImage()" class="btn btn-sm btn-outline-secondary">‹ Anterior</button>
                      <span *ngFor="let image of galleryImages; let i = index"
                            (click)="selectImage(i)"
                            [ngClass]="{'active': i === currentGalleryIndex}"
                            style="margin: 0 5px; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc; display: inline-block;">
                        {{ i + 1 }}
                      </span>
                      <button (click)="nextImage()" class="btn btn-sm btn-outline-secondary">Próximo ›</button>
                    </div>
                  </div><!-- /.wprt-images-grid -->

                  <h3>We do what's right. Now.</h3>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis. Curabitur vitae sem ullamcorper, elementum ipsum vitae, convallis ante. Proin et pretium odio. Duis bibendum ex sed nulla vestibulum lacinia. Vivamus elementum lorem massa, sed laoreet est tempor in. Donec aliquam suscipit maximus. Aliquam mauris sem.</p>

                  <p class="font-size-16">At quality CarService, our professional service is <span class="text-accent-color">second to none</span>. We guarantee to deliver.</p>

                  <!-- Main Services -->
                  <div class="row">
                    <div class="col-md-4" *ngFor="let service of mainServices">
                      <div class="wprt-content-box style-3">
                        <div class="wprt-icon-box style-6 clearfix icon-top w75 accent-bg align-left has-width">
                          <div class="icon-wrap">
                            <i [class]="service.icon"></i>
                          </div>

                          <h3 class="heading">
                            <a href="#">{{ service.title }}</a>
                          </h3>

                          <p class="desc">{{ service.description }}</p>
                        </div><!-- /.wprt-icon-box -->
                      </div><!-- /.wprt-content-box -->

                    </div><!-- /.col-md-4 -->
                  </div><!-- /.row -->

                  <div class="wprt-divider divider-center divider-solid"></div>

                  <h3>Your Car is in the Right Hands</h3>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis. Curabitur vitae sem ullamcorper, elementum ipsum vitae, convallis ante.</p>

                  <div class="wprt-divider divider-center divider-solid"></div>

                  <!-- Certifications -->
                  <div *ngFor="let cert of certifications; let i = index">
                    <div class="wprt-icon-box style-7 clearfix icon-left">
                      <div class="image-wrap">
                        <img [src]="cert.image" alt="Image">
                      </div>

                      <h3 class="heading">{{ cert.title }}</h3>

                      <p class="desc">{{ cert.description }}</p>
                    </div><!-- /.wprt-icon-box -->

                    <div [ngClass]="{'mb-50': i === certifications.length - 1, 'mb-40': i !== certifications.length - 1}"></div>

                    <div *ngIf="i < certifications.length - 1" class="wprt-divider divider-center divider-solid"></div>
                    
                  </div>

                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis. Curabitur vitae sem ullamcorper, elementum ipsum vitae, convallis ante. Proin et pretium odio. Duis bibendum ex sed nulla vestibulum lacinia. Vivamus elementum lorem massa, sed laoreet est tempor in. Donec aliquam suscipit maximus. Aliquam mauris sem.</p>

                  <!-- FAQ Accordion -->
                  <app-accordion
                    [items]="faqItems"
                    style="style-2">
                  </app-accordion>
                </div><!-- /.wprt-content-box -->
              </div><!-- /.col-md-9 -->

              <div class="col-md-12">
                </div><!-- /.col-md-12 -->
        </div><!-- /.row -->
      </div><!-- /.container -->
      <div class="pb-90"></div>

          <!-- PROMOTION BANNER -->
          <app-promotion-banner
            title="Conheça mais detalhes sobre nossa empresa e serviços"
            buttonText="SOLICITE AGORA"
            buttonAction="service-request">
          </app-promotion-banner>
          <!-- END PROMOTION BANNER -->

        </div><!-- /.page-content -->
      </div><!-- /#inner-content -->
    </div><!-- /#site-content -->
  </div><!-- /#content-wrap -->
</div><!-- /#main-content -->

<!-- Footer -->
<app-footer></app-footer>
