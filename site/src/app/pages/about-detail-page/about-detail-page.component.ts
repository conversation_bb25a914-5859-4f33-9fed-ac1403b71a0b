import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FeaturedTitleComponent, BreadcrumbItem } from '../../shared/featured-title/featured-title.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';
import { AccordionComponent, AccordionItem } from '../../shared/accordion/accordion.component';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';

@Component({
  selector: 'app-about-detail-page',
  imports: [
    CommonModule,
    HeaderComponent,
    FooterComponent,
    FeaturedTitleComponent,
    AccordionComponent,
    PromotionBannerComponent
  ],
  templateUrl: './about-detail-page.component.html',
  styleUrl: './about-detail-page.component.css'
})
export class AboutDetailPageComponent {

  // Dados para o Featured Title
  pageTitle = 'About Company';
  breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'About Company', isActive: true }
  ];

  // Menu de navegação lateral
  sidebarMenu = [
    { title: 'About Our Company', url: '#', isActive: true },
    { title: 'Awards & Recognition', url: '#', isActive: false },
    { title: 'Career Opportunities', url: '#', isActive: false },
    { title: 'Exclusive Offers', url: '#', isActive: false },
    { title: 'Maintenance Packages', url: '#', isActive: false }
  ];

  // Galeria de imagens
  galleryImages = [
    {
      thumbnail: 'assets/img/gallery/gallery-1-848x439.jpg',
      fullsize: 'assets/img/gallery/gallery-1-full.jpg',
      alt: 'Gallery Image 1'
    },
    {
      thumbnail: 'assets/img/gallery/gallery-2-848x439.jpg',
      fullsize: 'assets/img/gallery/gallery-2-full.jpg',
      alt: 'Gallery Image 2'
    },
    {
      thumbnail: 'assets/img/gallery/gallery-3-848x439.jpg',
      fullsize: 'assets/img/gallery/gallery-3-full.jpg',
      alt: 'Gallery Image 3'
    }
  ];

  // Serviços principais
  mainServices = [
    {
      icon: 'as-icon-mechanic',
      title: 'Transparency',
      description: 'Nulla imperdiet ultricies sapien eu blandit. Curabitur sem est, iaculis nec malesuada aliquet, fringilla at arcu.'
    },
    {
      icon: 'as-icon-inspection',
      title: 'Quality & Value',
      description: 'Nulla imperdiet ultricies sapien eu blandit. Curabitur sem est, iaculis nec malesuada aliquet, fringilla at arcu.'
    },
    {
      icon: 'as-icon-diagnostic',
      title: 'Technology',
      description: 'Nulla imperdiet ultricies sapien eu blandit. Curabitur sem est, iaculis nec malesuada aliquet, fringilla at arcu.'
    }
  ];

  // Certificações
  certifications = [
    {
      image: 'assets/img/ase-2.png',
      title: 'ASE CERTIFIED AUTOMOTIVE',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis.'
    },
    {
      image: 'assets/img/allstate.png',
      title: 'ALLSTATE CERTIFIED',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis.'
    }
  ];

  // FAQ Accordion
  faqItems: AccordionItem[] = [
    {
      title: 'Over 25 Years Experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis. Curabitur vitae sem ullamcorper, elementum ipsum vitae, convallis ante. Proin et pretium odio. Duis bibendum ex sed nulla vestibulum lacinia. Vivamus elementum lorem massa, sed laoreet est tempor in.',
      isActive: false,
      hasIcon: false
    },
    {
      title: 'Collision Repair Excellence',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis. Curabitur vitae sem ullamcorper, elementum ipsum vitae, convallis ante. Proin et pretium odio. Duis bibendum ex sed nulla vestibulum lacinia. Vivamus elementum lorem massa, sed laoreet est tempor in.',
      isActive: true,
      hasIcon: false
    },
    {
      title: 'Accreditation & Certifications',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus rhoncus sem et luctus finibus. Cras lobortis pellentesque quam non mollis. Curabitur vitae sem ullamcorper, elementum ipsum vitae, convallis ante. Proin et pretium odio. Duis bibendum ex sed nulla vestibulum lacinia. Vivamus elementum lorem massa, sed laoreet est tempor in.',
      isActive: false,
      hasIcon: false
    }
  ];

  currentGalleryIndex = 0;

  // Métodos para navegação da galeria
  nextImage(): void {
    this.currentGalleryIndex = (this.currentGalleryIndex + 1) % this.galleryImages.length;
  }

  prevImage(): void {
    this.currentGalleryIndex = this.currentGalleryIndex === 0
      ? this.galleryImages.length - 1
      : this.currentGalleryIndex - 1;
  }

  selectImage(index: number): void {
    this.currentGalleryIndex = index;
  }

  get currentImage() {
    return this.galleryImages[this.currentGalleryIndex];
  }
}
