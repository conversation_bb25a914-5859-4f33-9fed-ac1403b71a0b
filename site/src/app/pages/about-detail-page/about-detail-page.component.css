/* Estilos para a galeria */
.gallery-nav {
  margin-top: 15px;
  text-align: center;
}

.gallery-nav button {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 8px 12px;
  margin: 0 5px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.gallery-nav button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.gallery-nav span {
  display: inline-block;
  margin: 0 3px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #495057;
}

.gallery-nav span:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.gallery-nav span.active {
  background: #1c63b8;
  border-color: #1c63b8;
  color: white;
}

/* Estilos para o menu lateral */
.wprt-navbar.style-1 ul.menu li.current-nav-item a {
  color: #1c63b8;
  font-weight: 600;
}

/* Estilos para os dividers */
.wprt-divider.divider-center.divider-solid {
  border-top: 1px solid #e9ecef;
  margin: 0 auto;
  width: 100%;
}

/* Responsividade para a galeria */
@media (max-width: 768px) {
  .gallery-nav button,
  .gallery-nav span {
    padding: 6px 8px;
    font-size: 12px;
    margin: 0 2px;
  }
}