import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FeaturedTitleComponent, BreadcrumbItem } from '../../shared/featured-title/featured-title.component';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { ToastService } from '../../shared/toast/toast.service';

@Component({
  selector: 'app-appointment-page',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    HeaderComponent,
    FooterComponent,
    FeaturedTitleComponent
  ],
  templateUrl: './appointment-page.component.html',
  styleUrl: './appointment-page.component.css'
})
export class AppointmentPageComponent implements OnInit {

  appointmentForm!: FormGroup;

  // Dados para o Featured Title
  pageTitle = 'Book Appointment';
  breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'Book Appointment', isActive: true }
  ];

  constructor(
    private fb: FormBuilder,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.appointmentForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]],
      service: ['', [Validators.required]],
      date: ['', [Validators.required]],
      time: ['', [Validators.required]],
      message: ['']
    });
  }

  onSubmit(): void {
    if (this.appointmentForm.valid) {
      this.toastService.success(
        'Agendamento realizado com sucesso! Entraremos em contato para confirmar.',
        'Agendamento Confirmado',
        {
          duration: 8000,
          actions: [
            {
              label: 'Agendar Outro',
              action: () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }
            }
          ]
        }
      );
      this.appointmentForm.reset();
    } else {
      Object.keys(this.appointmentForm.controls).forEach(key => {
        this.appointmentForm.get(key)?.markAsTouched();
      });
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.appointmentForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }
}
