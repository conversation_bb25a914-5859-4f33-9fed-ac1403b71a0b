import { Component, OnInit, After<PERSON>iew<PERSON>nit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { FeaturedTitleComponent } from '../../shared/featured-title/featured-title.component';
import { AccordionComponent } from '../../shared/accordion/accordion.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';

declare var $: any;

@Component({
  selector: 'app-faqs-page',
  imports: [CommonModule, HeaderComponent, FooterComponent, FeaturedTitleComponent, AccordionComponent, PromotionBannerComponent],
  templateUrl: './faqs-page.component.html',
  styleUrl: './faqs-page.component.css'
})
export class FaqsPageComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
  private initializationTimeout: any;
  pageTitle = "FAQ'S";
  breadcrumbs = [
    { label: 'Home', url: '/' },
    { label: "FAQ'S", url: '/faqs' }
  ];

  faqItems = [
    {
      title: 'Qual a diferença entre vistoria e cautelar?',
      content: 'A vistoria verifica aspectos físicos e estruturais do veículo. A cautelar analisa o histórico legal e documental.',
      active: true
    },
    {
      title: 'Em quanto tempo recebo o relatório?',
      content: 'O relatório é enviado em até 24 horas após a finalização da análise.',
      active: false
    },
    {
      title: 'Vocês atendem pessoas físicas ou apenas concessionárias?',
      content: 'Atendemos ambos, com planos adaptados para cada tipo de cliente.',
      active: false
    },
    {
      title: 'A vistoria é obrigatória para transferência de veículo?',
      content: 'Não necessariamente, mas é altamente recomendada para evitar problemas futuros.',
      active: false
    },
    {
      title: 'Onde posso realizar a vistoria?',
      content: 'Temos unidades parceiras em diversas regiões. Consulte nosso atendimento para agendar.',
      active: false
    }
  ];

  ngOnInit() {
    console.log('❓ FAQs: Componente inicializado');
  }

  ngAfterViewInit() {
    this.initializationTimeout = setTimeout(() => {
      this.initializePlugins();
    }, 100);
  }

  ngOnDestroy() {
    if (this.initializationTimeout) {
      clearTimeout(this.initializationTimeout);
    }
  }

  private initializePlugins(): void {
    try {
      if (typeof (window as any).AngularJQueryIntegration !== 'undefined') {
        const integration = (window as any).AngularJQueryIntegration;
        integration.initialized = false;
        integration.init();
        console.log('✅ FAQs: Plugins reinicializados');
      } else if (typeof (window as any).initializeJQueryPlugins === 'function') {
        (window as any).initializeJQueryPlugins();
        console.log('✅ FAQs: Plugins inicializados via função global');
      }
    } catch (error) {
      console.error('❌ FAQs: Erro na inicialização dos plugins:', error);
    }
  }
}
