/* Garantir visibilidade da página ABOUT */
:host {
  display: block;
  min-height: 100vh;
}

/* Fallback para animsition - garantir que a página seja sempre visível */
#wrapper {
  opacity: 1 !important;
  visibility: visible !important;
  min-height: 100vh;
}

/* Garantir que o conteúdo principal seja visível */
#main-content {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fallback para Revolution Slider */
.rev_slider_wrapper {
  opacity: 1 !important;
  visibility: visible !important;
}

.rev_slider {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Garantir que seções sejam visíveis mesmo sem animações */
.wprt-animation-block {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Loading state melhorado */
.animsition-loading {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
}

/* Garantir que elementos críticos sejam sempre visíveis */
.page-content {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fallback para casos onde jQuery falha completamente */
.no-js #wrapper,
.no-jquery #wrapper {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Melhorar performance de renderização */
#wrapper,
#main-content,
.page-content {
  will-change: auto;
  transform: translateZ(0);
}

/* Garantir que imagens sejam carregadas corretamente */
img {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fallback para counter animations */
.wprt-counter .number {
  opacity: 1 !important;
  visibility: visible !important;
}

/* ========================================
   MELHORIAS VISUAIS PARA "5 RAZÕES PARA ESCOLHER"
   ======================================== */

/* Cards das razões - melhorias visuais */
.reasons-card {
  border: 2px solid #e8f4fd !important;
  border-radius: 15px !important;
  box-shadow: 0 8px 25px rgba(28, 99, 184, 0.1) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%) !important;
}

.reasons-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 15px 40px rgba(28, 99, 184, 0.2) !important;
  border-color: #1c63b8 !important;
}

.reasons-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1c63b8 0%, #4a90e2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.reasons-card:hover::before {
  opacity: 1;
}

/* Wrapper dos ícones - melhorias */
.reasons-icon-wrap {
  position: relative !important;
  margin-bottom: 35px !important;
}

.reasons-icon-wrap::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90px;
  height: 90px;
  background: rgba(28, 99, 184, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  transition: all 0.3s ease;
}

.reasons-card:hover .reasons-icon-wrap::after {
  width: 100px;
  height: 100px;
  background: rgba(28, 99, 184, 0.15);
}

/* Ícones - tamanhos aumentados e melhorias */
.reasons-icon {
  width: 70px !important;
  height: 70px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
  position: relative !important;
  z-index: 1 !important;
  box-shadow: 0 4px 15px rgba(28, 99, 184, 0.3) !important;
  transition: all 0.3s ease !important;
  background-color: #1c63b8 !important;
}

/* Sobrescrever qualquer estilo que possa estar afetando a cor dos ícones */
.reasons-icon.bg-accent-color {
  background-color: #1c63b8 !important;
}

.reasons-icon i {
  font-size: 32px !important;
  color: #ffffff !important;
  transition: all 0.3s ease !important;
  opacity: 1 !important;
  display: block !important;
  visibility: visible !important;
}

/* Garantir que os ícones sejam sempre visíveis */
.reasons-icon i:before {
  color: #ffffff !important;
  opacity: 1 !important;
}

/* Estilos específicos para cada tipo de ícone */
.reasons-icon .as-icon-mechanic,
.reasons-icon .as-icon-inspection,
.reasons-icon .as-icon-speedometer2,
.reasons-icon .as-icon-diagnostic,
.reasons-icon .as-icon-key2 {
  color: #ffffff !important;
  opacity: 1 !important;
  font-size: 32px !important;
}

/* Sobrescrever qualquer estilo global que possa afetar os ícones */
.reasons-card .icon i,
.reasons-card .icon [class*="as-icon-"],
.reasons-card .icon [class^="as-icon-"] {
  color: #ffffff !important;
  opacity: 1 !important;
}

.reasons-card:hover .reasons-icon {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(28, 99, 184, 0.4) !important;
}

.reasons-card:hover .reasons-icon i {
  transform: scale(1.1) !important;
}

/* Títulos - melhorias tipográficas */
.reasons-title {
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  margin-bottom: 18px !important;
  transition: color 0.3s ease !important;
  line-height: 1.3 !important;
}

.reasons-card:hover .reasons-title {
  color: #1c63b8 !important;
}

/* Texto descritivo - melhorias */
.reasons-text {
  font-size: 15px !important;
  line-height: 1.6 !important;
  color: #6c757d !important;
  margin-bottom: 0 !important;
  transition: color 0.3s ease !important;
}

.reasons-card:hover .reasons-text {
  color: #495057 !important;
}

/* Padding interno melhorado */
.reasons-card .inner {
  padding: 45px 35px 40px !important;
}

/* Responsividade */
@media (max-width: 991px) {
  .reasons-card .inner {
    padding: 35px 25px 30px !important;
  }

  .reasons-icon {
    width: 60px !important;
    height: 60px !important;
  }

  .reasons-icon i {
    font-size: 28px !important;
  }

  .reasons-title {
    font-size: 18px !important;
  }

  .reasons-text {
    font-size: 14px !important;
  }
}

@media (max-width: 767px) {
  .reasons-card {
    margin-bottom: 25px !important;
  }

  .reasons-card .inner {
    padding: 30px 20px 25px !important;
  }

  .reasons-icon-wrap {
    margin-bottom: 25px !important;
  }

  .reasons-icon {
    width: 55px !important;
    height: 55px !important;
  }

  .reasons-icon i {
    font-size: 24px !important;
  }

  .reasons-title {
    font-size: 17px !important;
    margin-bottom: 15px !important;
  }
}

/* Animação adicional para os cards */
.reasons-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Efeito de pulso sutil nos ícones */
.reasons-icon {
  animation: subtlePulse 3s ease-in-out infinite;
}

@keyframes subtlePulse {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(28, 99, 184, 0.3);
  }
  50% {
    box-shadow: 0 4px 15px rgba(28, 99, 184, 0.5);
  }
}

.reasons-card:hover .reasons-icon {
  animation: none;
}