import { Component, OnInit, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FeaturedTitleComponent, BreadcrumbItem } from '../../shared/featured-title/featured-title.component';
import { AccordionComponent, AccordionItem } from '../../shared/accordion/accordion.component';
import { TeamComponent, TeamMember } from '../../shared/team/team.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { Router } from '@angular/router';

declare var $: any;
declare var wprtTheme: any;
declare var RevSlider: any;
declare var AngularJQueryIntegration: any;
declare var initializeJQueryPlugins: any;
declare var ensurePageVisibility: any;

@Component({
  selector: 'app-about-page',
  imports: [
    CommonModule,
    HeaderComponent,
    FooterComponent,
    FeaturedTitleComponent,
    AccordionComponent,
    TeamComponent,
    PromotionBannerComponent
  ],
  templateUrl: './about-page.component.html',
  styleUrl: './about-page.component.css'
})
export class AboutPageComponent implements OnInit, AfterViewInit, OnDestroy {
  private initializationTimeout: any;
  private maxRetries = 10;
  private retryCount = 0;

  constructor(private router: Router) {}

  ngOnInit() {
    // Garantir que a página seja visível imediatamente
    this.ensurePageVisibility();

    // Timeout de segurança para remover loading após 3 segundos
    setTimeout(() => {
      this.removeLoadingElements();
    }, 3000);
  }

  ngAfterViewInit() {
    // Aguardar um pouco para garantir que o DOM esteja pronto
    setTimeout(() => {
      this.initializePlugins();
    }, 100);
  }

  private initializePlugins(): void {
    try {
      // Usar sistema de integração existente se disponível
      if (typeof (window as any).AngularJQueryIntegration !== 'undefined') {
        const integration = (window as any).AngularJQueryIntegration;
        integration.initialized = false; // Forçar reinicialização
        integration.init();
        // Plugins reinicializados via AngularJQueryIntegration
      } else if (typeof (window as any).initializeJQueryPlugins === 'function') {
        (window as any).initializeJQueryPlugins();
        // Plugins inicializados via função global
      } else {
        // Sistema de integração jQuery não encontrado
      }

      // Inicializar especificamente o cubeportfolio para a galeria de imagens
      setTimeout(() => {
        this.initializeCubePortfolio();
      }, 200);

    } catch (error) {
      // Erro na inicialização dos plugins
    }
  }

  private initializeCubePortfolio(): void {
    if (typeof $ !== 'undefined' && $.fn.cubeportfolio) {
      $('.wprt-images-grid').each(function(this: HTMLElement) {
        const $this = $(this);
        const layout = $this.data("layout");
        const item = $this.data("column");
        const item2 = $this.data("column2");
        const item3 = $this.data("column3");
        const item4 = $this.data("column4");
        const gapH = Number($this.data("gaph"));
        const gapV = Number($this.data("gapv"));

        $this.find('#images-wrap').cubeportfolio({
          layoutMode: layout,
          defaultFilter: '*',
          animationType: 'quicksand',
          gapHorizontal: gapH,
          gapVertical: gapV,
          showNavigation: true,
          showPagination: true,
          gridAdjustment: 'responsive',
          rewindNav: false,
          auto: false,
          mediaQueries: [{
            width: 1500,
            cols: item
          }, {
            width: 1100,
            cols: item
          }, {
            width: 800,
            cols: item2
          }, {
            width: 550,
            cols: item3
          }, {
            width: 320,
            cols: item4
          }],
          caption: ' ',
          displayType: 'bottomToTop',
          displayTypeSpeed: 100
        });
      });
    }
  }

  private onPageFullyLoaded() {
    // Aguardar um pouco e então forçar remoção do loading
    setTimeout(() => {
      this.removeLoadingElements();
      document.body.classList.remove('loading-active');
    }, 500);
  }

  ngOnDestroy() {
    if (this.initializationTimeout) {
      clearTimeout(this.initializationTimeout);
    }
  }

  openServiceModal(event: Event) {
    event.preventDefault();
    this.router.navigate(['/solicitacao-servico']);
  }

  private ensurePageVisibility() {
    // Forçar visibilidade da página
    const wrapper = document.getElementById('wrapper');
    const mainContent = document.getElementById('main-content');
    const pageContent = document.querySelector('.page-content');

    if (wrapper) {
      wrapper.style.opacity = '1';
      wrapper.style.visibility = 'visible';
      // Remover classe animsition que pode estar causando problemas
      wrapper.classList.remove('animsition-loading');
    }

    if (mainContent) {
      mainContent.style.opacity = '1';
      mainContent.style.visibility = 'visible';
    }

    if (pageContent) {
      (pageContent as HTMLElement).style.opacity = '1';
      (pageContent as HTMLElement).style.visibility = 'visible';
    }

    // Remover elementos de loading
    this.removeLoadingElements();

    // Adicionar classe para indicar que Angular está pronto
    document.documentElement.classList.add('angular-ready');
  }

  private removeLoadingElements() {
    // Remover loading do animsition
    const animsitionLoading = document.querySelectorAll('.animsition-loading');
    animsitionLoading.forEach(element => {
      element.remove();
    });

    // Remover loading do Revolution Slider
    const tpLoaders = document.querySelectorAll('.tp-loader');
    tpLoaders.forEach(element => {
      element.remove();
    });

    // Remover classes de loading do body
    document.body.classList.remove('animsition-loading');

    // Garantir que o wrapper não tenha opacity 0
    const wrapper = document.getElementById('wrapper');
    if (wrapper) {
      wrapper.style.opacity = '1';
      wrapper.style.visibility = 'visible';
      wrapper.classList.add('fade-in');
    }

    // Forçar visibilidade do Revolution Slider
    const revSliderWrapper = document.querySelector('.rev_slider_wrapper');
    const revSlider = document.querySelector('.rev_slider');

    if (revSliderWrapper) {
      (revSliderWrapper as HTMLElement).style.opacity = '1';
      (revSliderWrapper as HTMLElement).style.visibility = 'visible';
    }

    if (revSlider) {
      (revSlider as HTMLElement).style.opacity = '1';
      (revSlider as HTMLElement).style.visibility = 'visible';
    }
  }

  private initializeWithIntegration() {
    // Usar a função global de integração se disponível
    if (typeof (window as any).AngularJQueryIntegration !== 'undefined') {
      (window as any).AngularJQueryIntegration.onReady(() => {
        this.ensurePageVisibility();
        // Aguardar um pouco e remover loading novamente para garantir
        setTimeout(() => {
          this.removeLoadingElements();
        }, 1000);
      });

      // Forçar inicialização se ainda não foi feita
      if (!(window as any).AngularJQueryIntegration.initialized) {
        (window as any).AngularJQueryIntegration.init();
      }
    } else {
      // Fallback para inicialização manual
      this.fallbackInitialization();
    }
  }

  private fallbackInitialization() {
    if (typeof $ === 'undefined') {
      this.retryInitialization();
      return;
    }

    try {
      // Inicializar plugins básicos
      if (typeof wprtTheme !== 'undefined' && wprtTheme.init) {
        wprtTheme.init();
      }

      if (typeof RevSlider !== 'undefined' && RevSlider.init) {
        setTimeout(() => {
          RevSlider.init();
          // Remover loading após Revolution Slider inicializar
          setTimeout(() => {
            this.removeLoadingElements();
          }, 500);
        }, 300);
      }

      // Garantir visibilidade
      this.ensurePageVisibility();

      // Remover loading após um tempo para garantir
      setTimeout(() => {
        this.removeLoadingElements();
      }, 1500);
    } catch (error) {
      this.ensurePageVisibility();
      this.removeLoadingElements();
    }
  }

  private retryInitialization() {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.initializationTimeout = setTimeout(() => {
        this.initializeWithIntegration();
      }, 500 * this.retryCount);
    } else {
      this.ensurePageVisibility();
    }
  }

  // Dados para o Featured Title
  pageTitle = 'About Company';
  breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'About Company', isActive: true }
  ];

  // Dados para o Accordion
  accordionItems: AccordionItem[] = [
    {
      title: 'Missão',
      content: `
        <p><strong>Promover negócios automotivos seguros e transparentes através de vistorias e análises técnicas de alta qualidade.</strong></p>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check-square-o text-accent-color"></i></span>
              <span class="text font-size-15">Garantimos segurança em cada transação veicular</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check-square-o text-accent-color"></i></span>
              <span class="text font-size-15">Transparência total em nossos processos e relatórios</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-check-square-o text-accent-color"></i></span>
              <span class="text font-size-15">Análises técnicas realizadas por profissionais certificados</span>
            </span>
          </div>
        </div>
      `,
      isActive: true,
      hasIcon: false
    },
    {
      title: 'Visão',
      content: `
        <p><strong>Ser referência nacional em serviços de cautelagem veicular, reconhecida pela confiabilidade e compromisso com o cliente.</strong></p>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-star text-accent-color"></i></span>
              <span class="text font-size-15">Liderança no mercado de vistoria veicular</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-star text-accent-color"></i></span>
              <span class="text font-size-15">Reconhecimento nacional pela qualidade dos serviços</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-star text-accent-color"></i></span>
              <span class="text font-size-15">Compromisso inabalável com a satisfação do cliente</span>
            </span>
          </div>
        </div>
      `,
      isActive: false,
      hasIcon: false
    },
    {
      title: 'Valores',
      content: `
        <p><strong>Nossos valores fundamentais guiam todas as nossas ações e decisões:</strong></p>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-heart text-accent-color"></i></span>
              <span class="text font-size-15"><strong>Transparência</strong> - Clareza total em nossos processos</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-heart text-accent-color"></i></span>
              <span class="text font-size-15"><strong>Comprometimento</strong> - Dedicação total aos nossos clientes</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-heart text-accent-color"></i></span>
              <span class="text font-size-15"><strong>Inovação</strong> - Sempre buscando as melhores soluções</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-heart text-accent-color"></i></span>
              <span class="text font-size-15"><strong>Excelência técnica</strong> - Padrão superior em todos os serviços</span>
            </span>
          </div>
        </div>
        <div class="wprt-list clearfix icon-left icon-top style-2">
          <div class="inner">
            <span class="icon-wrap">
              <span class="icon"><i class="fa fa-heart text-accent-color"></i></span>
              <span class="text font-size-15"><strong>Foco no cliente</strong> - Suas necessidades são nossa prioridade</span>
            </span>
          </div>
        </div>
      `,
      isActive: false,
      hasIcon: false
    }
  ];

  // Dados para os contadores
  counters = [
    {
      icon: 'as-icon-mechanic',
      number: 320,
      suffix: '',
      title: 'EXPERIENCED TECHNICIALS'
    },
    {
      icon: 'as-icon-key2',
      number: 100,
      suffix: '%',
      title: 'TRANSPARENCY MATTERS'
    },
    {
      icon: 'as-icon-inspection',
      number: 9580,
      suffix: '',
      title: 'COMPLETED PROJECTS'
    },
    {
      icon: 'as-icon-diagnostic',
      number: 140,
      suffix: '+',
      title: 'PROFESSIONAL AWARDS'
    }
  ];

  // Dados para a equipe (correspondendo ao template original)
  teamMembers: TeamMember[] = [
    {
      name: 'BARBARA MCLAREN',
      position: 'MARKETING MANAGER',
      image: 'assets/img/team/member-1-303x363.jpg',
      socials: {
        twitter: '#',
        facebook: '#',
        linkedin: '#'
      }
    },
    {
      name: 'BARBARA MCLAREN',
      position: 'MARKETING MANAGER',
      image: 'assets/img/team/member-2-303x363.jpg',
      socials: {
        twitter: '#',
        facebook: '#',
        linkedin: '#'
      }
    },
    {
      name: 'BARBARA MCLAREN',
      position: 'MARKETING MANAGER',
      image: 'assets/img/team/member-3-303x363.jpg',
      socials: {
        twitter: '#',
        facebook: '#',
        linkedin: '#'
      }
    },
    {
      name: 'BARBARA MCLAREN',
      position: 'MARKETING MANAGER',
      image: 'assets/img/team/member-4-303x363.jpg',
      socials: {
        twitter: '#',
        facebook: '#',
        linkedin: '#'
      }
    }
  ];
}
