<!-- Header -->
<app-header headerType="auto"></app-header>

<!-- Featured Title -->
<app-featured-title
  [title]="pageTitle"
  [breadcrumbs]="breadcrumbs"
  alignment="left">
</app-featured-title>

<!-- Main Content -->
<div id="main-content" class="site-main clearfix">
  <div id="content-wrap">
    <div id="site-content" class="site-content clearfix">
      <div id="inner-content" class="inner-content-wrap">
        <div class="page-content">

          <!-- MAP AND CONTACT -->
          <div class="row-map-1">
            <div class="container-fluid no-padding">
              <div class="row no-gutters">
                <div class="col-md-12">
                  <div id="gmap" class="wprt-gmap"></div>

                </div><!-- /.col-md-12 -->
              </div><!-- /.row -->
            </div><!-- /.container -->

            <div class="container pt-80">
              <div class="row large-gutters">
                <div class="col-md-4">
                  <div class="wprt-headings style-1 clearfix">
                    <h3 class="heading clearfix">Entre em Contato</h3>
                    <div class="sep clearfix"></div>
                    <p class="sub-heading clearfix">Entre em contato com os profissionais da AutoVPro para serviços de qualidade com preços competitivos.</p>
                  </div><!-- /.wprt-headings -->

                  <!-- Endereço -->
                  <div class="wprt-icon-box style-5 clearfix icon-left w50 accent-outline outline-type align-left rounded-100 has-width">
                    <div class="icon-wrap">
                      <i class="rt-icon-placeholder2"></i>
                    </div>
                    <h3 class="heading">
                      <a href="#">{{ contactInfo.address.title }}</a>
                    </h3>
                    <p class="desc">{{ contactInfo.address.content }}</p>
                  </div><!-- /.wprt-icon-box -->

                  <!-- Horário de Funcionamento -->
                  <div class="wprt-icon-box style-5 clearfix icon-left w50 accent-outline outline-type align-left rounded-100 has-width">
                    <div class="icon-wrap">
                      <i class="rt-icon-alarm-clock"></i>
                    </div>
                    <h3 class="heading">
                      <a href="#">{{ contactInfo.hours.title }}</a>
                    </h3>
                    <p class="desc" [innerHTML]="contactInfo.hours.content"></p>
                  </div><!-- /.wprt-icon-box -->

                  <!-- Telefone e E-mail -->
                  <div class="wprt-icon-box style-5 clearfix icon-left w50 accent-outline outline-type align-left rounded-100 has-width">
                    <div class="icon-wrap">
                      <i class="rt-icon-chat"></i>
                    </div>
                    <h3 class="heading">
                      <a href="#">{{ contactInfo.phone.title }}</a>
                    </h3>
                    <p class="desc" [innerHTML]="contactInfo.phone.content"></p>
                  </div><!-- /.wprt-icon-box -->

                </div><!-- /.col-md-4 -->

                <div class="col-md-8">
                  <div class="wprt-contact-form style-1 margin-top-10">
                    <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="contact-form wpcf7-form">

                      <span class="form-control-wrap your-name">
                        <input
                          type="text"
                          tabindex="1"
                          id="name"
                          formControlName="name"
                          class="wpcf7-form-control"
                          [class.error]="isFieldInvalid('name')"
                          placeholder="Nome *">
                        <div *ngIf="isFieldInvalid('name')" class="error-message">
                          {{ getFieldError('name') }}
                        </div>
                      </span>

                      <span class="form-control-wrap your-email">
                        <input
                          type="email"
                          tabindex="2"
                          id="email"
                          formControlName="email"
                          class="wpcf7-form-control"
                          [class.error]="isFieldInvalid('email')"
                          placeholder="E-mail *">
                        <div *ngIf="isFieldInvalid('email')" class="error-message">
                          {{ getFieldError('email') }}
                        </div>
                      </span>

                      <span class="form-control-wrap your-phone">
                        <input
                          type="tel"
                          tabindex="3"
                          id="phone"
                          formControlName="phone"
                          class="wpcf7-form-control"
                          [class.error]="isFieldInvalid('phone')"
                          placeholder="Telefone (opcional)">
                        <div *ngIf="isFieldInvalid('phone')" class="error-message">
                          {{ getFieldError('phone') }}
                        </div>
                      </span>

                      <span class="form-control-wrap your-message">
                        <textarea
                          formControlName="message"
                          tabindex="4"
                          cols="40"
                          rows="10"
                          class="wpcf7-form-control wpcf7-textarea"
                          [class.error]="isFieldInvalid('message')"
                          placeholder="Mensagem *"></textarea>
                        <div *ngIf="isFieldInvalid('message')" class="error-message">
                          {{ getFieldError('message') }}
                        </div>
                      </span>

                      <div class="wrap-submit">
                        <input
                          type="submit"
                          value="ENVIAR MENSAGEM"
                          class="submit wpcf7-form-control wpcf7-submit"
                          id="submit"
                          [disabled]="contactForm.invalid">
                      </div>
                    </form>
                  </div><!-- /.wprt-contact-form -->
                </div><!-- /.col-md-8 -->
              </div><!-- /.row -->

              <div class="row">
                <div class="col-md-12">
                  </div><!-- /.col-md-12 -->
        </div><!-- /.row -->
      </div><!-- /.container -->
      <div class="pb-90"></div>
          </div>
          <!-- END MAP AND CONTACT -->

          <!-- PROMOTION BANNER -->
          <app-promotion-banner
            title="Entre em contato conosco para esclarecer suas dúvidas"
            buttonText="SOLICITE AGORA"
            buttonAction="service-request">
          </app-promotion-banner>
          <!-- END PROMOTION BANNER -->

        </div><!-- /.page-content -->
      </div><!-- /#inner-content -->
    </div><!-- /#site-content -->
  </div><!-- /#content-wrap -->
</div><!-- /#main-content -->

<!-- Footer -->
<app-footer></app-footer>
