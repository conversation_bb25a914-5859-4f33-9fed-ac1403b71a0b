import { Component, OnInit, AfterViewInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FeaturedTitleComponent, BreadcrumbItem } from '../../shared/featured-title/featured-title.component';
import { HeaderComponent } from '../../shared/header/header.component';
import { FooterComponent } from '../../shared/footer/footer.component';
import { PromotionBannerComponent } from '../../shared/promotion-banner/promotion-banner.component';
import { ServiceRequestModalService } from '../../shared/service-request-modal/service-request-modal.component';
import { ToastService } from '../../shared/toast/toast.service';

declare var $: any;
declare var google: any;

@Component({
  selector: 'app-contact-page',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    HeaderComponent,
    FooterComponent,
    FeaturedTitleComponent,
    PromotionBannerComponent
  ],
  templateUrl: './contact-page.component.html',
  styleUrl: './contact-page.component.css'
})
export class ContactPageComponent implements OnInit, AfterViewInit, OnDestroy {
  private initializationTimeout: any;
  contactForm!: FormGroup;

  // Dados para o Featured Title
  pageTitle = 'Contato';
  breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'Contato', isActive: true }
  ];

  // Dados de contato
  contactInfo = {
    address: {
      title: 'ENDEREÇO:',
      content: 'Avenida 9 de Julho, Taubaté SP'
    },
    hours: {
      title: 'HORÁRIO DE FUNCIONAMENTO:',
      content: 'Segunda - Sexta: 8:00 - 18:00<br>Sábado: 8:00 - 15:00'
    },
    phone: {
      title: 'CONTATO:',
      content: 'Telefone: +55 11 93657-8574<br>Telefone: ************<br>E-mail: <EMAIL>'
    }
  };

  constructor(
    private fb: FormBuilder,
    private modalService: ServiceRequestModalService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    console.log('📞 Contact: Componente inicializado');
  }

  ngAfterViewInit() {
    this.initializationTimeout = setTimeout(() => {
      this.initializePlugins();
    }, 500); // Aumentar timeout para dar mais tempo aos scripts carregarem
  }

  ngOnDestroy() {
    if (this.initializationTimeout) {
      clearTimeout(this.initializationTimeout);
    }
  }

  private initializePlugins(): void {
    try {
      if (typeof (window as any).AngularJQueryIntegration !== 'undefined') {
        const integration = (window as any).AngularJQueryIntegration;
        integration.initialized = false;
        integration.init();
        console.log('✅ Contact: Plugins reinicializados');
      } else if (typeof (window as any).initializeJQueryPlugins === 'function') {
        (window as any).initializeJQueryPlugins();
        console.log('✅ Contact: Plugins inicializados via função global');
      }

      // Inicializar o mapa do Google
      this.initializeGoogleMap();

    } catch (error) {
      console.error('❌ Contact: Erro na inicialização dos plugins:', error);
    }
  }

  private initializeGoogleMap(): void {
    // Verificar se o elemento do mapa existe
    const mapElement = document.getElementById('gmap');
    if (!mapElement) {
      console.error('❌ Contact: Elemento #gmap não encontrado');
      return;
    }

    console.log('🗺️ Contact: Elemento #gmap encontrado, verificando dependências...');
    console.log('🗺️ Contact: Google disponível?', typeof google !== 'undefined');
    console.log('🗺️ Contact: jQuery disponível?', typeof $ !== 'undefined');
    console.log('🗺️ Contact: gmap3 disponível?', typeof $.fn.gmap3 !== 'undefined');
    console.log('🗺️ Contact: googleMap function disponível?', typeof (window as any).googleMap === 'function');

    // Verificar se o Google Maps API e gmap3 estão disponíveis
    if (typeof google !== 'undefined' && typeof $ !== 'undefined' && typeof $.fn.gmap3 !== 'undefined' && typeof (window as any).googleMap === 'function') {
      console.log('🗺️ Contact: Inicializando Google Maps...');
      try {
        (window as any).googleMap();
        console.log('✅ Contact: Google Maps inicializado com sucesso');
      } catch (error) {
        console.error('❌ Contact: Erro ao inicializar Google Maps:', error);
      }
    } else {
      // Tentar novamente após um pequeno delay
      setTimeout(() => {
        if (typeof google !== 'undefined' && typeof $ !== 'undefined' && typeof $.fn.gmap3 !== 'undefined' && typeof (window as any).googleMap === 'function') {
          try {
            (window as any).googleMap();
          } catch (error) {
            // Erro ao inicializar Google Maps
          }
        }
      }, 2000);

      // Terceira tentativa com timeout ainda maior
      setTimeout(() => {
        if (typeof google !== 'undefined' && typeof $ !== 'undefined') {
          console.log('🗺️ Contact: Terceira tentativa - tentando inicializar mapa manualmente...');
          this.initializeMapManually();
        }
      }, 5000);
    }
  }

  private initializeMapManually(): void {
    const mapElement = document.getElementById('gmap');
    if (!mapElement) {
      console.error('❌ Contact: Elemento #gmap não encontrado para inicialização manual');
      return;
    }

    if (typeof google === 'undefined') {
      console.error('❌ Contact: Google Maps API não está disponível para inicialização manual');
      return;
    }

    try {
      console.log('🗺️ Contact: Inicializando mapa manualmente...');

      // Configuração básica do mapa
      const mapOptions = {
        zoom: 14,
        center: new google.maps.LatLng(-23.0204, -45.5563), // Coordenadas de Taubaté-SP
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        scrollwheel: false,
        draggable: true
      };

      // Criar o mapa
      const map = new google.maps.Map(mapElement, mapOptions);

      // Geocodificar o endereço
      const geocoder = new google.maps.Geocoder();
      const address = 'Avenida 9 de Julho, Taubaté SP, Brasil';

      geocoder.geocode({ address: address }, (results: any, status: any) => {
        if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
          const location = results[0].geometry.location;
          map.setCenter(location);

          // Adicionar marcador
          const marker = new google.maps.Marker({
            position: location,
            map: map,
            title: 'AutoVPro',
            icon: 'assets/img/marker.png'
          });

          console.log('✅ Contact: Mapa inicializado manualmente com sucesso');
        } else {
          console.error('❌ Contact: Erro na geocodificação:', status);
          // Usar coordenadas padrão se a geocodificação falhar
          const defaultLocation = new google.maps.LatLng(37.4419, -122.1419);
          map.setCenter(defaultLocation);

          const marker = new google.maps.Marker({
            position: defaultLocation,
            map: map,
            title: 'AutoService Group',
            icon: 'assets/img/marker.png'
          });

          console.log('✅ Contact: Mapa inicializado manualmente com coordenadas padrão');
        }
      });

    } catch (error) {
      console.error('❌ Contact: Erro na inicialização manual do mapa:', error);
    }
  }

  openServiceModal(event: Event) {
    event.preventDefault();
    this.modalService.openModal();
  }

  private initializeForm(): void {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phone: [''], // Campo opcional
      message: ['', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.contactForm.valid) {
      // Aqui você implementaria o envio do formulário
      this.toastService.success(
        'Mensagem enviada com sucesso! Responderemos em breve.',
        'Mensagem Enviada',
        {
          duration: 8000,
          actions: [
            {
              label: 'Enviar Outra',
              action: () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }
            }
          ]
        }
      );
      this.contactForm.reset();
    } else {
      // Marcar todos os campos como touched para mostrar erros
      Object.keys(this.contactForm.controls).forEach(key => {
        this.contactForm.get(key)?.markAsTouched();
      });
    }
  }

  // Métodos auxiliares para validação
  isFieldInvalid(fieldName: string): boolean {
    const field = this.contactForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.contactForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} é obrigatório`;
      }
      if (field.errors['email']) {
        return 'Email inválido';
      }
    }
    return '';
  }
}
