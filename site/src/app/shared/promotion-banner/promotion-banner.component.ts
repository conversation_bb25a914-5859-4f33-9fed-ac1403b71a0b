import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-promotion-banner',
  imports: [],
  templateUrl: './promotion-banner.component.html',
  styleUrl: './promotion-banner.component.css'
})
export class PromotionBannerComponent {
  @Input() title: string = 'Estamos aqui para te ajudar a comprar seu veículo com segurança';
  @Input() buttonText: string = 'SOLICITE AGORA';
  @Input() buttonAction: 'service-request' | 'contact' | 'custom' = 'service-request';
  @Input() customRoute?: string;
  @Input() icon: string = 'as-icon-speedometer2';

  constructor(private router: Router) {}

  onButtonClick(event: Event) {
    event.preventDefault();
    
    switch (this.buttonAction) {
      case 'service-request':
        this.router.navigate(['/solicitacao-servico']);
        break;
      case 'contact':
        this.router.navigate(['/contato']);
        break;
      case 'custom':
        if (this.customRoute) {
          this.router.navigate([this.customRoute]);
        }
        break;
    }
  }
}
