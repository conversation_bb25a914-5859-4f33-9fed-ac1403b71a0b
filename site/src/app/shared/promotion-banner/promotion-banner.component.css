/* Promotion Banner Component Styles */
.row-promotion {
  position: relative;
}

.wprt-action-box {
  position: relative;
}

.wprt-action-box .inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.heading-wrap {
  flex: 1;
  min-width: 300px;
}

.text-wrap {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
}

.heading {
  margin: 0;
  flex: 1;
}

.icon {
  font-size: 24px;
  color: inherit;
  opacity: 0.8;
}

.button-wrap {
  flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .wprt-action-box .inner {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .heading-wrap {
    min-width: auto;
    width: 100%;
  }
  
  .text-wrap {
    justify-content: center;
  }
}
