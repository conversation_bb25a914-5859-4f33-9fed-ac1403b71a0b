<div class="breadcrumbs-inner" *ngIf="items.length > 0">
  <div class="breadcrumb-trail">
    <ng-container *ngFor="let item of items; let last = last">
      <a *ngIf="item.url && !item.isActive"
         [routerLink]="item.url"
         [title]="item.label"
         [ngClass]="{'trail-begin': items.indexOf(item) === 0}">
        {{ item.label }}
      </a>
      <span *ngIf="!item.url || item.isActive"
            [ngClass]="{'trail-end': item.isActive}">
        {{ item.label }}
      </span>
      <span *ngIf="!last" class="sep">
        <i class="rt-icon-right-arrow12"></i>
      </span>
    </ng-container>
  </div>
</div>
