<div class="wprt-accordions" [ngClass]="style">
  <div class="accordion-item"
       *ngFor="let item of items; let i = index"
       [ngClass]="{
         'active': item.isActive,
         'no-icon': !item.hasIcon
       }">
    <h3 class="accordion-heading angular-accordion" (click)="toggleItem(i)">
      <span class="inner">
        <i *ngIf="item.hasIcon && item.icon" [class]="item.icon"></i>
        {{ item.title }}
      </span>
    </h3>

    <div class="accordion-content"
         [class.active]="item.isActive">
      <div class="accordion-content-inner" [innerHTML]="item.content"></div>
    </div>
  </div>
</div>
