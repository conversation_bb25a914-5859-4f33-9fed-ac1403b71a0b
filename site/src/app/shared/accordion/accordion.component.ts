import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface AccordionItem {
  title: string;
  content: string;
  isActive?: boolean;
  hasIcon?: boolean;
  icon?: string;
}

@Component({
  selector: 'app-accordion',
  imports: [CommonModule],
  templateUrl: './accordion.component.html',
  styleUrl: './accordion.component.css'
})
export class AccordionComponent {
  @Input() items: AccordionItem[] = [];
  @Input() style: 'style-1' | 'style-2' = 'style-1';
  @Input() allowMultiple: boolean = false;

  toggleItem(index: number): void {
    const item = this.items[index];

    if (!this.allowMultiple) {
      // Fechar todos os outros itens
      this.items.forEach((item, i) => {
        if (i !== index) {
          item.isActive = false;
        }
      });
    }

    // Toggle do item atual
    item.isActive = !item.isActive;
  }
}
