/* Accordion Component Styles */

/* Container principal - solução híbrida para manter animação sem empurrar */
.wprt-accordions {
  position: relative !important;
}

.wprt-accordions .accordion-item {
  position: relative !important;
  z-index: 1 !important;
}

/* Transições suaves para o accordion - animação de altura preservada */
.wprt-accordions .accordion-content {
  display: block !important;
  height: 0 !important;
  overflow: hidden !important;
  transition: height 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              padding 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              opacity 0.3s ease !important;
  padding: 0 !important;
  opacity: 0 !important;
  background: #ffffff !important;
  border: 1px solid #ebebeb !important;
  border-top: none !important;
  margin-top: -1px !important;
}

.wprt-accordions .accordion-content.active {
  height: auto !important;
  min-height: 120px !important;
  padding: 22px 20px 18px !important;
  opacity: 1 !important;
  transition: height 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              padding 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              opacity 0.3s ease 0.1s !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  border-radius: 0 0 5px 5px !important;
}

.wprt-accordions .accordion-content-inner {
  opacity: 0;
  transform: translateY(-8px);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.wprt-accordions .accordion-content.active .accordion-content-inner {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease-in 0.2s, transform 0.3s ease-in 0.2s;
}

/* Solução específica para página about - overlay após animação */
.about-page .wprt-accordions {
  position: relative !important;
}

/* Criar um overlay que não empurra o conteúdo */
.about-page .wprt-accordions .accordion-content {
  position: relative !important;
}

.about-page .wprt-accordions .accordion-content.active {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  width: 100% !important;
  height: auto !important;
  max-height: 350px !important;
  overflow-y: auto !important;
  margin-top: 0 !important;
  animation: accordionSlideDown 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

/* Animação personalizada para simular a expansão */
@keyframes accordionSlideDown {
  0% {
    max-height: 0;
    opacity: 0;
    padding: 0 20px;
  }
  100% {
    max-height: 350px;
    opacity: 1;
    padding: 22px 20px 18px;
  }
}

/* Garantir que o accordion item tenha z-index maior quando ativo */
.about-page .wprt-accordions .accordion-item.active {
  z-index: 1001 !important;
}