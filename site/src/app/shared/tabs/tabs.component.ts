import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TabItem {
  title: string;
  content: string;
  isActive?: boolean;
}

@Component({
  selector: 'app-tabs',
  imports: [CommonModule],
  templateUrl: './tabs.component.html',
  styleUrl: './tabs.component.css'
})
export class TabsComponent {
  @Input() items: TabItem[] = [];
  @Input() style: 'style-1' | 'style-2' | 'style-3' | 'style-4' = 'style-1';
  @Input() titleWidth: string = '';

  ngOnInit(): void {
    // Ativar a primeira aba por padrão se nenhuma estiver ativa
    if (this.items.length > 0 && !this.items.some(item => item.isActive)) {
      this.items[0].isActive = true;
    }
  }

  selectTab(index: number): void {
    // Desativar todas as abas
    this.items.forEach(item => item.isActive = false);
    // Ativar a aba selecionada
    this.items[index].isActive = true;
  }

  get activeTabContent(): string {
    const activeTab = this.items.find(item => item.isActive);
    return activeTab ? activeTab.content : '';
  }
}
