/* Garantir que as abas fiquem na mesma linha */
.wprt-tabs .tab-title {
  display: flex;
  flex-wrap: nowrap;
  margin: 0;
  border-bottom: 1px solid #dbdbdb;
  justify-content: flex-start;
  align-items: center;
}

.wprt-tabs .tab-title .item-title {
  display: inline-block;
  margin: 0 2px 0 0;
  flex: 0 0 auto;
  white-space: nowrap;
}

.wprt-tabs .tab-title .item-title > span {
  padding: 10px 20px 8px;
  font-size: 14px;
}

/* Responsivo - em telas menores, permitir quebra de linha */
@media only screen and (max-width: 991px) {
  .wprt-tabs .tab-title {
    flex-wrap: wrap;
    justify-content: center;
  }

  .wprt-tabs .tab-title .item-title {
    flex: 1 1 auto;
    margin-bottom: 5px;
    min-width: 120px;
  }

  .wprt-tabs .tab-title .item-title > span {
    padding: 8px 15px 6px;
    font-size: 13px;
  }
}

@media only screen and (max-width: 576px) {
  .wprt-tabs .tab-title .item-title {
    flex: 1 1 100%;
    margin: 0 0 3px 0;
  }

  .wprt-tabs .tab-title .item-title > span {
    display: block;
    text-align: center;
    padding: 10px 15px 8px;
  }
}