<div class="wprt-tabs clearfix"
     [ngClass]="[style, titleWidth]">
  <ul class="tab-title clearfix">
    <li class="item-title"
        *ngFor="let item of items; let i = index"
        [ngClass]="{'active': item.isActive}"
        (click)="selectTab(i)">
      <span class="inner">{{ item.title }}</span>
    </li>
  </ul>

  <div class="tab-content-wrap">
    <div class="tab-content"
         *ngFor="let item of items"
         [style.display]="item.isActive ? 'block' : 'none'">
      <div class="item-content" [innerHTML]="item.content"></div>
    </div>
  </div>
</div>
