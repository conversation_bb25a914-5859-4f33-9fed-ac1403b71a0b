import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export enum ToastType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info'
}

export enum ToastPosition {
  TOP_RIGHT = 'top-right',
  TOP_LEFT = 'top-left',
  TOP_CENTER = 'top-center',
  BOTTOM_RIGHT = 'bottom-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_CENTER = 'bottom-center'
}

export interface ToastConfig {
  id?: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
  position?: ToastPosition;
  dismissible?: boolean;
  actions?: ToastAction[];
  data?: any;
}

export interface ToastAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

export interface ToastInstance extends ToastConfig {
  id: string;
  timestamp: number;
  visible: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toasts$ = new BehaviorSubject<ToastInstance[]>([]);
  private defaultConfig: Partial<ToastConfig> = {
    duration: 5000,
    position: ToastPosition.TOP_RIGHT,
    dismissible: true
  };

  constructor() {}

  getToasts(): Observable<ToastInstance[]> {
    return this.toasts$.asObservable();
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  private createToast(config: ToastConfig): ToastInstance {
    const toast: ToastInstance = {
      ...this.defaultConfig,
      ...config,
      id: config.id || this.generateId(),
      timestamp: Date.now(),
      visible: false  // Start with visible false to prevent FOUC
    };

    return toast;
  }

  private addToast(toast: ToastInstance): void {
    // Wait for DOM stability, then add with proper animation sequence
    this.waitForDOMStability(() => {
      // Step 1: Add toast to DOM in hidden state (prevents FOUC)
      const currentToasts = this.toasts$.value;
      this.toasts$.next([...currentToasts, toast]);
      
      // Step 2: Trigger smooth entrance animation after next frame
      requestAnimationFrame(() => {
        const updatedToasts = this.toasts$.value.map(t => 
          t.id === toast.id ? { ...t, visible: true } : t
        );
        this.toasts$.next(updatedToasts);
      });
      
      // Auto-dismiss if duration is set
      if (toast.duration && toast.duration > 0) {
        setTimeout(() => {
          this.dismiss(toast.id);
        }, toast.duration);
      }
    });
  }

  private waitForDOMStability(callback: () => void): void {
    // Universal approach: combine multiple strategies for maximum reliability
    // This works regardless of page complexity, scripts, or frameworks
    
    let callbackExecuted = false;
    
    const executeCallback = () => {
      if (!callbackExecuted) {
        callbackExecuted = true;
        callback();
      }
    };

    // Strategy 1: requestIdleCallback for optimal performance (when available)
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(executeCallback, { timeout: 100 });
    } else {
      // Strategy 2: Fallback - wait for next available frame + small buffer
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setTimeout(executeCallback, 50);
        });
      });
    }
    
    // Strategy 3: Safety net timeout (always executes within 200ms)
    setTimeout(executeCallback, 200);
  }

  show(config: ToastConfig): string {
    const toast = this.createToast(config);
    this.addToast(toast);
    return toast.id;
  }

  success(message: string, title?: string, options?: Partial<ToastConfig>): string {
    return this.show({
      type: ToastType.SUCCESS,
      message,
      title,
      ...options
    });
  }

  error(message: string, title?: string, options?: Partial<ToastConfig>): string {
    return this.show({
      type: ToastType.ERROR,
      message,
      title,
      duration: 0, // Errors should not auto-dismiss
      ...options
    });
  }

  warning(message: string, title?: string, options?: Partial<ToastConfig>): string {
    return this.show({
      type: ToastType.WARNING,
      message,
      title,
      ...options
    });
  }

  info(message: string, title?: string, options?: Partial<ToastConfig>): string {
    return this.show({
      type: ToastType.INFO,
      message,
      title,
      ...options
    });
  }

  dismiss(id: string): void {
    const currentToasts = this.toasts$.value;
    const updatedToasts = currentToasts.filter(toast => toast.id !== id);
    this.toasts$.next(updatedToasts);
  }

  dismissAll(): void {
    this.toasts$.next([]);
  }

  updateToast(id: string, updates: Partial<ToastConfig>): void {
    const currentToasts = this.toasts$.value;
    const updatedToasts = currentToasts.map(toast => 
      toast.id === id ? { ...toast, ...updates } : toast
    );
    this.toasts$.next(updatedToasts);
  }

  setDefaultConfig(config: Partial<ToastConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }
}