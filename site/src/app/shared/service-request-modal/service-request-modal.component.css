/* Modal Customizado de Solicitação de Serviço */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  overflow-y: auto;
}

.custom-modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.custom-modal-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  transform: translateY(-50px);
  transition: transform 0.3s ease;
}

.custom-modal-overlay.show .custom-modal-container {
  transform: translateY(0);
}

.custom-modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.custom-modal-header {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-modal-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.custom-modal-body {
  padding: 30px;
}

.custom-modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Indicadores de Progresso */
.step-indicators {
  margin-bottom: 30px;
}

.step-tabs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.step-tab {
  flex: 1;
  text-align: center;
  padding: 12px 15px;
  background-color: #e9ecef;
  color: #6c757d;
  font-weight: 500;
  border-radius: 6px;
  margin: 0 5px;
  transition: all 0.3s ease;
  position: relative;
}

.step-tab.active {
  background-color: #007bff;
  color: white;
}

.step-tab.completed {
  background-color: #28a745;
  color: white;
}

.progress {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
}

.progress-bar {
  transition: width 0.3s ease;
  border-radius: 3px;
}

/* Formulários */
.step-content {
  min-height: 400px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.form-control {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 12px 15px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  height: 48px; /* Altura fixa para consistência */
  width: 100%;
  box-sizing: border-box;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
}

/* Estilos específicos para select */
select.form-control {
  height: 48px; /* Mesma altura dos inputs */
  padding: 12px 15px;
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

select.form-control:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Estilos específicos para textarea */
textarea.form-control {
  height: auto;
  min-height: 100px;
  resize: vertical;
  padding: 12px 15px;
}

/* Radio Buttons Customizados */
.radio-group {
  display: flex;
  gap: 30px;
  margin-top: 10px;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #007bff;
  border-radius: 50%;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  background-color: #007bff;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

/* Checkboxes Customizados */
.checkbox-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #333;
  margin-top: 15px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

/* Descrições dos Serviços */
.service-description {
  margin-top: 10px;
  margin-left: 30px;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-left: 4px solid #007bff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.checkbox-group .form-group {
  margin-bottom: 0;
}

.checkbox-option input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #007bff;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom {
  background-color: #007bff;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Botões */
.btn {
  padding: 12px 25px;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:hover {
  background-color: #1e7e34;
  border-color: #1e7e34;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #545b62;
  border-color: #545b62;
}

/* Spinner customizado */
.custom-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Animação de entrada do modal */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .custom-modal-overlay {
    padding: 10px;
  }

  .custom-modal-header,
  .custom-modal-body,
  .custom-modal-footer {
    padding: 20px;
  }

  .step-tabs {
    flex-direction: column;
    gap: 10px;
  }

  .step-tab {
    margin: 0;
    font-size: 14px;
    padding: 10px;
  }

  .radio-group {
    flex-direction: column;
    gap: 15px;
  }

  .custom-modal-footer {
    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .custom-modal-title {
    font-size: 20px;
  }

  .step-content {
    min-height: 300px;
  }

  .form-control {
    padding: 10px 12px;
    height: 44px; /* Altura reduzida para mobile */
  }

  select.form-control {
    height: 44px;
    padding: 10px 12px;
    padding-right: 36px;
  }

  textarea.form-control {
    height: auto;
    min-height: 80px;
    padding: 10px 12px;
  }

  .custom-modal-container {
    margin: 0;
  }

  .custom-modal-overlay {
    padding: 5px;
  }
}

/* Animações */
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Close button customizado */
.custom-close-btn {
  background: none;
  border: none;
  font-size: 28px;
  font-weight: 300;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease, background-color 0.3s ease;
  border-radius: 50%;
}

.custom-close-btn:hover {
  color: #333;
  background-color: rgba(0, 0, 0, 0.1);
}

.custom-close-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Validação visual */
.is-invalid {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Toast Customizado */
.custom-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border-left: 4px solid;
}

.custom-toast-success {
  border-left-color: #28a745;
}

.custom-toast-error {
  border-left-color: #dc3545;
}

.custom-toast-header {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.custom-toast-success .custom-toast-header {
  color: #28a745;
}

.custom-toast-error .custom-toast-header {
  color: #dc3545;
}

.custom-toast-body {
  padding: 12px 16px;
  color: #333;
  line-height: 1.4;
}

.custom-toast-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.custom-toast-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}
