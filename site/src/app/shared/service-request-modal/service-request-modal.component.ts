import { Component, OnInit, OnDestroy, ElementRef, ViewChild, Injectable } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { ServiceRequestService, ServiceRequestResponse } from '../services/service-request.service';



// Serviço para gerenciar o modal globalmente
@Injectable({
  providedIn: 'root'
})
export class ServiceRequestModalService {
  private modalComponent: ServiceRequestModalComponent | null = null;

  registerModal(component: ServiceRequestModalComponent) {
    this.modalComponent = component;
  }

  openModal() {
    if (this.modalComponent) {
      this.modalComponent.openModal();
    }
  }

  closeModal() {
    if (this.modalComponent) {
      this.modalComponent.closeModal();
    }
  }
}

// Validadores customizados
export class CustomValidators {
  static cpf(control: AbstractControl): ValidationErrors | null {
    const cpf = control.value?.replace(/\D/g, '');
    if (!cpf || cpf.length !== 11) {
      return { cpf: true };
    }

    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1{10}$/.test(cpf)) {
      return { cpf: true };
    }

    // Validação dos dígitos verificadores
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i);
    }
    let remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(9))) return { cpf: true };

    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i);
    }
    remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(10))) return { cpf: true };

    return null;
  }

  static cep(control: AbstractControl): ValidationErrors | null {
    const cep = control.value?.replace(/\D/g, '');
    if (!cep || cep.length !== 8) {
      return { cep: true };
    }
    return null;
  }

  static placa(control: AbstractControl): ValidationErrors | null {
    const placa = control.value?.replace(/[^A-Za-z0-9]/g, '');
    if (!placa || (placa.length !== 7 && placa.length !== 8)) {
      return { placa: true };
    }
    return null;
  }

  static celular(control: AbstractControl): ValidationErrors | null {
    const celular = control.value?.replace(/\D/g, '');
    if (!celular || celular.length < 10 || celular.length > 11) {
      return { celular: true };
    }
    return null;
  }

  static cnpj(control: AbstractControl): ValidationErrors | null {
    const cnpj = control.value?.replace(/\D/g, '');
    if (!cnpj || cnpj.length !== 14) {
      return { cnpj: true };
    }

    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1{13}$/.test(cnpj)) {
      return { cnpj: true };
    }

    // Validação dos dígitos verificadores
    let tamanho = cnpj.length - 2;
    let numeros = cnpj.substring(0, tamanho);
    let digitos = cnpj.substring(tamanho);
    let soma = 0;
    let pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
      soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
      if (pos < 2) pos = 9;
    }

    let resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado !== parseInt(digitos.charAt(0))) return { cnpj: true };

    tamanho = tamanho + 1;
    numeros = cnpj.substring(0, tamanho);
    soma = 0;
    pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
      soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
      if (pos < 2) pos = 9;
    }

    resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado !== parseInt(digitos.charAt(1))) return { cnpj: true };

    return null;
  }
}

export interface ServiceRequestData {
  // Informações do Cliente
  clientType: 'fisica' | 'juridica';
  nomeCompleto: string;
  cpf: string;
  email: string;
  celular: string;
  cep: string;
  endereco: string;
  numero: string;
  complemento: string;
  bairro: string;
  cidade: string;
  estado: string;
  criarUsuario: boolean;

  // Campos específicos para Pessoa Jurídica
  nomeFantasia?: string;
  razaoSocial?: string;
  cnpj?: string;
  inscricaoEstadual?: string;

  // Informações do Veículo
  enderecoVeiculo: 'proprio' | 'outro';
  placa: string;
  marca: string;
  modelo: string;
  cor: string;
  nomeProprietario: string;
  celularProprietario: string;
  tipoServico: 'cautelar' | 'vistoria';

  // Campos específicos para endereço do veículo (quando enderecoVeiculo === 'outro')
  cepVeiculo?: string;
  enderecoVeiculoCompleto?: string;
  numeroVeiculo?: string;
  complementoVeiculo?: string;
  bairroVeiculo?: string;
  cidadeVeiculo?: string;
  estadoVeiculo?: string;

  // Pagamento
  metodoPagamento: string;
  observacoes: string;
}

@Component({
  selector: 'app-service-request-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './service-request-modal.component.html',
  styleUrls: ['./service-request-modal.component.css']
})
export class ServiceRequestModalComponent implements OnInit, OnDestroy {
  @ViewChild('modalElement', { static: true }) modalElement!: ElementRef;

  currentStep: number = 1;
  totalSteps: number = 3;
  isSubmitting: boolean = false;
  isModalOpen: boolean = false;

  clientForm!: FormGroup;
  vehicleForm!: FormGroup;
  paymentForm!: FormGroup;

  // Descrições dos serviços
  serviceDescriptions = {
    cautelarVeiculo: 'A análise cautelar é um procedimento detalhado que verifica a procedência do veículo. Avaliamos o histórico de sinistros, leilões, roubos/furtos, alienações, adulterações em chassis e motores, entre outros pontos cruciais.',
    vistoria: 'A vistoria é uma inspeção visual e técnica do estado físico do veículo. Verificamos a originalidade de peças, nível de conservação, pinturas, soldas, e integridade estrutural. É um serviço indispensável para avaliar o valor justo e a segurança do carro.'
  };

  // Controle de exibição das descrições
  showCautelarDescription: boolean = false;
  showVistoriaDescription: boolean = false;

  // Estados brasileiros
  estados = [
    { sigla: 'AC', nome: 'Acre' },
    { sigla: 'AL', nome: 'Alagoas' },
    { sigla: 'AP', nome: 'Amapá' },
    { sigla: 'AM', nome: 'Amazonas' },
    { sigla: 'BA', nome: 'Bahia' },
    { sigla: 'CE', nome: 'Ceará' },
    { sigla: 'DF', nome: 'Distrito Federal' },
    { sigla: 'ES', nome: 'Espírito Santo' },
    { sigla: 'GO', nome: 'Goiás' },
    { sigla: 'MA', nome: 'Maranhão' },
    { sigla: 'MT', nome: 'Mato Grosso' },
    { sigla: 'MS', nome: 'Mato Grosso do Sul' },
    { sigla: 'MG', nome: 'Minas Gerais' },
    { sigla: 'PA', nome: 'Pará' },
    { sigla: 'PB', nome: 'Paraíba' },
    { sigla: 'PR', nome: 'Paraná' },
    { sigla: 'PE', nome: 'Pernambuco' },
    { sigla: 'PI', nome: 'Piauí' },
    { sigla: 'RJ', nome: 'Rio de Janeiro' },
    { sigla: 'RN', nome: 'Rio Grande do Norte' },
    { sigla: 'RS', nome: 'Rio Grande do Sul' },
    { sigla: 'RO', nome: 'Rondônia' },
    { sigla: 'RR', nome: 'Roraima' },
    { sigla: 'SC', nome: 'Santa Catarina' },
    { sigla: 'SP', nome: 'São Paulo' },
    { sigla: 'SE', nome: 'Sergipe' },
    { sigla: 'TO', nome: 'Tocantins' }
  ];

  constructor(
    private fb: FormBuilder,
    private modalService: ServiceRequestModalService,
    private serviceRequestService: ServiceRequestService
  ) {
    this.initializeForms();
  }

  ngOnInit() {
    this.setupModal();
    this.modalService.registerModal(this);
    this.setupFormWatchers();
  }

  ngOnDestroy() {
    // Limpar event listeners se necessário
    document.removeEventListener('keydown', this.handleEscapeKey);
  }

  private initializeForms() {
    // Formulário de Informações do Cliente
    this.clientForm = this.fb.group({
      clientType: ['fisica', Validators.required],
      nomeCompleto: ['', [Validators.required, Validators.minLength(3)]],
      cpf: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      celular: ['', Validators.required],
      cep: ['', Validators.required],
      endereco: ['', Validators.required],
      numero: ['', Validators.required],
      complemento: [''],
      bairro: ['', Validators.required],
      cidade: ['', Validators.required],
      estado: ['', Validators.required],
      criarUsuario: [true],

      // Campos específicos para Pessoa Jurídica
      nomeFantasia: [''],
      razaoSocial: [''],
      cnpj: [''],
      inscricaoEstadual: ['']
    });

    // Formulário de Informações do Veículo
    this.vehicleForm = this.fb.group({
      placa: ['', Validators.required],
      marca: ['', Validators.required],
      modelo: ['', Validators.required],
      cor: ['', Validators.required],
      nomeProprietario: ['', [Validators.required, Validators.minLength(3)]],
      celularProprietario: ['', Validators.required],
      enderecoVeiculo: ['proprio', Validators.required],
      cepVeiculo: [''],
      enderecoVeiculoCompleto: [''],
      numeroVeiculo: [''],
      complementoVeiculo: [''],
      bairroVeiculo: [''],
      cidadeVeiculo: [''],
      estadoVeiculo: ['']
    });

    // Formulário de Pagamento
    this.paymentForm = this.fb.group({
      metodoPagamento: ['', Validators.required],
      observacoes: [''],
      tipoServico: ['', Validators.required]
    });
  }

  private setupFormWatchers() {
    // Watcher para CEP - buscar endereço automaticamente
    this.clientForm.get('cep')?.valueChanges.subscribe(cep => {
      if (cep && cep.replace(/\D/g, '').length === 8) {
        this.buscarEnderecoPorCep(cep);
      }
    });

    // Watcher para CEP do veículo - buscar endereço automaticamente
    this.vehicleForm.get('cepVeiculo')?.valueChanges.subscribe(cep => {
      if (cep && cep.replace(/\D/g, '').length === 8) {
        this.buscarEnderecoPorCepVeiculo(cep);
      }
    });

    // Watcher para tipo de cliente - ajustar validações e aplicar máscaras
    this.clientForm.get('clientType')?.valueChanges.subscribe(type => {
      this.updateClientTypeValidations(type);

      // Aplicar máscaras nos campos que aparecem/desaparecem baseado no tipo
      setTimeout(() => {
        if (type === 'juridica') {
          // Aplicar máscaras específicas para pessoa jurídica
          this.applyMaskToField('cnpj', this.cnpjMask);
          this.applyMaskToField('inscricaoEstadual', this.inscricaoEstadualMask);
        }
        // CPF sempre está disponível para pessoa física
        this.applyMaskToField('cpf', this.cpfMask);
      }, 100);
    });

    // Watcher para endereço do veículo - ajustar validações
    this.vehicleForm.get('enderecoVeiculo')?.valueChanges.subscribe(tipo => {
      this.updateVehicleAddressValidations(tipo);
    });

    // Watcher para tipo de serviço - mostrar/ocultar descrições
    this.paymentForm.get('tipoServico')?.valueChanges.subscribe(tipo => {
      this.showCautelarDescription = tipo === 'cautelar';
      this.showVistoriaDescription = tipo === 'vistoria';
    });

    // Aplicar máscaras nos campos
    this.applyMasks();
  }

  private applyMasks() {
    // Implementar máscaras nativas para CPF, CEP, Celular, Placa, CNPJ
    // Usar múltiplas tentativas para garantir que os elementos estejam disponíveis
    this.tryApplyMasks(0);
  }

  private tryApplyMasks(attempt: number) {
    const maxAttempts = 5;
    const delay = 200 * (attempt + 1); // Aumentar delay progressivamente

    setTimeout(() => {
      // Aplicar máscaras apenas nos campos que estão visíveis no DOM
      const fieldsToApply = [
        // Campos sempre visíveis
        { id: 'cep', mask: this.cepMask },
        { id: 'cepVeiculo', mask: this.cepMask },
        { id: 'celular', mask: this.celularMask },
        { id: 'celularProprietario', mask: this.celularMask },
        { id: 'placa', mask: this.placaMask }
      ];

      // Adicionar campos condicionais baseado no tipo de cliente
      const clientType = this.clientForm.get('clientType')?.value;
      if (clientType === 'fisica') {
        fieldsToApply.push({ id: 'cpf', mask: this.cpfMask });
      } else if (clientType === 'juridica') {
        fieldsToApply.push(
          { id: 'cnpj', mask: this.cnpjMask },
          { id: 'inscricaoEstadual', mask: this.inscricaoEstadualMask }
        );
      }

      const fieldsApplied = fieldsToApply.map(field =>
        this.applyMaskToField(field.id, field.mask)
      );

      const successfullyApplied = fieldsApplied.filter(Boolean).length;

      // Se nem todos os campos foram encontrados e ainda temos tentativas, tentar novamente
      if (successfullyApplied < fieldsApplied.length && attempt < maxAttempts) {
        this.tryApplyMasks(attempt + 1);
      }
    }, delay);
  }

  private applyMaskToField(fieldId: string, maskFunction: (value: string) => string): boolean {
    const element = document.getElementById(fieldId) as HTMLInputElement;
    if (element) {
      // Remover listeners anteriores se existirem
      if ((element as any)._maskListener) {
        element.removeEventListener('input', (element as any)._maskListener);
      }

      const maskListener = (e: Event) => {
        const target = e.target as HTMLInputElement;
        const cursorPosition = target.selectionStart;
        const oldValue = target.value;
        const newValue = maskFunction(target.value);

        if (oldValue !== newValue) {
          target.value = newValue;

          // Calcular posição correta do cursor baseada nos dígitos digitados
          if (cursorPosition !== null) {
            const newPosition = this.calculateCursorPosition(oldValue, newValue, cursorPosition);
            target.setSelectionRange(newPosition, newPosition);
          }

          // Disparar evento de mudança para o Angular detectar
          target.dispatchEvent(new Event('input', { bubbles: true }));
        }
      };

      element.addEventListener('input', maskListener);
      (element as any)._maskListener = maskListener;

      return true;
    }
    return false;
  }

  /**
   * Calcula a posição correta do cursor após aplicar a máscara
   * Baseado na quantidade de dígitos antes da posição original do cursor
   */
  private calculateCursorPosition(oldValue: string, newValue: string, oldCursorPosition: number): number {
    // Contar quantos dígitos existem antes da posição do cursor no valor antigo
    const digitsBeforeCursor = oldValue.substring(0, oldCursorPosition).replace(/\D/g, '').length;

    // Encontrar a posição no novo valor onde temos a mesma quantidade de dígitos
    let digitCount = 0;
    let newPosition = 0;

    for (let i = 0; i < newValue.length; i++) {
      if (/\d/.test(newValue[i])) {
        digitCount++;
        if (digitCount > digitsBeforeCursor) {
          newPosition = i;
          break;
        }
      }
      newPosition = i + 1;
    }

    return Math.min(newPosition, newValue.length);
  }

  private cpfMask(value: string): string {
    const cleanValue = value.replace(/\D/g, '');
    return cleanValue
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d{1,2})/, '$1-$2')
      .replace(/(-\d{2})\d+?$/, '$1')
      .substring(0, 14); // Limitar a 14 caracteres (XXX.XXX.XXX-XX)
  }

  private cepMask(value: string): string {
    const cleanValue = value.replace(/\D/g, '');
    return cleanValue
      .replace(/(\d{5})(\d)/, '$1-$2')
      .replace(/(-\d{3})\d+?$/, '$1')
      .substring(0, 9); // Limitar a 9 caracteres (XXXXX-XXX)
  }

  private celularMask(value: string): string {
    const cleanValue = value.replace(/\D/g, '');
    if (cleanValue.length <= 10) {
      // Telefone fixo: (XX) XXXX-XXXX
      return cleanValue
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .replace(/(-\d{4})\d+?$/, '$1')
        .substring(0, 14);
    } else {
      // Celular: (XX) XXXXX-XXXX
      return cleanValue
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{5})(\d)/, '$1-$2')
        .replace(/(-\d{4})\d+?$/, '$1')
        .substring(0, 15);
    }
  }

  private placaMask(value: string): string {
    const cleanValue = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    return cleanValue
      .replace(/([A-Z]{3})(\d)/, '$1-$2')
      .replace(/(-\d{4})\w+?$/, '$1')
      .substring(0, 8); // Limitar a 8 caracteres (XXX-XXXX)
  }

  private cnpjMask(value: string): string {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{2})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1/$2')
      .replace(/(\d{4})(\d)/, '$1-$2')
      .replace(/(-\d{2})\d+?$/, '$1');
  }

  private inscricaoEstadualMask(value: string): string {
    // Máscara genérica para inscrição estadual (varia por estado)
    // Aplicando formato básico com pontos e traços
    return value
      .replace(/\D/g, '')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1-$2')
      .replace(/(-\d{1})\d+?$/, '$1');
  }

  private async buscarEnderecoPorCep(cep: string) {
    try {
      const addressData = await this.serviceRequestService.getAddressByCEP(cep);
      this.clientForm.patchValue({
        endereco: addressData.logradouro,
        bairro: addressData.bairro,
        cidade: addressData.localidade,
        estado: addressData.uf
      });
    } catch (error) {
      console.error('Erro ao buscar CEP:', error);
      // Não mostrar erro para o usuário, apenas log
    }
  }

  private async buscarEnderecoPorCepVeiculo(cep: string) {
    try {
      const addressData = await this.serviceRequestService.getAddressByCEP(cep);
      this.vehicleForm.patchValue({
        enderecoVeiculoCompleto: addressData.logradouro,
        bairroVeiculo: addressData.bairro,
        cidadeVeiculo: addressData.localidade,
        estadoVeiculo: addressData.uf
      });
    } catch (error) {
      console.error('Erro ao buscar CEP do veículo:', error);
      // Não mostrar erro para o usuário, apenas log
    }
  }

  private updateClientTypeValidations(type: string) {
    const cpfControl = this.clientForm.get('cpf');
    const cnpjControl = this.clientForm.get('cnpj');
    const nomeFantasiaControl = this.clientForm.get('nomeFantasia');
    const razaoSocialControl = this.clientForm.get('razaoSocial');
    const inscricaoEstadualControl = this.clientForm.get('inscricaoEstadual');

    if (type === 'juridica') {
      // Para Pessoa Jurídica - CPF não é obrigatório, CNPJ e outros campos são
      cpfControl?.clearValidators();
      cnpjControl?.setValidators([Validators.required, CustomValidators.cnpj]);
      nomeFantasiaControl?.setValidators([Validators.required, Validators.minLength(2)]);
      razaoSocialControl?.setValidators([Validators.required, Validators.minLength(2)]);
      inscricaoEstadualControl?.setValidators([Validators.required]);

      // Limpar CPF quando mudar para jurídica
      cpfControl?.setValue('');
    } else {
      // Para Pessoa Física - CPF é obrigatório, campos de PJ não são
      cpfControl?.setValidators([Validators.required, CustomValidators.cpf]);
      cnpjControl?.clearValidators();
      nomeFantasiaControl?.clearValidators();
      razaoSocialControl?.clearValidators();
      inscricaoEstadualControl?.clearValidators();

      // Limpar campos de PJ quando mudar para física
      cnpjControl?.setValue('');
      nomeFantasiaControl?.setValue('');
      razaoSocialControl?.setValue('');
      inscricaoEstadualControl?.setValue('');
    }

    // Atualizar validações
    cpfControl?.updateValueAndValidity();
    cnpjControl?.updateValueAndValidity();
    nomeFantasiaControl?.updateValueAndValidity();
    razaoSocialControl?.updateValueAndValidity();
    inscricaoEstadualControl?.updateValueAndValidity();
  }

  private updateVehicleAddressValidations(tipo: string) {
    const cepControl = this.vehicleForm.get('cepVeiculo');
    const enderecoControl = this.vehicleForm.get('enderecoVeiculoCompleto');
    const numeroControl = this.vehicleForm.get('numeroVeiculo');
    const bairroControl = this.vehicleForm.get('bairroVeiculo');
    const cidadeControl = this.vehicleForm.get('cidadeVeiculo');
    const estadoControl = this.vehicleForm.get('estadoVeiculo');

    if (tipo === 'outro') {
      // Adicionar validações obrigatórias para campos de endereço
      cepControl?.setValidators([Validators.required]);
      enderecoControl?.setValidators([Validators.required]);
      numeroControl?.setValidators([Validators.required]);
      bairroControl?.setValidators([Validators.required]);
      cidadeControl?.setValidators([Validators.required]);
      estadoControl?.setValidators([Validators.required]);
    } else {
      // Remover validações e limpar campos
      cepControl?.clearValidators();
      enderecoControl?.clearValidators();
      numeroControl?.clearValidators();
      bairroControl?.clearValidators();
      cidadeControl?.clearValidators();
      estadoControl?.clearValidators();

      // Limpar valores dos campos
      this.vehicleForm.patchValue({
        cepVeiculo: '',
        enderecoVeiculoCompleto: '',
        numeroVeiculo: '',
        complementoVeiculo: '',
        bairroVeiculo: '',
        cidadeVeiculo: '',
        estadoVeiculo: ''
      });
    }

    // Atualizar validações
    cepControl?.updateValueAndValidity();
    enderecoControl?.updateValueAndValidity();
    numeroControl?.updateValueAndValidity();
    bairroControl?.updateValueAndValidity();
    cidadeControl?.updateValueAndValidity();
    estadoControl?.updateValueAndValidity();
  }

  private setupModal() {
    // Configurar event listeners para o modal customizado
    document.addEventListener('keydown', this.handleEscapeKey.bind(this));
  }

  openModal() {
    this.isModalOpen = true;
    // Prevenir scroll do body quando modal estiver aberto
    document.body.style.overflow = 'hidden';

    // Posicionar modal na posição atual do scroll
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    if (this.modalElement?.nativeElement) {
      this.modalElement.nativeElement.style.paddingTop = `${Math.max(20, scrollTop + 20)}px`;
    }
  }

  closeModal() {
    this.isModalOpen = false;
    // Restaurar scroll do body
    document.body.style.overflow = '';
    this.resetModal();
  }

  onOverlayClick(event: Event) {
    // Fechar modal apenas se clicar no overlay (não no conteúdo)
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }

  private handleEscapeKey(event: KeyboardEvent) {
    if (event.key === 'Escape' && this.isModalOpen) {
      this.closeModal();
    }
  }

  private resetModal() {
    this.currentStep = 1;
    this.clientForm.reset({
      clientType: 'fisica',
      criarUsuario: true
    });
    this.vehicleForm.reset({
      enderecoVeiculo: 'proprio'
    });
    this.paymentForm.reset({
      tipoServico: ''
    });
  }

  nextStep() {
    if (this.validateCurrentStep()) {
      if (this.currentStep < this.totalSteps) {
        this.currentStep++;

        // Aplicar máscaras específicas quando entrar na etapa 2 (veículo)
        if (this.currentStep === 2) {
          setTimeout(() => {
            this.applyMaskToField('celularProprietario', this.celularMask);
            this.applyMaskToField('placa', this.placaMask);
            this.applyMaskToField('cepVeiculo', this.cepMask);
          }, 100);
        }
      }
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  private validateCurrentStep(): boolean {
    switch (this.currentStep) {
      case 1:
        if (this.clientForm.invalid) {
          this.markFormGroupTouched(this.clientForm);
          return false;
        }
        break;
      case 2:
        if (this.vehicleForm.invalid) {
          this.markFormGroupTouched(this.vehicleForm);
          return false;
        }
        break;
      case 3:
        if (this.paymentForm.invalid) {
          this.markFormGroupTouched(this.paymentForm);
          return false;
        }
        break;
    }
    return true;
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  isFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  async onSubmit() {
    if (this.validateCurrentStep() && !this.isSubmitting) {
      this.isSubmitting = true;

      try {
        const formData: ServiceRequestData = {
          ...this.clientForm.value,
          ...this.vehicleForm.value,
          ...this.paymentForm.value
        };

        // Dados formatados para envio

        // Enviar dados usando o serviço
        const response = await this.serviceRequestService.submitServiceRequest(formData).toPromise();

        if (response?.success) {
          this.showSuccessMessage(response.message, response.requestId);

          // Fechar modal após 3 segundos
          setTimeout(() => {
            this.closeModal();
          }, 3000);
        } else {
          throw new Error(response?.message || 'Erro desconhecido');
        }

      } catch (error: any) {
        this.showErrorMessage(error.message || 'Erro ao enviar solicitação');
      } finally {
        this.isSubmitting = false;
      }
    }
  }

  private showSuccessMessage(message: string, requestId?: string) {
    const fullMessage = requestId
      ? `${message}\n\nNúmero da solicitação: ${requestId}`
      : message;

    this.showCustomToast(fullMessage, 'success');
  }

  private showErrorMessage(message: string = 'Erro ao enviar solicitação. Tente novamente.') {
    this.showCustomToast(message, 'error');
  }

  private showCustomToast(message: string, type: 'success' | 'error') {
    const toast = document.createElement('div');
    toast.className = `custom-toast custom-toast-${type}`;
    toast.innerHTML = `
      <div class="custom-toast-header">
        <strong>${type === 'success' ? '✓ Sucesso!' : '✗ Erro!'}</strong>
        <button type="button" class="custom-toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
      <div class="custom-toast-body">${message.replace('\n\n', '<br><br>')}</div>
    `;

    // Adicionar estilos inline para garantir que funcionem
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      min-width: 300px;
      max-width: 400px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    `;

    document.body.appendChild(toast);

    // Animar entrada
    setTimeout(() => {
      toast.style.opacity = '1';
      toast.style.transform = 'translateX(0)';
    }, 10);

    // Remover automaticamente após 5 segundos
    setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (toast.parentElement) {
          toast.parentElement.removeChild(toast);
        }
      }, 300);
    }, 5000);
  }

  getStepTitle(): string {
    switch (this.currentStep) {
      case 1: return 'Informações do Cliente';
      case 2: return 'Informações do Veículo';
      case 3: return 'Pagamento';
      default: return '';
    }
  }

  getProgressPercentage(): number {
    return (this.currentStep / this.totalSteps) * 100;
  }

  get shouldShowVehicleAddressFields(): boolean {
    return this.vehicleForm.get('enderecoVeiculo')?.value === 'outro';
  }

  get shouldShowJuridicaFields(): boolean {
    return this.clientForm.get('clientType')?.value === 'juridica';
  }
}
