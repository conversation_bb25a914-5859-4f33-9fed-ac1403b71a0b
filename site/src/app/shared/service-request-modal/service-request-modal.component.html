<!-- Modal Customizado de Solicitação de Serviço -->
<div class="custom-modal-overlay" #modalElement [class.show]="isModalOpen" (click)="onOverlayClick($event)">
  <div class="custom-modal-container" (click)="$event.stopPropagation()">
    <div class="custom-modal-content">
      <!-- Header do Modal -->
      <div class="custom-modal-header">
        <h4 class="custom-modal-title">SOLICITAÇÃO DE SERVIÇO</h4>
        <button type="button" class="custom-close-btn" (click)="closeModal()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>

      <!-- Body do Modal -->
      <div class="custom-modal-body">
        <!-- Indicadores de Progresso -->
        <div class="step-indicators mb-4">
          <div class="step-tabs">
            <div class="step-tab" [class.active]="currentStep === 1" [class.completed]="currentStep > 1">
              Informações do Cliente
            </div>
            <div class="step-tab" [class.active]="currentStep === 2" [class.completed]="currentStep > 2">
              Informações do Veículo
            </div>
            <div class="step-tab" [class.active]="currentStep === 3">
              Pagamento
            </div>
          </div>
          <div class="progress mt-2">
            <div class="progress-bar bg-primary" role="progressbar" [style.width.%]="getProgressPercentage()"
              [attr.aria-valuenow]="getProgressPercentage()" aria-valuemin="0" aria-valuemax="100">
            </div>
          </div>
        </div>

        <!-- Etapa 1: Informações do Cliente -->
        <div *ngIf="currentStep === 1" class="step-content">
          <form [formGroup]="clientForm">
            <!-- Tipo de Pessoa -->
            <div class="form-group mb-3">
              <label class="form-label">Tipo Pessoa:</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input type="radio" formControlName="clientType" value="fisica">
                  <span class="radio-custom"></span>
                  Pessoa Física
                </label>
                <label class="radio-option">
                  <input type="radio" formControlName="clientType" value="juridica">
                  <span class="radio-custom"></span>
                  Pessoa Jurídica
                </label>
              </div>
            </div>

            <!-- Campos para Pessoa Física -->
            <div *ngIf="!shouldShowJuridicaFields">
              <!-- Linha 1: Nome, CPF, Email -->
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="nomeCompleto">Nome Completo</label>
                    <input type="text" id="nomeCompleto" class="form-control" formControlName="nomeCompleto"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'nomeCompleto')">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="cpf">CPF</label>
                    <input type="text" id="cpf" class="form-control" formControlName="cpf"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'cpf')">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" class="form-control" formControlName="email"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'email')">
                  </div>
                </div>
              </div>
            </div>

            <!-- Campos para Pessoa Jurídica -->
            <div *ngIf="shouldShowJuridicaFields">
              <!-- Linha 1: Nome Fantasia, Razão Social, Inscrição Estadual -->
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="nomeFantasia">Nome Fantasia</label>
                    <input type="text" id="nomeFantasia" class="form-control" formControlName="nomeFantasia"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'nomeFantasia')">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="razaoSocial">Razão Social</label>
                    <input type="text" id="razaoSocial" class="form-control" formControlName="razaoSocial"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'razaoSocial')">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="inscricaoEstadual">Inscrição Estadual</label>
                    <input type="text" id="inscricaoEstadual" class="form-control" formControlName="inscricaoEstadual"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'inscricaoEstadual')">
                  </div>
                </div>
              </div>

              <!-- Linha 2: Email e CNPJ -->
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="cnpj">CNPJ</label>
                    <input type="text" id="cnpj" class="form-control" formControlName="cnpj"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'cnpj')">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" class="form-control" formControlName="email"
                      [class.is-invalid]="isFieldInvalid(clientForm, 'email')">
                  </div>
                </div>
              </div>
            </div>

            <!-- Linha 3: Celular, CEP, Endereço (comum para ambos os tipos) -->
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="celular">Celular</label>
                  <input type="text" id="celular" class="form-control" formControlName="celular"
                    [class.is-invalid]="isFieldInvalid(clientForm, 'celular')">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="cep">CEP</label>
                  <input type="text" id="cep" class="form-control" formControlName="cep"
                    [class.is-invalid]="isFieldInvalid(clientForm, 'cep')">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="endereco">Endereço</label>
                  <input type="text" id="endereco" class="form-control" formControlName="endereco"
                    [class.is-invalid]="isFieldInvalid(clientForm, 'endereco')">
                </div>
              </div>
            </div>

            <!-- Linha 4: Número, Complemento, Bairro (comum para ambos os tipos) -->
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="numero">Número</label>
                  <input type="text" id="numero" class="form-control" formControlName="numero"
                    [class.is-invalid]="isFieldInvalid(clientForm, 'numero')">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="complemento">Complemento</label>
                  <input type="text" id="complemento" class="form-control" formControlName="complemento">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="bairro">Bairro</label>
                  <input type="text" id="bairro" class="form-control" formControlName="bairro"
                    [class.is-invalid]="isFieldInvalid(clientForm, 'bairro')">
                </div>
              </div>
            </div>

            <!-- Linha 5: Cidade, Estado (comum para ambos os tipos) -->
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="cidade">Cidade</label>
                  <input type="text" id="cidade" class="form-control" formControlName="cidade"
                    [class.is-invalid]="isFieldInvalid(clientForm, 'cidade')">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="estado">Estado</label>
                  <select id="estado" class="form-control" formControlName="estado"
                    [class.is-invalid]="isFieldInvalid(clientForm, 'estado')">
                    <option value="">Selecione o estado</option>
                    <option *ngFor="let estado of estados" [value]="estado.sigla">
                      {{ estado.nome }}
                    </option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Checkbox -->
            <div class="form-group">
              <label class="checkbox-option">
                <input type="checkbox" formControlName="criarUsuario">
                <span class="checkbox-custom"></span>
                Permitir criação do meu usuário na AutoVPro
              </label>
            </div>
          </form>
        </div>

        <!-- Etapa 2: Informações do Veículo -->
        <div *ngIf="currentStep === 2" class="step-content">
          <form [formGroup]="vehicleForm">
            <!-- Linha 1: Placa, Marca, Modelo -->
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="placa">Placa</label>
                  <input type="text" id="placa" class="form-control" formControlName="placa"
                    [class.is-invalid]="isFieldInvalid(vehicleForm, 'placa')">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="marca">Marca</label>
                  <input type="text" id="marca" class="form-control" formControlName="marca"
                    [class.is-invalid]="isFieldInvalid(vehicleForm, 'marca')">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="modelo">Modelo</label>
                  <input type="text" id="modelo" class="form-control" formControlName="modelo"
                    [class.is-invalid]="isFieldInvalid(vehicleForm, 'modelo')">
                </div>
              </div>
            </div>

            <!-- Linha 2: Cor, Nome Proprietário, Celular Proprietário -->
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="cor">Cor</label>
                  <input type="text" id="cor" class="form-control" formControlName="cor"
                    [class.is-invalid]="isFieldInvalid(vehicleForm, 'cor')">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="nomeProprietario">Nome Proprietário</label>
                  <input type="text" id="nomeProprietario" class="form-control" formControlName="nomeProprietario"
                    [class.is-invalid]="isFieldInvalid(vehicleForm, 'nomeProprietario')">
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="celularProprietario">Celular Proprietário</label>
                  <input type="text" id="celularProprietario" class="form-control" formControlName="celularProprietario"
                    [class.is-invalid]="isFieldInvalid(vehicleForm, 'celularProprietario')">
                </div>
              </div>
            </div>

            <!-- Endereço do Veículo -->
            <div class="form-group mb-3">
              <label class="form-label">Endereço do veículo:</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input type="radio" formControlName="enderecoVeiculo" value="proprio">
                  <span class="radio-custom"></span>
                  Próprio Endereço
                </label>
                <label class="radio-option">
                  <input type="radio" formControlName="enderecoVeiculo" value="outro">
                  <span class="radio-custom"></span>
                  Outro Endereço
                </label>
              </div>
            </div>

            <!-- Campos de Endereço do Veículo (aparecem apenas quando "Outro Endereço" é selecionado) -->
            <div *ngIf="shouldShowVehicleAddressFields">
              <!-- Linha 1: CEP, Endereço, Número -->
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="cepVeiculo">CEP</label>
                    <input type="text" id="cepVeiculo" class="form-control" formControlName="cepVeiculo"
                      [class.is-invalid]="isFieldInvalid(vehicleForm, 'cepVeiculo')">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="enderecoVeiculoCompleto">Endereço</label>
                    <input type="text" id="enderecoVeiculoCompleto" class="form-control"
                      formControlName="enderecoVeiculoCompleto"
                      [class.is-invalid]="isFieldInvalid(vehicleForm, 'enderecoVeiculoCompleto')">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label for="numeroVeiculo">Número</label>
                    <input type="text" id="numeroVeiculo" class="form-control" formControlName="numeroVeiculo"
                      [class.is-invalid]="isFieldInvalid(vehicleForm, 'numeroVeiculo')">
                  </div>
                </div>
              </div>

              <!-- Linha 2: Complemento, Bairro -->
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="complementoVeiculo">Complemento</label>
                    <input type="text" id="complementoVeiculo" class="form-control"
                      formControlName="complementoVeiculo">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="bairroVeiculo">Bairro</label>
                    <input type="text" id="bairroVeiculo" class="form-control" formControlName="bairroVeiculo"
                      [class.is-invalid]="isFieldInvalid(vehicleForm, 'bairroVeiculo')">
                  </div>
                </div>
              </div>

              <!-- Linha 3: Cidade, Estado -->
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="cidadeVeiculo">Cidade</label>
                    <input type="text" id="cidadeVeiculo" class="form-control" formControlName="cidadeVeiculo"
                      [class.is-invalid]="isFieldInvalid(vehicleForm, 'cidadeVeiculo')">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="estadoVeiculo">Estado</label>
                    <select id="estadoVeiculo" class="form-control" formControlName="estadoVeiculo"
                      [class.is-invalid]="isFieldInvalid(vehicleForm, 'estadoVeiculo')">
                      <option value="">Selecione o estado</option>
                      <option *ngFor="let estado of estados" [value]="estado.sigla">
                        {{ estado.nome }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>


          </form>
        </div>

        <!-- Etapa 3: Pagamento -->
        <div *ngIf="currentStep === 3" class="step-content">
          <form [formGroup]="paymentForm">
            <!-- Tipo de Serviço -->
            <div class="form-group mb-3">
              <label class="form-label">Tipo de Serviço:</label>
              <div class="radio-group">
                <div class="form-group">
                  <label class="radio-option">
                    <input type="radio" formControlName="tipoServico" value="cautelar">
                    <span class="radio-custom"></span>
                    Cautelar de Veículo
                  </label>
                  <div *ngIf="showCautelarDescription" class="service-description">
                    {{ serviceDescriptions.cautelarVeiculo }}
                  </div>
                </div>
                <div class="form-group">
                  <label class="radio-option">
                    <input type="radio" formControlName="tipoServico" value="vistoria">
                    <span class="radio-custom"></span>
                    Vistoria
                  </label>
                  <div *ngIf="showVistoriaDescription" class="service-description">
                    {{ serviceDescriptions.vistoria }}
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">Método de Pagamento</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input type="radio" formControlName="metodoPagamento" value="debito">
                  <span class="radio-custom"></span>
                  Débito
                </label>
                <label class="radio-option">
                  <input type="radio" formControlName="metodoPagamento" value="credito">
                  <span class="radio-custom"></span>
                  Crédito
                </label>
                <label class="radio-option">
                  <input type="radio" formControlName="metodoPagamento" value="pix">
                  <span class="radio-custom"></span>
                  PIX
                </label>
              </div>
            </div>

            <div class="form-group">
              <label for="observacoes">Observações</label>
              <textarea id="observacoes" class="form-control" rows="4" formControlName="observacoes"
                placeholder="Adicione observações adicionais sobre sua solicitação..."></textarea>
            </div>
          </form>
        </div>
      </div>

      <!-- Footer do Modal -->
      <div class="custom-modal-footer">
        <button type="button" class="btn btn-secondary" *ngIf="currentStep > 1" (click)="previousStep()">
          Voltar
        </button>

        <button type="button" class="btn btn-primary" *ngIf="currentStep < totalSteps" (click)="nextStep()">
          Avançar
        </button>

        <button type="button" class="btn btn-success" *ngIf="currentStep === totalSteps" [disabled]="isSubmitting"
          (click)="onSubmit()">
          <span *ngIf="isSubmitting" class="custom-spinner" role="status" aria-hidden="true"></span>
          {{ isSubmitting ? 'Enviando...' : 'Finalizar' }}
        </button>
      </div>
    </div>
  </div>
</div>