/* Team Component Styles - Matching original template */
.row-team {
  background: #fff;
}

.wprt-team .team-item {
  margin-bottom: 30px;
}

.wprt-team .thumb {
  position: relative;
  overflow: hidden;
}

.wprt-team .thumb img {
  width: 100%;
  height: auto;
}

/* Overlay effect on hover */
.wprt-team .thumb:after {
  opacity: 0;
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: #000;
  transform: translate3d(0, 50%, 0);
  transition: all 0.3s;
}

.wprt-team .team-item:hover .thumb:after {
  opacity: 0.6;
  transform: translate3d(0, 0, 0);
}

/* Social icons */
.wprt-team .socials {
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
  text-align: center;
  z-index: 2;
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -17px;
}

.wprt-team .socials li {
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  padding: 0;
  margin: 0 4px;
  transform: translate3d(0, -50%, 0);
  transition: opacity 0.2s, transform 0.35s;
}

.wprt-team .socials li a {
  color: #777;
  background-color: #fff;
  font-size: 15px;
  line-height: 34px;
  width: 34px;
  height: 34px;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease 0s;
}

.wprt-team .socials li a:hover {
  background-color: #1c63b8;
  color: #fff;
}

/* Show socials on hover with staggered animation */
.wprt-team .team-item:hover .socials li {
  opacity: 1;
  visibility: visible;
  transform: translate3d(0, 0, 0);
}

.wprt-team .team-item:hover .socials li:nth-child(4) {
  transition-delay: 0.2s;
}

.wprt-team .team-item:hover .socials li:nth-child(3) {
  transition-delay: 0.15s;
}

.wprt-team .team-item:hover .socials li:nth-child(2) {
  transition-delay: 0.1s;
}

.wprt-team .team-item:hover .socials li:first-child {
  transition-delay: 0.05s;
}

.wprt-team .text-wrap {
  text-align: center;
  padding: 25px 0 0;
}

.wprt-team .name {
  font-size: 16px;
  margin-bottom: 3px;
  text-transform: uppercase;
}

.wprt-team .position {
  font-size: 0.928em;
  text-transform: uppercase;
}

/* Shadow effect */
.wprt-team.has-shadow .team-item .inner {
  background-color: #f7f7f7;
  box-shadow: 1px 1px 0px 0px #ebebeb;
}

.wprt-team.has-shadow .team-item {
  padding-bottom: 1px;
  padding-right: 1px;
}

.wprt-team.has-shadow .text-wrap {
  padding: 27px 30px 20px;
}

/* Responsive styles */
@media only screen and (max-width: 991px) {
  .wprt-team-grid {
    margin: 0 !important;
  }

  .wprt-team-grid .team-row {
    padding: 0 !important;
    margin: 0 !important;
  }

  .wprt-team-grid .team-item {
    padding: 0 !important;
    margin: 0 0 35px !important;
  }

  .wprt-team-grid .team-row:last-child .team-item:last-child {
    margin-bottom: 0 !important;
  }

  .wprt-team-grid.col-2 .team-item,
  .wprt-team-grid.col-3 .team-item {
    width: 100%;
  }

  .wprt-team-grid.col-4 .team-item,
  .wprt-team-grid.col-5 .team-item {
    width: 50%;
  }
}

@media only screen and (max-width: 479px) {
  .wprt-team-grid.col-4 .team-item,
  .wprt-team-grid.col-5 .team-item {
    width: 100%;
  }
}
