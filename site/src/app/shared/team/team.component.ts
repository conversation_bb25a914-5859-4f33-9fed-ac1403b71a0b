import { Component, Input, AfterViewInit, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TeamMember {
  name: string;
  position: string;
  image: string;
  socials: {
    twitter?: string;
    facebook?: string;
    linkedin?: string;
  };
}

@Component({
  selector: 'app-team',
  imports: [CommonModule],
  templateUrl: './team.component.html',
  styleUrl: './team.component.css'
})
export class TeamComponent implements AfterViewInit {
  @Input() members: TeamMember[] = [];
  @Input() title: string = 'Nossa Equipe';
  @Input() subtitle: string = 'Conheça nossa equipe de profissionais';
  @Input() columns: number = 4;
  @Input() hasShadow: boolean = true;
  @Input() hasBullets: boolean = true;
  @Input() autoplay: boolean = false;
  @Input() gap: number = 30;

  constructor(private elementRef: ElementRef) {}

  ngAfterViewInit(): void {
    // Inicializar o carousel do team após a view ser carregada
    setTimeout(() => {
      if (typeof (window as any).$ !== 'undefined') {
        const $ = (window as any).$;
        const teamElement = $(this.elementRef.nativeElement).find('.wprt-team');

        if (teamElement.length && $.fn.owlCarousel) {
          const auto = teamElement.data('auto') || this.autoplay;
          const item = teamElement.data('column') || this.columns;
          const item2 = teamElement.data('column2') || 2;
          const item3 = teamElement.data('column3') || 1;
          const gap = Number(teamElement.data('gap')) || this.gap;

          teamElement.find('.owl-carousel').owlCarousel({
            loop: false,
            margin: gap,
            nav: true,
            navigation: true,
            pagination: true,
            autoplay: auto,
            autoplayTimeout: 5000,
            responsive: {
              0: {
                items: item3
              },
              600: {
                items: item2
              },
              1000: {
                items: item
              }
            }
          });
        }
      }
    }, 100);
  }
}
