import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ServiceRequestData } from '../service-request-modal/service-request-modal.component';
import { environment } from '../../../environments/environment';

export interface ServiceRequestResponse {
  success: boolean;
  message: string;
  requestId?: string;
  errors?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class ServiceRequestService {
  private readonly apiUrl = `${environment.apiUrl}/api`;

  constructor(private http: HttpClient) { }

  /**
   * Submete uma solicitação de serviço
   * Versão simplificada seguindo padrões arquiteturais do projeto
   * Toda lógica de negócio foi movida para o backend
   * @param data Dados da solicitação
   * @returns Observable com a resposta do servidor
   */
  submitServiceRequest(data: ServiceRequestData): Observable<ServiceRequestResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    // ✅ NOVA VERSÃO: Envio direto para endpoint dedicado no backend
    // Toda lógica de resolução de IDs e validação agora está no backend
    return this.http.post<any>(`${this.apiUrl}/attendance/service-request`, data, { headers })
      .pipe(
        map((response: any) => {
          if (response.success) {
            return {
              success: true,
              message: response.message || 'Solicitação enviada com sucesso!',
              requestId: response.requestId
            };
          } else {
            throw new Error(response.message || 'Erro desconhecido');
          }
        }),
        catchError((error: any) => {
          console.error('Erro ao enviar solicitação:', error);
          
          // Tratar diferentes tipos de erro do backend
          let errorMessage = 'Erro ao enviar solicitação. Tente novamente.';
          let errors: string[] = [];

          if (error.error) {
            if (error.error.message) {
              errorMessage = error.error.message;
            }
            if (error.error.errors) {
              errors = Array.isArray(error.error.errors) ? error.error.errors : [error.error.errors];
            }
          } else if (error.message) {
            errorMessage = error.message;
          }

          return new Observable<ServiceRequestResponse>(observer => {
            observer.next({
              success: false,
              message: errorMessage,
              errors: errors.length > 0 ? errors : undefined
            });
            observer.complete();
          });
        })
      );
  }

  /**
   * Busca endereço por CEP
   * ✅ REMOVIDO: Lógica movida para AddressService no backend
   * Mantido apenas para compatibilidade com componentes existentes
   * @deprecated Use backend integration instead
   */
  async getAddressByCEP(cep: string): Promise<any> {
    console.warn('getAddressByCEP is deprecated. Address resolution now handled by backend.');
    
    // Implementação simplificada para não quebrar componentes existentes
    try {
      const cleanCEP = cep.replace(/\D/g, '');
      if (cleanCEP.length !== 8) {
        throw new Error('CEP deve ter 8 dígitos');
      }

      const response = await fetch(`https://viacep.com.br/ws/${cleanCEP}/json/`);
      const data = await response.json();
      
      if (data.erro) {
        throw new Error('CEP não encontrado');
      }

      return {
        logradouro: data.logradouro,
        bairro: data.bairro,
        localidade: data.localidade,
        uf: data.uf,
        complemento: data.complemento
      };
    } catch (error) {
      console.error('Erro ao buscar CEP:', error);
      throw error;
    }
  }

  /**
   * Formata dados para exibição ou log
   * ✅ SIMPLIFICADO: Removida lógica complexa de formatação
   */
  formatServiceRequestData(data: ServiceRequestData): any {
    return {
      cliente: {
        tipo: data.clientType === 'fisica' ? 'Pessoa Física' : 'Pessoa Jurídica',
        nome: data.nomeCompleto,
        documento: data.clientType === 'juridica' ? data.cnpj : data.cpf,
        email: data.email,
        celular: data.celular
      },
      veiculo: {
        placa: data.placa,
        marca: data.marca,
        modelo: data.modelo,
        cor: data.cor,
        proprietario: data.nomeProprietario,
        servico: data.tipoServico
      },
      timestamp: new Date().toISOString()
    };
  }
}

// ✅ REMOVIDO: Todas as funções de validação, resolução de IDs e mapeamento complexo
// Essas responsabilidades foram movidas para o backend seguindo padrões arquiteturais

/*
FUNÇÕES REMOVIDAS (agora no backend):
- resolveCustomerAndCreateAttendance()
- findOrCreateCustomer()
- createNewCustomer() 
- findOrCreateBrand()
- findOrCreateModel()
- convertToAttendanceData()
- validateServiceRequest()
- isValidCPF(), isValidEmail(), isValidPhone(), isValidCEP(), isValidPlaca()
- generateGuid()
- generateRequestId()

BENEFÍCIOS DA REFATORAÇÃO:
✅ Single Responsibility: Service só gerencia HTTP requests
✅ Separation of Concerns: Lógica de negócio no backend
✅ Testability: Código mais simples e testável
✅ Maintainability: Menos código no frontend
✅ Consistency: Segue padrão dos outros services do projeto
✅ Performance: Menos processamento no cliente
*/