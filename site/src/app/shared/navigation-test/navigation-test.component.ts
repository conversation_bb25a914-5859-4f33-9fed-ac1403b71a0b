import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NavigationService } from '../../core/services/navigation.service';
import { PerformanceService } from '../../core/services/performance.service';
import { ScriptLoaderService } from '../../core/services/script-loader.service';
import { ToastService } from '../toast/toast.service';

@Component({
  selector: 'app-navigation-test',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="navigation-test" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.9); color: white; padding: 10px; border-radius: 5px; z-index: 9999; font-size: 11px; max-width: 300px;">
      <h4 style="margin: 0 0 10px 0; color: #4CAF50;">🧪 Debug Panel</h4>

      <!-- Informações de Navegação -->
      <div style="margin-bottom: 8px; border-bottom: 1px solid #333; padding-bottom: 8px;">
        <div><strong>Rota:</strong> {{ getCurrentRoute() }}</div>
        <div><strong>Timestamp:</strong> {{ timestamp }}</div>
      </div>

      <!-- Botões de Navegação -->
      <div style="margin-bottom: 8px;">
        <button
          (click)="testNavigation('/empresa')"
          style="margin: 1px; padding: 3px 6px; background: #2196F3; color: white; border: none; border-radius: 2px; cursor: pointer; font-size: 10px;">
          Empresa
        </button>
        <button
          (click)="testNavigation('/servicos')"
          style="margin: 1px; padding: 3px 6px; background: #2196F3; color: white; border: none; border-radius: 2px; cursor: pointer; font-size: 10px;">
          Serviços
        </button>
        <button
          (click)="testNavigation('/contato')"
          style="margin: 1px; padding: 3px 6px; background: #2196F3; color: white; border: none; border-radius: 2px; cursor: pointer; font-size: 10px;">
          Contato
        </button>
      </div>

      <!-- Controles de Debug -->
      <div style="margin-bottom: 8px;">
        <button
          (click)="forceReinitialize()"
          style="margin: 1px; padding: 3px 6px; background: #FF9800; color: white; border: none; border-radius: 2px; cursor: pointer; font-size: 10px;">
          🔄 Reinit jQuery
        </button>
        <button
          (click)="showPerformance()"
          style="margin: 1px; padding: 3px 6px; background: #9C27B0; color: white; border: none; border-radius: 2px; cursor: pointer; font-size: 10px;">
          📊 Performance
        </button>
      </div>

      <!-- Informações de Scripts -->
      <div style="margin-bottom: 8px; border-bottom: 1px solid #333; padding-bottom: 8px;">
        <div style="font-size: 10px; color: #ccc;">
          <strong>Scripts Carregados:</strong> {{ getLoadedScriptsCount() }}
        </div>
        <div style="font-size: 10px; color: #ccc;">
          <strong>Memória:</strong> {{ getMemoryUsage() }}
        </div>
      </div>

      <!-- Status -->
      <div style="font-size: 9px; color: #4CAF50;">
        ✅ SPA Navigation (sem reload)<br>
        ⚡ jQuery gerenciado automaticamente<br>
        📊 Performance monitorada
      </div>
    </div>
  `,
  styles: []
})
export class NavigationTestComponent {
  timestamp: string = new Date().toLocaleTimeString();

  constructor(
    private navigationService: NavigationService,
    private performanceService: PerformanceService,
    private scriptLoader: ScriptLoaderService,
    private toastService: ToastService
  ) {
    // Atualizar timestamp a cada segundo para mostrar que não há reload
    setInterval(() => {
      this.timestamp = new Date().toLocaleTimeString();
    }, 1000);
  }

  getCurrentRoute(): string {
    return this.navigationService.getCurrentRoute();
  }

  testNavigation(route: string): void {
    this.navigationService.navigateTo(route);
  }

  forceReinitialize(): void {
    this.navigationService.forceReinitialize();
  }

  showPerformance(): void {
    const report = this.performanceService.generateReport();
    const issues = this.performanceService.detectPerformanceIssues();

    if (issues.length > 0) {
      // Problemas de performance detectados
    }

    this.toastService.info(
      report + (issues.length > 0 ? '\n\nProblemas:\n' + issues.join('\n') : ''),
      issues.length > 0 ? '⚠️ Relatório de Performance' : '✅ Performance OK',
      {
        duration: issues.length > 0 ? 0 : 8000,
        position: 'top-left' as any,
        actions: issues.length > 0 ? [
          {
            label: 'Ver Console',
            action: () => {
              console.group('📋 Relatório de Performance Detalhado');
              console.log(report);
              if (issues.length > 0) {
                console.warn('Problemas detectados:', issues);
              }
              console.groupEnd();
              this.toastService.info('Relatório detalhado disponível no console (F12)', 'Console');
            }
          }
        ] : []
      }
    );
  }

  getLoadedScriptsCount(): number {
    return this.scriptLoader.getLoadedScripts().length;
  }

  getMemoryUsage(): string {
    const memory = this.performanceService.getMemoryUsage();
    return memory ? memory.used : 'N/A';
  }
}
