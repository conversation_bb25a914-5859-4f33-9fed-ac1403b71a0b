import { Component, Input, OnInit, inject } from '@angular/core';
import { Router, NavigationEnd, RouterModule } from '@angular/router';
import { filter } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-header',
  imports: [CommonModule, RouterModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent implements OnInit {
  @Input() headerType: 'home' | 'internal' | 'auto' = 'auto';

  private router = inject(Router);
  private currentHeaderType: 'home' | 'internal' = 'internal';

  constructor() {}

  ngOnInit() {
    if (this.headerType === 'auto') {
      // Detectar rota inicial
      this.updateHeaderType(this.router.url);

      // Escutar mudanças de rota
      this.router.events
        .pipe(filter(event => event instanceof NavigationEnd))
        .subscribe((event: NavigationEnd) => {
          this.updateHeaderType(event.url);
        });
    }
  }

  private updateHeaderType(url: string): void {
    // Considerar home se a URL for '/' ou '/home'
    const isHomePage = url === '/' || url === '/home' || url.startsWith('/?') || url.startsWith('/home?');
    this.currentHeaderType = isHomePage ? 'home' : 'internal';
  }

  get isHomeHeader(): boolean {
    if (this.headerType === 'auto') {
      return this.currentHeaderType === 'home';
    }
    return this.headerType === 'home';
  }

  get isInternalHeader(): boolean {
    if (this.headerType === 'auto') {
      return this.currentHeaderType === 'internal';
    }
    return this.headerType === 'internal';
  }

  openServiceModal(event: Event) {
    event.preventDefault();
    this.router.navigate(['/solicitacao-servico']);
  }

  openClientArea(event: Event) {
    event.preventDefault();
    // Redirecionar para a área de login do sistema Autopro
    // Por enquanto, vamos usar uma URL local, mas isso pode ser configurado
    window.open('http://localhost:4201/login', '_blank');
  }
}
