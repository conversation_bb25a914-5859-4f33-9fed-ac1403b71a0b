import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

export interface BreadcrumbItem {
  label: string;
  url?: string;
  isActive?: boolean;
}

@Component({
  selector: 'app-featured-title',
  imports: [CommonModule, RouterModule],
  templateUrl: './featured-title.component.html',
  styleUrl: './featured-title.component.css'
})
export class FeaturedTitleComponent {
  @Input() title: string = '';
  @Input() breadcrumbs: BreadcrumbItem[] = [];
  @Input() alignment: 'left' | 'right' | 'centered1' | 'centered2' = 'left';
  @Input() hasShadow: boolean = false;
}
