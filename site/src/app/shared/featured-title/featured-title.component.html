<!-- Featured Title -->
<div id="featured-title" class="clearfix"
     [ngClass]="{
       'featured-title-left': alignment === 'left',
       'featured-title-right': alignment === 'right',
       'featured-title-centered1': alignment === 'centered1',
       'featured-title-centered2': alignment === 'centered2'
     }">
  <div id="featured-title-inner" class="wprt-container clearfix">
    <div class="featured-title-inner-wrap">
      <div class="featured-title-heading-wrap">
        <h1 class="featured-title-heading"
            [ngClass]="{'has-shadow': hasShadow}">
          {{ title }}
        </h1>
      </div>

      <div id="breadcrumbs" *ngIf="breadcrumbs.length > 0">
        <div class="breadcrumbs-inner">
          <div class="breadcrumb-trail">
            <ng-container *ngFor="let item of breadcrumbs; let last = last">
              <a *ngIf="item.url && !item.isActive"
                 [routerLink]="item.url"
                 [title]="item.label"
                 [ngClass]="{'trail-begin': breadcrumbs.indexOf(item) === 0}">
                {{ item.label }}
              </a>
              <span *ngIf="!item.url || item.isActive"
                    [ngClass]="{'trail-end': item.isActive}">
                {{ item.label }}
              </span>
              <span *ngIf="!last" class="sep">
                <i class="rt-icon-right-arrow12"></i>
              </span>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
</div><!-- #featured-title -->
