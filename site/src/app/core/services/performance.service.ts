import { Injectable } from '@angular/core';
import { isDevMode } from '@angular/core';

interface PerformanceMetrics {
  navigationStart: number;
  domContentLoaded: number;
  loadComplete: number;
  jQueryInitialized: number;
  componentsReady: number;
}

@Injectable({
  providedIn: 'root'
})
export class PerformanceService {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];
  private isDevelopment = isDevMode();

  constructor() {
    this.initializePerformanceTracking();
  }

  /**
   * Inicializa o rastreamento de performance
   */
  private initializePerformanceTracking(): void {
    // Marcar início da navegação
    this.metrics.navigationStart = performance.now();

    // Observar eventos de performance
    this.observeNavigationTiming();
    this.observeResourceTiming();
  }

  /**
   * Observa timing de navegação
   */
  private observeNavigationTiming(): void {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.processNavigationTiming(navEntry);
            }
          }
        });

        observer.observe({ entryTypes: ['navigation'] });
        this.observers.push(observer);
      } catch (error) {
        console.warn('⚠️ PerformanceObserver não suportado:', error);
      }
    }
  }

  /**
   * Observa timing de recursos
   */
  private observeResourceTiming(): void {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'resource') {
              this.processResourceTiming(entry as PerformanceResourceTiming);
            }
          }
        });

        observer.observe({ entryTypes: ['resource'] });
        this.observers.push(observer);
      } catch (error) {
        console.warn('⚠️ Resource timing observer não suportado:', error);
      }
    }
  }

  /**
   * Processa timing de navegação
   */
  private processNavigationTiming(entry: PerformanceNavigationTiming): void {
    // Usar fetchStart como referência se navigationStart não estiver disponível
    const startTime = (entry as any).navigationStart || entry.fetchStart;
    this.metrics.domContentLoaded = entry.domContentLoadedEventEnd - startTime;
    this.metrics.loadComplete = entry.loadEventEnd - startTime;

    if (this.isDevelopment) {
      console.log('📊 Navigation Timing:', {
        domContentLoaded: `${this.metrics.domContentLoaded.toFixed(2)}ms`,
        loadComplete: `${this.metrics.loadComplete.toFixed(2)}ms`
      });
    }
  }

  /**
   * Processa timing de recursos
   */
  private processResourceTiming(entry: PerformanceResourceTiming): void {
    // Filtrar apenas scripts JavaScript relevantes
    if (entry.name.includes('.js') && entry.name.includes('assets')) {
      const loadTime = entry.responseEnd - entry.startTime;
      
      if (loadTime > 100 && this.isDevelopment) { // Apenas scripts que demoram mais de 100ms
        console.log(`📊 Script Load Time: ${entry.name.split('/').pop()} - ${loadTime.toFixed(2)}ms`);
      }
    }
  }

  /**
   * Marca quando jQuery foi inicializado
   */
  public markJQueryInitialized(): void {
    this.metrics.jQueryInitialized = performance.now() - (this.metrics.navigationStart || 0);
    if (this.isDevelopment) {
      console.log(`📊 jQuery inicializado em: ${this.metrics.jQueryInitialized.toFixed(2)}ms`);
    }
  }

  /**
   * Marca quando componentes estão prontos
   */
  public markComponentsReady(): void {
    this.metrics.componentsReady = performance.now() - (this.metrics.navigationStart || 0);
    if (this.isDevelopment) {
      console.log(`📊 Componentes prontos em: ${this.metrics.componentsReady.toFixed(2)}ms`);
    }
  }

  /**
   * Obtém métricas atuais
   */
  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  /**
   * Gera relatório de performance
   */
  public generateReport(): string {
    const report = [
      '📊 RELATÓRIO DE PERFORMANCE',
      '================================',
      `DOM Content Loaded: ${(this.metrics.domContentLoaded || 0).toFixed(2)}ms`,
      `Load Complete: ${(this.metrics.loadComplete || 0).toFixed(2)}ms`,
      `jQuery Initialized: ${(this.metrics.jQueryInitialized || 0).toFixed(2)}ms`,
      `Components Ready: ${(this.metrics.componentsReady || 0).toFixed(2)}ms`,
      '================================'
    ];

    return report.join('\n');
  }

  /**
   * Monitora uso de memória
   */
  public getMemoryUsage(): any {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
      };
    }
    return null;
  }

  /**
   * Otimiza performance removendo observers desnecessários
   */
  public optimizePerformance(): void {
    // Desconectar observers após um tempo
    setTimeout(() => {
      this.observers.forEach(observer => {
        try {
          observer.disconnect();
        } catch (error) {
          // Erro ao desconectar observer
        }
      });
      this.observers = [];
      if (this.isDevelopment) {
        console.log('🧹 Performance observers otimizados');
      }
    }, 30000); // 30 segundos
  }

  /**
   * Detecta problemas de performance
   */
  public detectPerformanceIssues(): string[] {
    const issues: string[] = [];

    if ((this.metrics.domContentLoaded || 0) > 3000) {
      issues.push('⚠️ DOM Content Loaded muito lento (>3s)');
    }

    if ((this.metrics.loadComplete || 0) > 5000) {
      issues.push('⚠️ Load Complete muito lento (>5s)');
    }

    if ((this.metrics.jQueryInitialized || 0) > 2000) {
      issues.push('⚠️ jQuery inicialização lenta (>2s)');
    }

    const memory = this.getMemoryUsage();
    if (memory && parseFloat(memory.used) > 100) {
      issues.push('⚠️ Alto uso de memória (>100MB)');
    }

    return issues;
  }

  /**
   * Limpa recursos ao destruir o serviço
   */
  public destroy(): void {
    this.observers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (error) {
        console.warn('⚠️ Erro ao desconectar observer:', error);
      }
    });
    this.observers = [];
    this.metrics = {};
  }
}
