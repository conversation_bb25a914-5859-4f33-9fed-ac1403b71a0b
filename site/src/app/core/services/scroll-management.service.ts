import { Injectable } from '@angular/core';
import { Router, NavigationEnd, Scroll } from '@angular/router';
import { ViewportScroller } from '@angular/common';
import { filter } from 'rxjs/operators';

/**
 * Serviço dedicado ao gerenciamento de scroll em mudanças de rota
 * Garante que o scroll sempre vá para o topo ao navegar entre páginas
 */
@Injectable({
  providedIn: 'root'
})
export class ScrollManagementService {

  constructor(
    private router: Router,
    private viewportScroller: ViewportScroller
  ) {
    this.initializeScrollManagement();
  }

  /**
   * Inicializa o gerenciamento automático de scroll
   */
  private initializeScrollManagement(): void {
    // Escutar eventos de navegação
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.scrollToTop();
      });

    // Escutar eventos de scroll específicos do router
    this.router.events
      .pipe(filter(event => event instanceof Scroll))
      .subscribe((event: Scroll) => {
        if (event.position) {
          // Se há uma posição específica, usar ela
          this.viewportScroller.scrollToPosition(event.position);
        } else if (event.anchor) {
          // Se há uma âncora, rolar para ela
          this.viewportScroller.scrollToAnchor(event.anchor);
        } else {
          // Caso contrário, rolar para o topo
          this.scrollToTop();
        }
      });
  }

  /**
   * Rola a página para o topo de forma suave
   */
  public scrollToTop(): void {
    try {
      // Método 1: ViewportScroller (Angular nativo)
      this.viewportScroller.scrollToPosition([0, 0]);
      
      // Método 2: JavaScript nativo com smooth scroll
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      }, 0);
      
      // Método 3: Fallback para navegadores antigos
      setTimeout(() => {
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
      }, 10);
      
    } catch (error) {
      console.warn('Erro ao fazer scroll para o topo:', error);
      // Fallback final
      window.scrollTo(0, 0);
    }
  }

  /**
   * Rola para uma posição específica
   */
  public scrollToPosition(x: number, y: number): void {
    try {
      this.viewportScroller.scrollToPosition([x, y]);
    } catch (error) {
      window.scrollTo(x, y);
    }
  }

  /**
   * Rola para um elemento específico
   */
  public scrollToElement(elementId: string): void {
    try {
      this.viewportScroller.scrollToAnchor(elementId);
    } catch (error) {
      const element = document.getElementById(elementId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }

  /**
   * Força o scroll para o topo (para uso manual)
   */
  public forceScrollToTop(): void {
    // Múltiplas tentativas para garantir que funcione
    this.scrollToTop();
    
    setTimeout(() => this.scrollToTop(), 50);
    setTimeout(() => this.scrollToTop(), 100);
    setTimeout(() => this.scrollToTop(), 200);
  }
}
