import { Injectable } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { ScriptLoaderService } from './script-loader.service';
import { AssetOptimizerService } from './asset-optimizer.service';

declare global {
  interface Window {
    AngularJQueryIntegration: any;
    initializeJQueryPlugins: () => void;
  }
}

@Injectable({
  providedIn: 'root'
})
export class NavigationService {
  private isFirstNavigation = true;
  private reinitializationTimeout: any;

  constructor(
    private router: Router,
    private scriptLoader: ScriptLoaderService,
    private assetOptimizer: AssetOptimizerService
  ) {
    this.setupNavigationListener();
    this.initializeAssetOptimization();
  }

  /**
   * Inicializa otimizações de assets
   */
  private initializeAssetOptimization(): void {
    // Otimizar assets na inicialização
    setTimeout(() => {
      this.assetOptimizer.removeUnusedAssets();
      this.assetOptimizer.optimizeLoadOrder();
      this.assetOptimizer.monitorLoadPerformance();
    }, 1000);
  }

  /**
   * Configura o listener de navegação para reinicializar jQuery
   * sem recarregar a página
   */
  private setupNavigationListener(): void {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        // Sempre fazer scroll para o topo imediatamente em qualquer mudança de rota
        this.scrollToTop();

        // Não reinicializar na primeira navegação (carregamento inicial)
        if (this.isFirstNavigation) {
          this.isFirstNavigation = false;
          return;
        }

        this.handleRouteChange(event.url);
      });
  }

  /**
   * Gerencia a mudança de rota sem reload da página
   */
  private handleRouteChange(url: string): void {
    // Limpar timeout anterior se existir
    if (this.reinitializationTimeout) {
      clearTimeout(this.reinitializationTimeout);
    }

    // Aguardar o componente ser renderizado antes de reinicializar jQuery
    this.reinitializationTimeout = setTimeout(() => {
      this.reinitializeJQuery();
      this.cleanupLoadingElements();
    }, 100);
  }

  /**
   * Reinicializa os plugins jQuery de forma controlada
   */
  private reinitializeJQuery(): void {
    try {
      this.scriptLoader.reinitializeJQueryPlugins();
    } catch (error) {
      // Erro ao reinicializar jQuery
    }
  }

  /**
   * Remove elementos de loading que podem ter ficado na página
   */
  private cleanupLoadingElements(): void {
    try {
      // Remover elementos de loading
      const loadingElements = document.querySelectorAll('.animsition-loading, .tp-loader');
      loadingElements.forEach(element => element.remove());

      // Garantir visibilidade do wrapper
      const wrapper = document.getElementById('wrapper');
      if (wrapper) {
        wrapper.style.opacity = '1';
        wrapper.style.visibility = 'visible';
      }

      // Remover classes de loading do body
      document.body.classList.remove('animsition-loading', 'loading-active');
    } catch (error) {
      console.error('❌ Erro ao limpar elementos de loading:', error);
    }
  }

  /**
   * Rola a página para o topo suavemente
   */
  private scrollToTop(): void {
    try {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
    } catch (error) {
      // Fallback para navegadores mais antigos
      window.scrollTo(0, 0);
    }
  }

  /**
   * Força a reinicialização manual (para uso em componentes)
   */
  public forceReinitialize(): void {
    this.reinitializeJQuery();
    this.cleanupLoadingElements();
  }

  /**
   * Navega para uma rota específica
   */
  public navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  /**
   * Obtém a rota atual
   */
  public getCurrentRoute(): string {
    return this.router.url;
  }
}
