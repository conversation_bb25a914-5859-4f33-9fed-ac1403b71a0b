import { Injectable } from '@angular/core';

interface ScriptInfo {
  src: string;
  loaded: boolean;
  loading: boolean;
  element?: HTMLScriptElement;
}

@Injectable({
  providedIn: 'root'
})
export class ScriptLoaderService {
  private scripts: Map<string, ScriptInfo> = new Map();
  private loadedScripts: Set<string> = new Set();

  constructor() {
    this.initializeScriptMap();
  }

  /**
   * Inicializa o mapa de scripts disponíveis
   */
  private initializeScriptMap(): void {
    const scriptList = [
      // Scripts essenciais (já carregados no index.html)
      'jquery',
      'bootstrap',
      'plugins',
      
      // Scripts de animação
      'animsition',
      'countto',
      'vegas',
      
      // Scripts de galeria e carrossel
      'cubeportfolio',
      'owl-carousel',
      'flexslider',
      
      // Scripts de popup e modal
      'magnific-popup',
      
      // Scripts utilitários
      'equalize',
      'fittext',
      'simple-text-rotator',
      'typed',
      
      // Scripts de formulário
      'datepicker',
      'validate',
      
      // Scripts de mapas
      'gmap3',
      
      // Scripts customizados
      'shortcodes',
      'main',
      'angular-integration'
    ];

    scriptList.forEach(name => {
      this.scripts.set(name, {
        src: this.getScriptPath(name),
        loaded: false,
        loading: false
      });
    });
  }

  /**
   * Obtém o caminho do script baseado no nome
   */
  private getScriptPath(name: string): string {
    const basePath = 'assets/js/';
    
    switch (name) {
      case 'jquery': return `${basePath}jquery.min.js`;
      case 'bootstrap': return `${basePath}bootstrap.min.js`;
      case 'plugins': return `${basePath}plugins.js`;
      case 'animsition': return `${basePath}animsition.js`;
      case 'countto': return `${basePath}countto.js`;
      case 'vegas': return `${basePath}vegas.js`;
      case 'cubeportfolio': return `${basePath}cubeportfolio.js`;
      case 'owl-carousel': return `${basePath}owl.carousel.min.js`;
      case 'flexslider': return `${basePath}flexslider.min.js`;
      case 'magnific-popup': return `${basePath}magnific.popup.min.js`;
      case 'equalize': return `${basePath}equalize.min.js`;
      case 'fittext': return `${basePath}fittext.js`;
      case 'simple-text-rotator': return `${basePath}simple.text.rotator.js`;
      case 'typed': return `${basePath}typed.js`;
      case 'datepicker': return `${basePath}jquery-ui.datepicker.js`;
      case 'validate': return `${basePath}validate.js`;
      case 'gmap3': return `${basePath}gmap3.min.js`;
      case 'shortcodes': return `${basePath}shortcodes.js`;
      case 'main': return `${basePath}main.js`;
      case 'angular-integration': return `${basePath}angular-integration.js`;
      default: return `${basePath}${name}.js`;
    }
  }

  /**
   * Carrega um script específico
   */
  public loadScript(name: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const scriptInfo = this.scripts.get(name);
      
      if (!scriptInfo) {
        reject(new Error(`Script '${name}' não encontrado`));
        return;
      }

      // Se já está carregado, resolver imediatamente
      if (scriptInfo.loaded) {
        resolve();
        return;
      }

      // Se está carregando, aguardar
      if (scriptInfo.loading) {
        const checkLoaded = () => {
          if (scriptInfo.loaded) {
            resolve();
          } else {
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
        return;
      }

      // Marcar como carregando
      scriptInfo.loading = true;

      // Verificar se o script já existe no DOM
      const existingScript = document.querySelector(`script[src="${scriptInfo.src}"]`);
      if (existingScript) {
        scriptInfo.loaded = true;
        scriptInfo.loading = false;
        this.loadedScripts.add(name);
        resolve();
        return;
      }

      // Criar e carregar o script
      const script = document.createElement('script');
      script.src = scriptInfo.src;
      script.async = false; // Manter ordem de carregamento
      
      script.onload = () => {
        scriptInfo.loaded = true;
        scriptInfo.loading = false;
        scriptInfo.element = script;
        this.loadedScripts.add(name);
        resolve();
      };

      script.onerror = () => {
        scriptInfo.loading = false;
        reject(new Error(`Falha ao carregar script: ${name}`));
      };

      document.head.appendChild(script);
    });
  }

  /**
   * Carrega múltiplos scripts em sequência
   */
  public async loadScripts(names: string[]): Promise<void> {
    for (const name of names) {
      try {
        await this.loadScript(name);
      } catch (error) {
        // Continuar com os próximos scripts mesmo se um falhar
      }
    }
  }

  /**
   * Verifica se um script está carregado
   */
  public isScriptLoaded(name: string): boolean {
    return this.loadedScripts.has(name);
  }

  /**
   * Obtém lista de scripts carregados
   */
  public getLoadedScripts(): string[] {
    return Array.from(this.loadedScripts);
  }

  /**
   * Reinicializa os plugins jQuery
   */
  public reinitializeJQueryPlugins(): void {
    try {
      // Verificar se jQuery está disponível
      if (typeof (window as any).$ === 'undefined') {
        return;
      }

      // Usar o sistema de integração existente
      if (typeof (window as any).AngularJQueryIntegration !== 'undefined') {
        const integration = (window as any).AngularJQueryIntegration;
        integration.initialized = false;
        integration.retryCount = 0;
        integration.init();
      } else if (typeof (window as any).initializeJQueryPlugins === 'function') {
        (window as any).initializeJQueryPlugins();
      }
    } catch (error) {
      // Erro ao reinicializar jQuery plugins
    }
  }

  /**
   * Remove um script do DOM
   */
  public removeScript(name: string): void {
    const scriptInfo = this.scripts.get(name);
    if (scriptInfo && scriptInfo.element) {
      scriptInfo.element.remove();
      scriptInfo.loaded = false;
      scriptInfo.loading = false;
      scriptInfo.element = undefined;
      this.loadedScripts.delete(name);
    }
  }

  /**
   * Limpa todos os scripts carregados dinamicamente
   */
  public clearDynamicScripts(): void {
    this.scripts.forEach((scriptInfo, name) => {
      if (scriptInfo.element) {
        this.removeScript(name);
      }
    });
  }
}
