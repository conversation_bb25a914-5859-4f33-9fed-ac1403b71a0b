import { Injectable } from '@angular/core';

interface AssetConfig {
  critical: string[];
  deferred: string[];
  conditional: { [key: string]: string[] };
}

@Injectable({
  providedIn: 'root'
})
export class AssetOptimizerService {
  private loadedAssets: Set<string> = new Set();
  private assetConfig!: AssetConfig;

  constructor() {
    this.initializeAssetConfig();
  }

  /**
   * Inicializa a configuração de assets
   */
  private initializeAssetConfig(): void {
    this.assetConfig = {
      // Assets críticos - carregados imediatamente
      critical: [
        'assets/css/bootstrap.css',
        'assets/css/font-awesome.css',
        'assets/css/shortcodes.css',
        'assets/js/jquery.min.js',
        'assets/js/bootstrap.min.js'
      ],
      
      // Assets diferidos - carregados após o conteúdo principal
      deferred: [
        'assets/css/animate.css',
        'assets/css/animsition.css',
        'assets/css/magnific-popup.css',
        'assets/css/cubeportfolio.css',
        'assets/css/owl.carousel.css',
        'assets/css/flexslider.css',
        'assets/css/vegas.css',
        'assets/js/plugins.js',
        'assets/js/animsition.js',
        'assets/js/countto.js',
        'assets/js/vegas.js'
      ],
      
      // Assets condicionais - carregados apenas quando necessário
      conditional: {
        gallery: [
          'assets/js/cubeportfolio.js',
          'assets/js/magnific.popup.min.js'
        ],
        carousel: [
          'assets/js/owl.carousel.min.js',
          'assets/js/flexslider.min.js'
        ],
        forms: [
          'assets/js/jquery-ui.datepicker.js',
          'assets/js/validate.js'
        ],
        maps: [
          'assets/js/gmap3.min.js'
        ]
      }
    };
  }

  /**
   * Carrega assets críticos
   */
  public async loadCriticalAssets(): Promise<void> {
    console.log('🚀 Carregando assets críticos...');
    
    const promises = this.assetConfig.critical.map(asset => this.loadAsset(asset));
    await Promise.all(promises);
    
    console.log('✅ Assets críticos carregados');
  }

  /**
   * Carrega assets diferidos
   */
  public async loadDeferredAssets(): Promise<void> {
    // Aguardar um pouco para não bloquear o carregamento inicial
    await this.delay(500);
    
    console.log('⏳ Carregando assets diferidos...');
    
    const promises = this.assetConfig.deferred.map(asset => this.loadAsset(asset));
    await Promise.allSettled(promises); // Usar allSettled para não falhar se um asset não carregar
    
    console.log('✅ Assets diferidos carregados');
  }

  /**
   * Carrega assets condicionais baseado na necessidade
   */
  public async loadConditionalAssets(condition: string): Promise<void> {
    const assets = this.assetConfig.conditional[condition];
    
    if (!assets) {
      console.warn(`⚠️ Condição '${condition}' não encontrada`);
      return;
    }

    console.log(`🎯 Carregando assets para: ${condition}`);
    
    const promises = assets.map(asset => this.loadAsset(asset));
    await Promise.allSettled(promises);
    
    console.log(`✅ Assets para '${condition}' carregados`);
  }

  /**
   * Carrega um asset individual
   */
  private loadAsset(assetPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Verificar se já foi carregado
      if (this.loadedAssets.has(assetPath)) {
        resolve();
        return;
      }

      const isCSS = assetPath.endsWith('.css');
      const element = isCSS ? this.createLinkElement(assetPath) : this.createScriptElement(assetPath);

      element.onload = () => {
        this.loadedAssets.add(assetPath);
        resolve();
      };

      element.onerror = () => {
        reject(new Error(`Falha ao carregar ${assetPath}`));
      };

      // Adicionar ao DOM
      if (isCSS) {
        document.head.appendChild(element);
      } else {
        document.body.appendChild(element);
      }
    });
  }

  /**
   * Cria elemento link para CSS
   */
  private createLinkElement(href: string): HTMLLinkElement {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    return link;
  }

  /**
   * Cria elemento script para JS
   */
  private createScriptElement(src: string): HTMLScriptElement {
    const script = document.createElement('script');
    script.src = src;
    script.async = false; // Manter ordem de carregamento
    return script;
  }

  /**
   * Pré-carrega assets para melhor performance
   */
  public preloadAssets(assets: string[]): void {
    assets.forEach(asset => {
      const link = document.createElement('link');
      link.rel = 'preload';
      
      if (asset.endsWith('.css')) {
        link.as = 'style';
      } else if (asset.endsWith('.js')) {
        link.as = 'script';
      }
      
      link.href = asset;
      document.head.appendChild(link);
    });
    
    console.log(`🔄 Pré-carregando ${assets.length} assets`);
  }

  /**
   * Remove assets não utilizados
   */
  public removeUnusedAssets(): void {
    // Remover scripts duplicados
    const scripts = document.querySelectorAll('script[src]');
    const seenSrcs = new Set<string>();
    
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src;
      if (seenSrcs.has(src)) {
        script.remove();
      } else {
        seenSrcs.add(src);
      }
    });

    // Remover CSS duplicados
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    const seenHrefs = new Set<string>();
    
    links.forEach(link => {
      const href = (link as HTMLLinkElement).href;
      if (seenHrefs.has(href)) {
        link.remove();
      } else {
        seenHrefs.add(href);
      }
    });
  }

  /**
   * Otimiza ordem de carregamento
   */
  public optimizeLoadOrder(): void {
    // Mover scripts críticos para o final do body
    const criticalScripts = ['jquery.min.js', 'bootstrap.min.js'];
    
    criticalScripts.forEach(scriptName => {
      const script = document.querySelector(`script[src*="${scriptName}"]`);
      if (script && script.parentNode !== document.body) {
        document.body.appendChild(script);
        console.log(`📦 Script movido para body: ${scriptName}`);
      }
    });
  }

  /**
   * Monitora performance de carregamento
   */
  public monitorLoadPerformance(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource' && entry.name.includes('assets')) {
            const resourceEntry = entry as PerformanceResourceTiming;
            const loadTime = resourceEntry.responseEnd - resourceEntry.startTime;
            if (loadTime > 200) { // Assets que demoram mais de 200ms
              // Asset lento detectado
            }
          }
        }
      });

      observer.observe({ entryTypes: ['resource'] });
      
      // Desconectar após 30 segundos
      setTimeout(() => observer.disconnect(), 30000);
    }
  }

  /**
   * Utilitário para delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtém estatísticas de assets carregados
   */
  public getLoadStats(): { loaded: number; total: number; percentage: number } {
    const total = this.assetConfig.critical.length + this.assetConfig.deferred.length;
    const loaded = this.loadedAssets.size;
    const percentage = Math.round((loaded / total) * 100);
    
    return { loaded, total, percentage };
  }

  /**
   * Verifica se um asset está carregado
   */
  public isAssetLoaded(assetPath: string): boolean {
    return this.loadedAssets.has(assetPath);
  }
}
