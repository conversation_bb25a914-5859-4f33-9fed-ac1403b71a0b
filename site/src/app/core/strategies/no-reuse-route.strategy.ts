import { RouteReuseStrategy, ActivatedRouteSnapshot } from '@angular/router';

/**
 * Estratégia de rota que evita reutilização de componentes
 * Força a recriação completa de componentes em cada navegação
 * Resolve problemas de assets externos que não são reinicializados
 */
export class NoReuseRouteStrategy implements RouteReuseStrategy {

  /**
   * Determina se a rota deve ser armazenada
   * @param route - Snapshot da rota
   * @returns false - nunca armazenar rotas
   */
  shouldDetach(route: ActivatedRouteSnapshot): boolean {
    return false;
  }

  /**
   * Armazena a árvore de rota destacada
   * @param route - Snapshot da rota
   * @param tree - Árvore de rota destacada
   */
  store(route: ActivatedRouteSnapshot, tree: any): void {
    // Não armazenar nada
  }

  /**
   * Determina se a rota deve ser reutilizada
   * @param route - Snapshot da rota
   * @returns false - nunca reutilizar rotas
   */
  shouldAttach(route: ActivatedRouteSnapshot): boolean {
    return false;
  }

  /**
   * Recupera a árvore de rota armazenada
   * @param route - Snapshot da rota
   * @returns null - nunca retornar rotas armazenadas
   */
  retrieve(route: ActivatedRouteSnapshot): any {
    return null;
  }

  /**
   * Determina se a rota deve ser reutilizada
   * @param future - Snapshot da rota futura
   * @param curr - Snapshot da rota atual
   * @returns false - sempre recriar componentes
   */
  shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {
    // Forçar recriação do componente
    return false;
  }
}
