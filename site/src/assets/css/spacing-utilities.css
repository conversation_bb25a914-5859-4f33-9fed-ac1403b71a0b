/* Spacing Utilities - Substituição dos wprt-spacer
-------------------------------------------------------------- */

/* Padding Top Utilities */
.pt-20 { padding-top: 20px !important; }
.pt-30 { padding-top: 30px !important; }
.pt-40 { padding-top: 40px !important; }
.pt-50 { padding-top: 50px !important; }
.pt-60 { padding-top: 60px !important; }
.pt-70 { padding-top: 70px !important; }
.pt-80 { padding-top: 80px !important; }
.pt-90 { padding-top: 90px !important; }
.pt-100 { padding-top: 100px !important; }
.pt-120 { padding-top: 120px !important; }

/* Padding Bottom Utilities */
.pb-20 { padding-bottom: 20px !important; }
.pb-30 { padding-bottom: 30px !important; }
.pb-40 { padding-bottom: 40px !important; }
.pb-50 { padding-bottom: 50px !important; }
.pb-60 { padding-bottom: 60px !important; }
.pb-70 { padding-bottom: 70px !important; }
.pb-80 { padding-bottom: 80px !important; }
.pb-90 { padding-bottom: 90px !important; }
.pb-100 { padding-bottom: 100px !important; }
.pb-120 { padding-bottom: 120px !important; }

/* Padding Y (Top + Bottom) Utilities */
.py-20 { padding-top: 20px !important; padding-bottom: 20px !important; }
.py-30 { padding-top: 30px !important; padding-bottom: 30px !important; }
.py-40 { padding-top: 40px !important; padding-bottom: 40px !important; }
.py-50 { padding-top: 50px !important; padding-bottom: 50px !important; }
.py-60 { padding-top: 60px !important; padding-bottom: 60px !important; }
.py-70 { padding-top: 70px !important; padding-bottom: 70px !important; }
.py-80 { padding-top: 80px !important; padding-bottom: 80px !important; }
.py-90 { padding-top: 90px !important; padding-bottom: 90px !important; }
.py-100 { padding-top: 100px !important; padding-bottom: 100px !important; }
.py-120 { padding-top: 120px !important; padding-bottom: 120px !important; }

/* Margin Bottom Utilities (para espaçamento entre elementos) */
.mb-20 { margin-bottom: 20px !important; }
.mb-30 { margin-bottom: 30px !important; }
.mb-40 { margin-bottom: 40px !important; }
.mb-50 { margin-bottom: 50px !important; }
.mb-60 { margin-bottom: 60px !important; }
.mb-70 { margin-bottom: 70px !important; }

/* Margin Top Utilities */
.mt-20 { margin-top: 20px !important; }
.mt-30 { margin-top: 30px !important; }
.mt-40 { margin-top: 40px !important; }
.mt-50 { margin-top: 50px !important; }
.mt-60 { margin-top: 60px !important; }
.mt-70 { margin-top: 70px !important; }

/* Responsive Utilities - Tablet (max-width: 991px) */
@media only screen and (max-width: 991px) {
    /* Padding Top - Tablet */
    .pt-20 { padding-top: 20px !important; }
    .pt-30 { padding-top: 30px !important; }
    .pt-40 { padding-top: 40px !important; }
    .pt-50 { padding-top: 40px !important; }
    .pt-60 { padding-top: 50px !important; }
    .pt-70 { padding-top: 50px !important; }
    .pt-80 { padding-top: 50px !important; }
    .pt-90 { padding-top: 60px !important; }
    .pt-100 { padding-top: 60px !important; }
    .pt-120 { padding-top: 60px !important; }

    /* Padding Bottom - Tablet */
    .pb-20 { padding-bottom: 20px !important; }
    .pb-30 { padding-bottom: 30px !important; }
    .pb-40 { padding-bottom: 40px !important; }
    .pb-50 { padding-bottom: 40px !important; }
    .pb-60 { padding-bottom: 50px !important; }
    .pb-70 { padding-bottom: 50px !important; }
    .pb-80 { padding-bottom: 50px !important; }
    .pb-90 { padding-bottom: 60px !important; }
    .pb-100 { padding-bottom: 60px !important; }
    .pb-120 { padding-bottom: 60px !important; }

    /* Padding Y - Tablet */
    .py-20 { padding-top: 20px !important; padding-bottom: 20px !important; }
    .py-30 { padding-top: 30px !important; padding-bottom: 30px !important; }
    .py-40 { padding-top: 40px !important; padding-bottom: 40px !important; }
    .py-50 { padding-top: 40px !important; padding-bottom: 40px !important; }
    .py-60 { padding-top: 50px !important; padding-bottom: 50px !important; }
    .py-70 { padding-top: 50px !important; padding-bottom: 50px !important; }
    .py-80 { padding-top: 50px !important; padding-bottom: 50px !important; }
    .py-90 { padding-top: 60px !important; padding-bottom: 60px !important; }
    .py-100 { padding-top: 60px !important; padding-bottom: 60px !important; }
    .py-120 { padding-top: 60px !important; padding-bottom: 60px !important; }

    /* Margin Bottom - Tablet */
    .mb-20 { margin-bottom: 20px !important; }
    .mb-30 { margin-bottom: 30px !important; }
    .mb-40 { margin-bottom: 40px !important; }
    .mb-50 { margin-bottom: 40px !important; }
    .mb-60 { margin-bottom: 50px !important; }
    .mb-70 { margin-bottom: 50px !important; }
}

/* Responsive Utilities - Mobile (max-width: 767px) */
@media only screen and (max-width: 767px) {
    /* Padding Top - Mobile */
    .pt-20 { padding-top: 20px !important; }
    .pt-30 { padding-top: 30px !important; }
    .pt-40 { padding-top: 40px !important; }
    .pt-50 { padding-top: 40px !important; }
    .pt-60 { padding-top: 50px !important; }
    .pt-70 { padding-top: 50px !important; }
    .pt-80 { padding-top: 50px !important; }
    .pt-90 { padding-top: 60px !important; }
    .pt-100 { padding-top: 60px !important; }
    .pt-120 { padding-top: 60px !important; }

    /* Padding Bottom - Mobile */
    .pb-20 { padding-bottom: 20px !important; }
    .pb-30 { padding-bottom: 30px !important; }
    .pb-40 { padding-bottom: 40px !important; }
    .pb-50 { padding-bottom: 40px !important; }
    .pb-60 { padding-bottom: 50px !important; }
    .pb-70 { padding-bottom: 50px !important; }
    .pb-80 { padding-bottom: 50px !important; }
    .pb-90 { padding-bottom: 60px !important; }
    .pb-100 { padding-bottom: 60px !important; }
    .pb-120 { padding-bottom: 60px !important; }

    /* Padding Y - Mobile */
    .py-20 { padding-top: 20px !important; padding-bottom: 20px !important; }
    .py-30 { padding-top: 30px !important; padding-bottom: 30px !important; }
    .py-40 { padding-top: 40px !important; padding-bottom: 40px !important; }
    .py-50 { padding-top: 40px !important; padding-bottom: 40px !important; }
    .py-60 { padding-top: 50px !important; padding-bottom: 50px !important; }
    .py-70 { padding-top: 50px !important; padding-bottom: 50px !important; }
    .py-80 { padding-top: 50px !important; padding-bottom: 50px !important; }
    .py-90 { padding-top: 60px !important; padding-bottom: 60px !important; }
    .py-100 { padding-top: 60px !important; padding-bottom: 60px !important; }
    .py-120 { padding-top: 60px !important; padding-bottom: 60px !important; }

    /* Margin Bottom - Mobile */
    .mb-20 { margin-bottom: 20px !important; }
    .mb-30 { margin-bottom: 30px !important; }
    .mb-40 { margin-bottom: 40px !important; }
    .mb-50 { margin-bottom: 40px !important; }
    .mb-60 { margin-bottom: 50px !important; }
    .mb-70 { margin-bottom: 50px !important; }
}

/* Utility classes for zero spacing */
.pt-0 { padding-top: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
