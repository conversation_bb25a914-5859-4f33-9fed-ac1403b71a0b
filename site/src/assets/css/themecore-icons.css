@font-face {
  font-family: 'wprticons';
  src:  url('../font/theme-corea727.eot?g49o0u');
  src:  url('../font/theme-corea727.eot?g49o0u#iefix') format('embedded-opentype'),
    url('../font/theme-corea727.ttf?g49o0u') format('truetype'),
    url('../font/theme-corea727.woff?g49o0u') format('woff'),
    url('../font/theme-corea727.svg?g49o0u#theme-core') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="rt-icon-"], [class*=" rt-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'wprticons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.rt-icon-up-arrows-3:before {
  content: "\e935";
}
.rt-icon-up-arrows-1:before {
  content: "\e936";
}
.rt-icon-up-arrows-2:before {
  content: "\e937";
}
.rt-icon-up-arrows-12:before {
  content: "\e938";
}
.rt-icon-up-arrows:before {
  content: "\e939";
}
.rt-icon-zoom-in-3:before {
  content: "\e930";
}
.rt-icon-zoom-in-2:before {
  content: "\e931";
}
.rt-icon-zoom-in-1:before {
  content: "\e932";
}
.rt-icon-zoom-in:before {
  content: "\e933";
}
.rt-icon-cart-2:before {
  content: "\e908";
}
.rt-icon-cart-3:before {
  content: "\e909";
}
.rt-icon-cart:before {
  content: "\e90b";
}
.rt-icon-magnifier6:before {
  content: "\e90c";
}
.rt-icon-play-button-3:before {
  content: "\e90d";
}
.rt-icon-placeholder:before {
  content: "\e913";
}
.rt-icon-telephone:before {
  content: "\e914";
}
.rt-icon-play-button-4:before {
  content: "\e915";
}
.rt-icon-play-button-1:before {
  content: "\e916";
}
.rt-icon-play-button:before {
  content: "\e918";
}
.rt-icon-play-button-2:before {
  content: "\e919";
}
.rt-icon-play:before {
  content: "\e907";
}
.rt-icon-right-arrow-6:before {
  content: "\e934";
}
.rt-icon-play-arrow:before {
  content: "\e951";
}
.rt-icon-left-arrow12:before {
  content: "\e942";
}
.rt-icon-right-arrow12:before {
  content: "\e943";
}
.rt-icon-magnifier1:before {
  content: "\e953";
}
.rt-icon-magnifier2:before {
  content: "\e955";
}
.rt-icon-magnifier3:before {
  content: "\e957";
}
.rt-icon-magnifier4:before {
  content: "\e958";
}
.rt-icon-magnifier5:before {
  content: "\e97c";
}
.rt-icon-down-arrow:before {
  content: "\e925";
}
.rt-icon-right-arrow:before {
  content: "\e928";
}
.rt-icon-minus-circle:before {
  content: "\e929";
}
.rt-icon-plus-circle:before {
  content: "\e92a";
}
.rt-icon-down-arrow-circle:before {
  content: "\e92b";
}
.rt-icon-right-arrow-circle:before {
  content: "\e92c";
}
.rt-icon-plus:before {
  content: "\e820";
}
.rt-icon-minus:before {
  content: "\e821";
}
.rt-icon-1-down:before {
  content: "\e835";
}
.rt-icon-2-down:before {
  content: "\e837";
}
.rt-icon-2-down-1:before {
  content: "\e831";
}
.rt-icon-2-right:before {
  content: "\e836";
}
.rt-icon-close-envelope:before {
  content: "\e92d";
}
.rt-icon-old-phone:before {
  content: "\e92e";
}
.rt-icon-4-time:before {
  content: "\e823";
}
.rt-icon-4-user:before {
  content: "\e83e";
}
.rt-icon-4-address:before {
  content: "\e83f";
}
.rt-icon-4-cart:before {
  content: "\e840";
}
.rt-icon-4-cat:before {
  content: "\e841";
}
.rt-icon-4-comment:before {
  content: "\e842";
}
.rt-icon-4-phone:before {
  content: "\e843";
}
.rt-icon-4-search:before {
  content: "\e844";
}
.rt-icon-home:before {
  content: "\e92f";
}
.rt-icon-3-user:before {
  content: "\e833";
}
.rt-icon-3-address:before {
  content: "\e834";
}
.rt-icon-3-cart:before {
  content: "\e838";
}
.rt-icon-3-cat:before {
  content: "\e839";
}
.rt-icon-3-comment:before {
  content: "\e83a";
}
.rt-icon-3-phone:before {
  content: "\e83b";
}
.rt-icon-3-search:before {
  content: "\e83c";
}
.rt-icon-3-time:before {
  content: "\e83d";
}
.rt-icon-1-address:before {
  content: "\e822";
}
.rt-icon-1-phone:before {
  content: "\e827";
}
.rt-icon-1-search:before {
  content: "\e828";
}
.rt-icon-1-time:before {
  content: "\e829";
}
.rt-icon-2-user:before {
  content: "\e82a";
}
.rt-icon-2-address:before {
  content: "\e82b";
}
.rt-icon-2-cart:before {
  content: "\e82c";
}
.rt-icon-2-cat:before {
  content: "\e82d";
}
.rt-icon-2-comment:before {
  content: "\e82e";
}
.rt-icon-2-phone:before {
  content: "\e82f";
}
.rt-icon-2-search:before {
  content: "\e830";
}
.rt-icon-2-time:before {
  content: "\e832";
}
.rt-icon-flickr:before {
  content: "\e91a";
}
.rt-icon-dribbble:before {
  content: "\e91b";
}
.rt-icon-behance:before {
  content: "\e91c";
}
.rt-icon-android:before {
  content: "\e91d";
}
.rt-icon-apple:before {
  content: "\e91e";
}
.rt-icon-skype:before {
  content: "\e91f";
}
.rt-icon-instagram:before {
  content: "\e920";
}
.rt-icon-pinterest:before {
  content: "\e921";
}
.rt-icon-linkedin:before {
  content: "\e922";
}
.rt-icon-vimeo:before {
  content: "\e923";
}
.rt-icon-youtube:before {
  content: "\e924";
}
.rt-icon-google-plus:before {
  content: "\e917";
}
.rt-icon-twitter:before {
  content: "\e926";
}
.rt-icon-facebook:before {
  content: "\e927";
}
.rt-icon-message-1:before {
  content: "\e903";
}
.rt-icon-home2:before {
  content: "\e904";
}
.rt-icon-calendar:before {
  content: "\e905";
}
.rt-icon-chat2:before {
  content: "\e906";
}
.rt-icon-user:before {
  content: "\e90a";
}
.rt-icon-folder:before {
  content: "\e90e";
}
.rt-icon-settings:before {
  content: "\e90f";
}
.rt-icon-message:before {
  content: "\e910";
}
.rt-icon-placeholder2:before {
  content: "\e912";
}
.rt-icon-alarm-clock:before {
  content: "\e900";
}
.rt-icon-chat:before {
  content: "\e901";
}
.rt-icon-bag:before {
  content: "\e902";
}
.rt-icon-search2:before {
  content: "\e911";
}
