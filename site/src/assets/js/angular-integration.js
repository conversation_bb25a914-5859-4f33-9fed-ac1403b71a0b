/**
 * Angular Integration Script
 * Garante que os plugins jQuery funcionem corretamente com Angular
 */

;(function() {
    'use strict';

    // Variáveis globais para controle
    window.AngularJQueryIntegration = {
        initialized: false,
        retryCount: 0,
        maxRetries: 3, // Reduzido de 10 para 3 tentativas
        initCallbacks: [],
        
        // Adicionar callback para execução após inicialização
        onReady: function(callback) {
            if (this.initialized) {
                callback();
            } else {
                this.initCallbacks.push(callback);
            }
        },

        // Executar todos os callbacks
        executeCallbacks: function() {
            while (this.initCallbacks.length > 0) {
                var callback = this.initCallbacks.shift();
                try {
                    callback();
                } catch (error) {
                    // Erro ao executar callback
                }
            }
        },

        // Verificar se jQuery está disponível
        checkJQuery: function() {
            return typeof window.$ !== 'undefined' && typeof window.jQuery !== 'undefined';
        },

        // Verificar se elementos Angular foram renderizados
        checkAngularElements: function() {
            var appRoot = document.querySelector('app-root');
            var wrapper = document.querySelector('#wrapper');
            var hasContent = document.querySelector('#main-content') !== null ||
                           document.querySelector('.page-content') !== null;

            return appRoot !== null && wrapper !== null && hasContent;
        },

        // Garantir visibilidade da página
        ensurePageVisibility: function() {
            // Adicionar classe para indicar que o script está ativo
            document.documentElement.classList.add('angular-jquery-ready');
            
            // Garantir visibilidade de elementos críticos
            var criticalSelectors = [
                '#wrapper',
                '#main-content', 
                '.page-content',
                '.rev_slider_wrapper',
                '.rev_slider'
            ];

            criticalSelectors.forEach(function(selector) {
                var elements = document.querySelectorAll(selector);
                elements.forEach(function(element) {
                    element.style.opacity = '1';
                    element.style.visibility = 'visible';
                });
            });
        },

        // Inicialização principal
        init: function() {
            var self = this;
            
            if (self.initialized) {
                return;
            }

            // Verificar pré-requisitos
            if (!self.checkJQuery()) {
                self.retryInit();
                return;
            }

            if (!self.checkAngularElements()) {
                self.retryInit();
                return;
            }

            try {
                // Garantir visibilidade imediata
                self.ensurePageVisibility();

                // Inicializar plugins jQuery
                self.initializePlugins();

                // Marcar como inicializado
                self.initialized = true;
                
                // Executar callbacks pendentes
                self.executeCallbacks();

                // Angular-jQuery integration inicializada

            } catch (error) {
                // Erro na inicialização Angular-jQuery
                self.retryInit();
            }
        },

        // Tentar novamente a inicialização
        retryInit: function() {
            var self = this;
            
            if (self.retryCount >= self.maxRetries) {
                // Falha na inicialização após tentativas
                self.ensurePageVisibility();
                return;
            }

            self.retryCount++;
            var delay = Math.min(500 * self.retryCount, 2000); // Max 2 segundos

            setTimeout(function() {
                self.init();
            }, delay);
        },

        // Inicializar plugins jQuery
        initializePlugins: function() {
            var $ = window.$;

            try {
                // Inicializar wprtTheme se disponível
                if (typeof window.wprtTheme !== 'undefined') {
                    window.wprtTheme.init();
                }

                // Inicializar Revolution Slider se disponível
                if (typeof window.RevSlider !== 'undefined') {
                    setTimeout(function() {
                        window.RevSlider.init();
                    }, 300);
                }

                // Inicializar plugins específicos do shortcodes.js
                if (typeof $ !== 'undefined') {
                    // Spacer
                    if (typeof window.spacer === 'function') {
                        window.spacer();
                    }

                    // Counter
                    if (typeof window.counter === 'function') {
                        window.counter();
                    }

                    // Progress Bar
                    if (typeof window.progressBar === 'function') {
                        window.progressBar();
                    }

                    // Gallery Owl
                    if (typeof window.galleryOwl === 'function') {
                        window.galleryOwl();
                    }

                    // Partner Owl
                    if (typeof window.partnerOwl === 'function') {
                        window.partnerOwl();
                    }

                    // Image Popup
                    if (typeof window.imagePopup === 'function') {
                        window.imagePopup();
                    }

                    // Animation - verificar se existe função de animação customizada
                    if (typeof window['animation'] === 'function') {
                        window['animation']();
                    }

                    // Video Popup
                    if (typeof window.videoPopup === 'function') {
                        window.videoPopup();
                    }

                    // Parallax
                    if (typeof window.parallax === 'function') {
                        window.parallax();
                    }

                    // Team Owl
                    if (typeof window.teamOwl === 'function') {
                        window.teamOwl();
                    }

                    // News Owl
                    if (typeof window.newsOwl === 'function') {
                        window.newsOwl();
                    }

                    // Carousel Box Owl
                    if (typeof window.carouselBoxOwl === 'function') {
                        window.carouselBoxOwl();
                    }

                    // Tabs
                    if (typeof window.tabs === 'function') {
                        window.tabs();
                    }

                    // Accordions
                    if (typeof window.accordions === 'function') {
                        window.accordions();
                    }

                    // Content Box
                    if (typeof window.contentBox === 'function') {
                        window.contentBox();
                    }

                    // Thumb Slider
                    if (typeof window.thumbSlider === 'function') {
                        window.thumbSlider();
                    }

                    // Images Cube
                    if (typeof window.imagesCube === 'function') {
                        window.imagesCube();
                    }

                    // Galleries Cube
                    if (typeof window.galleriesCube === 'function') {
                        window.galleriesCube();
                    }

                    // Scroll Target
                    if (typeof window.scrollTarget === 'function') {
                        window.scrollTarget();
                    }

                    // Here Section
                    if (typeof window.hereSection === 'function') {
                        window.hereSection();
                    }

                    // Equalize Height
                    if (typeof window.equalizeHeight === 'function') {
                        window.equalizeHeight();
                    }

                    // InViewport (importante para progress bars)
                    if (typeof window.inViewport === 'function') {
                        window.inViewport();
                    }

                    // Plugins do shortcodes.js inicializados
                }

                // Refresh de waypoints
                if ($.fn.waypoint) {
                    setTimeout(function() {
                        if (typeof Waypoint !== 'undefined') {
                            Waypoint.refreshAll();
                        }
                    }, 500);
                }

                // Trigger resize para recalcular layouts
                setTimeout(function() {
                    $(window).trigger('resize');
                }, 200);

                // Plugins jQuery inicializados com sucesso

            } catch (error) {
                // Erro ao inicializar plugins jQuery
            }
        }
    };

    // Auto-inicialização
    function autoInit() {
        // Aguardar DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(function() {
                    window.AngularJQueryIntegration.init();
                }, 100);
            });
        } else {
            // DOM já está pronto
            setTimeout(function() {
                window.AngularJQueryIntegration.init();
            }, 100);
        }

        // Também tentar após window load
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (!window.AngularJQueryIntegration.initialized) {
                    window.AngularJQueryIntegration.init();
                }
            }, 200);
        });
    }

    // Iniciar processo
    autoInit();

    // Expor função global para uso em componentes Angular
    window.initializeJQueryPlugins = function() {
        window.AngularJQueryIntegration.init();
    };

    // Função para garantir visibilidade (uso de emergência)
    window.ensurePageVisibility = function() {
        window.AngularJQueryIntegration.ensurePageVisibility();
    };

})();
