/** 

  * http://jqueryui.com - jQuery UI - v1.11.2

  * Copyright 2014 jQuery Foundation and other contributors; Licensed MIT

*/


/* states and images */

.ui-icon {
    display: block;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat;
}


/* reset extra padding in Firefox, see h5bp.com/l */

input.ui-button::-moz-focus-inner,
button.ui-button::-moz-focus-inner {
    border: 0;
    padding: 0;
}

.ui-datepicker {
    width: 17em;
    padding: .2em .2em 0;
    display: none;
}

.ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: .2em 0;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    position: absolute;
    top: 6px;
    width: 1.8em;
    height: 1.8em;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-prev {
    left: 6px;
}

.ui-datepicker .ui-datepicker-next {
    right: -3px;
}

.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
    display: block;
    position: absolute;
    left: 50%;
    margin-left: -8px;
    top: 50%;
    margin-top: -8px;
}

.ui-datepicker .ui-datepicker-title {
    margin: 0 2.3em;
    line-height: 1.8em;
    text-align: center;
}

.ui-datepicker .ui-datepicker-title select {
    font-size: 1em;
    margin: 1px 0;
}

.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
    width: 45%;
}

.ui-datepicker table {
    width: 100%;
    font-size: .9em;
    border-collapse: collapse;
    margin: 0 0 .4em;
}

.ui-datepicker th {
    padding: .7em .3em;
    text-align: center;
    font-weight: bold;
    border: 0;
}

.ui-datepicker td {
    border: 0;
    padding: 1px;
}

.ui-datepicker td span,
.ui-datepicker td a {
    display: block;
    padding: .2em;
    text-align: center;
    text-decoration: none;
}

.ui-datepicker .ui-datepicker-buttonpane {
    background-image: none;
    margin: .7em 0 0 0;
    padding: 0 .2em;
    border-left: 0;
    border-right: 0;
    border-bottom: 0;
}

.ui-datepicker .ui-datepicker-buttonpane button {
    float: right;
    margin: .5em .2em .4em;
    cursor: pointer;
    padding: .2em .6em .3em .6em;
    width: auto;
    overflow: visible;
}

.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
    float: left;
}


/* with multiple calendars */

.ui-datepicker.ui-datepicker-multi {
    width: auto;
}

.ui-datepicker-multi .ui-datepicker-group {
    float: left;
}

.ui-datepicker-multi .ui-datepicker-group table {
    width: 95%;
    margin: 0 auto .4em;
}

.ui-datepicker-multi-2 .ui-datepicker-group {
    width: 50%;
}

.ui-datepicker-multi-3 .ui-datepicker-group {
    width: 33.3%;
}

.ui-datepicker-multi-4 .ui-datepicker-group {
    width: 25%;
}

.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
    border-left-width: 0;
}

.ui-datepicker-multi .ui-datepicker-buttonpane {
    clear: left;
}

.ui-datepicker-row-break {
    clear: both;
    width: 100%;
    font-size: 0;
}


/* Component containers

----------------------------------*/

.ui-widget {
    font-family: "Poppins", sans-serif;
    font-size: 15px;
}

.ui-widget .ui-widget {
    font-size: 15px;
}

.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
    font-family: "Poppins", sans-serif;
    font-size: 15px;
}

.ui-widget-content {
    border: 1px solid #aaaaaa;
    background: #ffffff;
    color: #443f3f;
}

.ui-widget-content a {
    color: #443f3f;
}

.ui-widget-header {
    border: 1px solid #aaaaaa;
    background: #cccccc;
    color: #333;
    font-weight: bold;
}

.ui-widget-header a.ui-datepicker-prev:before,
.ui-widget-header a.ui-datepicker-next:before {
    content: "";
    position: absolute;
    top: 6px;
    border-style: solid;
    border-width: 5px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.ui-widget-header a.ui-datepicker-prev:before {
    left: 0;
    border-color: transparent #443f3f transparent transparent;
}

.ui-widget-header a.ui-datepicker-next:before {
    left: 7px;
    border-color: transparent transparent transparent #443f3f;
}

.ui-widget-header a.ui-datepicker-next:hover:before {
    border-color: transparent transparent transparent #1c63b8;
}

.ui-widget-header a.ui-datepicker-prev:hover:before {
    border-color: transparent #1c63b8 transparent transparent;
}

.ui-datepicker-calendar .ui-state-default:hover {
    color: #1c63b8;
}