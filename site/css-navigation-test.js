/**
 * Script de Teste: Diagnóstico de CSS durante Navegação
 * 
 * Este script monitora e diagnostica problemas de CSS durante navegação Angular
 * Execute no console do navegador para monitorar mudanças de CSS
 */

(function() {
    'use strict';
    
    console.log('🎨 Iniciando diagnóstico de CSS durante navegação...');
    
    const cssMonitor = {
        initialStylesheets: [],
        currentStylesheets: [],
        missingStyles: [],
        addedStyles: [],
        logs: []
    };
    
    // Capturar estado inicial dos stylesheets
    function captureInitialState() {
        cssMonitor.initialStylesheets = Array.from(document.styleSheets).map(sheet => ({
            href: sheet.href,
            title: sheet.title,
            disabled: sheet.disabled,
            media: sheet.media.mediaText,
            rules: sheet.cssRules ? sheet.cssRules.length : 0
        }));
        
        console.log('📊 Estado inicial capturado:', cssMonitor.initialStylesheets.length, 'stylesheets');
        cssMonitor.initialStylesheets.forEach((sheet, index) => {
            console.log(`  ${index + 1}. ${sheet.href || 'inline'} (${sheet.rules} regras)`);
        });
    }
    
    // Verificar estado atual dos stylesheets
    function checkCurrentState() {
        cssMonitor.currentStylesheets = Array.from(document.styleSheets).map(sheet => ({
            href: sheet.href,
            title: sheet.title,
            disabled: sheet.disabled,
            media: sheet.media.mediaText,
            rules: sheet.cssRules ? sheet.cssRules.length : 0
        }));
        
        return cssMonitor.currentStylesheets;
    }
    
    // Comparar estados e identificar diferenças
    function compareStates() {
        const current = checkCurrentState();
        const initial = cssMonitor.initialStylesheets;
        
        // Encontrar stylesheets removidos
        const missing = initial.filter(initialSheet => 
            !current.some(currentSheet => currentSheet.href === initialSheet.href)
        );
        
        // Encontrar stylesheets adicionados
        const added = current.filter(currentSheet => 
            !initial.some(initialSheet => initialSheet.href === currentSheet.href)
        );
        
        // Encontrar stylesheets com regras alteradas
        const changed = current.filter(currentSheet => {
            const initialSheet = initial.find(s => s.href === currentSheet.href);
            return initialSheet && initialSheet.rules !== currentSheet.rules;
        });
        
        cssMonitor.missingStyles = missing;
        cssMonitor.addedStyles = added;
        
        return { missing, added, changed };
    }
    
    // Verificar se estilos específicos estão aplicados
    function checkSpecificStyles() {
        const tests = [
            {
                name: 'Bootstrap Grid',
                test: () => {
                    const testEl = document.createElement('div');
                    testEl.className = 'container';
                    document.body.appendChild(testEl);
                    const styles = window.getComputedStyle(testEl);
                    const hasBootstrap = styles.maxWidth !== 'none' && styles.maxWidth !== '';
                    document.body.removeChild(testEl);
                    return hasBootstrap;
                }
            },
            {
                name: 'Font Awesome Icons',
                test: () => {
                    const testEl = document.createElement('i');
                    testEl.className = 'fa fa-home';
                    document.body.appendChild(testEl);
                    const styles = window.getComputedStyle(testEl, '::before');
                    const hasFontAwesome = styles.fontFamily && styles.fontFamily.includes('FontAwesome');
                    document.body.removeChild(testEl);
                    return hasFontAwesome;
                }
            },
            {
                name: 'Owl Carousel Styles',
                test: () => {
                    const testEl = document.createElement('div');
                    testEl.className = 'owl-carousel';
                    document.body.appendChild(testEl);
                    const styles = window.getComputedStyle(testEl);
                    const hasOwlCarousel = styles.display !== 'block' || styles.position === 'relative';
                    document.body.removeChild(testEl);
                    return hasOwlCarousel;
                }
            },
            {
                name: 'Custom Theme Styles',
                test: () => {
                    const testEl = document.createElement('div');
                    testEl.className = 'wprt-spacer';
                    document.body.appendChild(testEl);
                    const styles = window.getComputedStyle(testEl);
                    const hasCustomStyles = styles.display === 'block';
                    document.body.removeChild(testEl);
                    return hasCustomStyles;
                }
            }
        ];
        
        const results = tests.map(test => ({
            name: test.name,
            passed: test.test(),
            timestamp: new Date().toISOString()
        }));
        
        return results;
    }
    
    // Monitorar mudanças durante navegação
    function startNavigationMonitoring() {
        // Observar mudanças no DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // Verificar se foram adicionados/removidos elementos <link> ou <style>
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1 && (node.tagName === 'LINK' || node.tagName === 'STYLE')) {
                            console.log('➕ Stylesheet adicionado:', node);
                            cssMonitor.logs.push({
                                type: 'added',
                                element: node.outerHTML,
                                timestamp: new Date().toISOString()
                            });
                        }
                    });
                    
                    mutation.removedNodes.forEach(node => {
                        if (node.nodeType === 1 && (node.tagName === 'LINK' || node.tagName === 'STYLE')) {
                            console.log('➖ Stylesheet removido:', node);
                            cssMonitor.logs.push({
                                type: 'removed',
                                element: node.outerHTML,
                                timestamp: new Date().toISOString()
                            });
                        }
                    });
                }
            });
        });
        
        observer.observe(document.head, {
            childList: true,
            subtree: true
        });
        
        // Monitorar mudanças de rota
        let currentUrl = window.location.href;
        setInterval(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                console.log('🔄 Navegação detectada para:', currentUrl);
                
                // Aguardar um pouco para a rota ser processada
                setTimeout(() => {
                    const comparison = compareStates();
                    const styleTests = checkSpecificStyles();
                    
                    console.log('📊 Análise pós-navegação:');
                    console.log('  Stylesheets removidos:', comparison.missing.length);
                    console.log('  Stylesheets adicionados:', comparison.added.length);
                    console.log('  Stylesheets alterados:', comparison.changed.length);
                    
                    console.log('🎨 Testes de estilos específicos:');
                    styleTests.forEach(test => {
                        console.log(`  ${test.passed ? '✅' : '❌'} ${test.name}`);
                    });
                    
                    if (comparison.missing.length > 0) {
                        console.warn('⚠️ Stylesheets perdidos durante navegação:', comparison.missing);
                    }
                    
                    cssMonitor.logs.push({
                        type: 'navigation',
                        url: currentUrl,
                        comparison,
                        styleTests,
                        timestamp: new Date().toISOString()
                    });
                }, 500);
            }
        }, 100);
        
        console.log('👁️ Monitoramento de navegação iniciado');
    }
    
    // Função para gerar relatório
    function generateReport() {
        const report = {
            initialState: cssMonitor.initialStylesheets,
            currentState: checkCurrentState(),
            comparison: compareStates(),
            styleTests: checkSpecificStyles(),
            logs: cssMonitor.logs,
            timestamp: new Date().toISOString()
        };
        
        console.log('📋 RELATÓRIO DE DIAGNÓSTICO CSS');
        console.log('================================');
        console.log('Stylesheets iniciais:', report.initialState.length);
        console.log('Stylesheets atuais:', report.currentState.length);
        console.log('Stylesheets perdidos:', report.comparison.missing.length);
        console.log('Stylesheets adicionados:', report.comparison.added.length);
        
        console.log('\n🎨 Testes de Estilos:');
        report.styleTests.forEach(test => {
            console.log(`${test.passed ? '✅' : '❌'} ${test.name}`);
        });
        
        if (report.comparison.missing.length > 0) {
            console.log('\n⚠️ PROBLEMA IDENTIFICADO: Stylesheets perdidos');
            report.comparison.missing.forEach(sheet => {
                console.log(`  - ${sheet.href || 'inline'}`);
            });
        }
        
        return report;
    }
    
    // Função para testar navegação automática
    function testNavigation() {
        console.log('🧭 Iniciando teste automático de navegação...');
        
        const routes = ['/', '/empresa', '/servicos', '/contato', '/'];
        let currentIndex = 0;
        
        function navigateNext() {
            if (currentIndex < routes.length) {
                const route = routes[currentIndex];
                console.log(`📍 Navegando para: ${route}`);
                
                // Simular navegação
                window.history.pushState({}, '', route);
                window.dispatchEvent(new PopStateEvent('popstate'));
                
                currentIndex++;
                
                // Aguardar e continuar
                setTimeout(navigateNext, 3000);
            } else {
                console.log('✅ Teste de navegação concluído!');
                setTimeout(() => {
                    generateReport();
                }, 1000);
            }
        }
        
        navigateNext();
    }
    
    // Inicializar monitoramento
    captureInitialState();
    startNavigationMonitoring();
    
    // Expor funções globalmente
    window.cssNavigationTest = {
        captureInitialState,
        checkCurrentState,
        compareStates,
        checkSpecificStyles,
        generateReport,
        testNavigation,
        monitor: cssMonitor
    };
    
    console.log('💡 Use cssNavigationTest.generateReport() para relatório atual');
    console.log('💡 Use cssNavigationTest.testNavigation() para teste automático');
    
})();
