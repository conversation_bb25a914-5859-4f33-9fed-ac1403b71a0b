{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "npm", "analytics": "************************************"}, "newProjectRoot": "projects", "projects": {"angular-site": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/angular-site", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/assets/css/bootstrap.css", "src/assets/css/animate.css", "src/assets/css/animsition.css", "src/assets/css/magnific-popup.css", "src/assets/css/cubeportfolio.css", "src/assets/css/owl.carousel.css", "src/assets/css/flexslider.css", "src/assets/css/simpletextrotator.css", "src/assets/css/vegas.css", "src/assets/css/datepicker.css", "src/assets/css/woocommerce.css", "src/assets/css/font-awesome.css", "src/assets/css/themecore-icons.css", "src/assets/css/carservice-icons.css", "src/assets/includes/rev-slider/css/settings.css", "src/assets/includes/rev-slider/css/layers.css", "src/assets/includes/rev-slider/css/navigation.css", "src/assets/css/shortcodes.css", "src/style.css"], "scripts": ["src/assets/js/jquery.min.js", "src/assets/js/plugins.js", "src/assets/js/bootstrap.min.js", "src/assets/js/animsition.js", "src/assets/js/countto.js", "src/assets/js/vegas.js", "src/assets/js/cubeportfolio.js", "src/assets/js/owl.carousel.min.js", "src/assets/js/flexslider.min.js", "src/assets/js/magnific.popup.min.js", "src/assets/js/equalize.min.js", "src/assets/js/fittext.js", "src/assets/js/simple.text.rotator.js", "src/assets/js/typed.js", "src/assets/js/jquery-ui.datepicker.js", "src/assets/js/validate.js", "src/assets/js/gmap3.min.js", "src/assets/includes/rev-slider/js/jquery.themepunch.tools.min.js", "src/assets/includes/rev-slider/js/jquery.themepunch.revolution.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.actions.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.carousel.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.kenburn.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.layeranimation.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.migration.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.navigation.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.parallax.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.slideanims.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.video.min.js", "src/assets/js/shortcodes.js", "src/assets/js/rev-slider.js", "src/assets/js/main.js", "src/assets/js/angular-integration.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "4MB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "15kB", "maximumError": "25kB"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "angular-site:build:production"}, "development": {"buildTarget": "angular-site:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/assets/css/bootstrap.css", "src/assets/css/animate.css", "src/assets/css/animsition.css", "src/assets/css/magnific-popup.css", "src/assets/css/cubeportfolio.css", "src/assets/css/owl.carousel.css", "src/assets/css/flexslider.css", "src/assets/css/simpletextrotator.css", "src/assets/css/vegas.css", "src/assets/css/datepicker.css", "src/assets/css/woocommerce.css", "src/assets/css/font-awesome.css", "src/assets/css/themecore-icons.css", "src/assets/css/carservice-icons.css", "src/assets/includes/rev-slider/css/settings.css", "src/assets/includes/rev-slider/css/layers.css", "src/assets/includes/rev-slider/css/navigation.css", "src/assets/css/shortcodes.css", "src/style.css"], "scripts": ["src/assets/js/jquery.min.js", "src/assets/js/plugins.js", "src/assets/js/bootstrap.min.js", "src/assets/js/animsition.js", "src/assets/js/countto.js", "src/assets/js/vegas.js", "src/assets/js/cubeportfolio.js", "src/assets/js/owl.carousel.min.js", "src/assets/js/flexslider.min.js", "src/assets/js/magnific.popup.min.js", "src/assets/js/equalize.min.js", "src/assets/js/fittext.js", "src/assets/js/simple.text.rotator.js", "src/assets/js/typed.js", "src/assets/js/jquery-ui.datepicker.js", "src/assets/js/validate.js", "src/assets/js/gmap3.min.js", "src/assets/includes/rev-slider/js/jquery.themepunch.tools.min.js", "src/assets/includes/rev-slider/js/jquery.themepunch.revolution.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.actions.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.carousel.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.kenburn.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.layeranimation.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.migration.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.navigation.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.parallax.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.slideanims.min.js", "src/assets/includes/rev-slider/js/extensions/revolution.extension.video.min.js", "src/assets/js/shortcodes.js", "src/assets/js/rev-slider.js", "src/assets/js/main.js", "src/assets/js/angular-integration.js"]}}}}}}