/**
 * Script de Teste: Correção Angular Router + Assets Externos
 * 
 * Este script valida se a correção implementada está funcionando corretamente.
 * Execute no console do navegador após carregar a aplicação.
 */

(function() {
    'use strict';
    
    console.log('🧪 Iniciando testes da correção Angular Router...');
    
    const tests = {
        passed: 0,
        failed: 0,
        results: []
    };
    
    function test(name, condition, details = '') {
        const result = {
            name,
            passed: !!condition,
            details
        };
        
        tests.results.push(result);
        
        if (result.passed) {
            tests.passed++;
            console.log(`✅ ${name}`, details);
        } else {
            tests.failed++;
            console.error(`❌ ${name}`, details);
        }
        
        return result.passed;
    }
    
    // Teste 1: jQuery disponível
    test(
        'jQuery Disponível',
        typeof $ !== 'undefined' && typeof jQuery !== 'undefined',
        `Versão: ${typeof $ !== 'undefined' ? $.fn.jquery : 'N/A'}`
    );
    
    // Teste 2: Bootstrap disponível
    test(
        'Bootstrap Disponível',
        typeof $.fn.modal !== 'undefined',
        'Bootstrap modal plugin detectado'
    );
    
    // Teste 3: Assets CSS carregados
    const stylesheets = Array.from(document.styleSheets);
    const bootstrapCSS = stylesheets.some(sheet => 
        sheet.href && sheet.href.includes('bootstrap')
    );
    test(
        'Bootstrap CSS Carregado',
        bootstrapCSS,
        `${stylesheets.length} stylesheets carregadas`
    );
    
    // Teste 4: Font Awesome disponível
    const fontAwesome = stylesheets.some(sheet => 
        sheet.href && sheet.href.includes('font-awesome')
    );
    test(
        'Font Awesome CSS Carregado',
        fontAwesome,
        'Ícones Font Awesome disponíveis'
    );
    
    // Teste 5: Scripts bundled
    const scripts = Array.from(document.scripts);
    const bundledScripts = scripts.some(script => 
        script.src && script.src.includes('scripts.js')
    );
    test(
        'Scripts Bundled Carregados',
        bundledScripts,
        `${scripts.length} scripts no DOM`
    );
    
    // Teste 6: Angular Router ativo
    test(
        'Angular Router Ativo',
        typeof ng !== 'undefined' || document.querySelector('router-outlet') !== null,
        'Router outlet encontrado no DOM'
    );
    
    // Teste 7: NoReuseStrategy ativa
    const routerLogs = [];
    const originalLog = console.log;
    console.log = function(...args) {
        if (args[0] && args[0].includes('NoReuseRouteStrategy')) {
            routerLogs.push(args.join(' '));
        }
        originalLog.apply(console, args);
    };
    
    // Teste 8: Owl Carousel disponível
    test(
        'Owl Carousel Plugin',
        typeof $.fn.owlCarousel !== 'undefined',
        'Plugin Owl Carousel carregado'
    );
    
    // Teste 9: Magnific Popup disponível
    test(
        'Magnific Popup Plugin',
        typeof $.fn.magnificPopup !== 'undefined',
        'Plugin Magnific Popup carregado'
    );
    
    // Teste 10: Revolution Slider disponível
    test(
        'Revolution Slider Plugin',
        typeof $.fn.revolution !== 'undefined',
        'Plugin Revolution Slider carregado'
    );
    
    // Teste 11: AngularJQueryIntegration
    test(
        'Angular-jQuery Integration',
        typeof window.AngularJQueryIntegration !== 'undefined',
        `Inicializado: ${window.AngularJQueryIntegration?.initialized || false}`
    );
    
    // Teste 12: Elementos DOM básicos
    const appRoot = document.querySelector('app-root');
    test(
        'App Root Presente',
        appRoot !== null,
        `Elemento app-root encontrado`
    );
    
    // Teste 13: Header component
    const header = document.querySelector('app-header') || document.querySelector('header');
    test(
        'Header Component',
        header !== null,
        'Componente de header encontrado'
    );
    
    // Teste 14: Footer component
    const footer = document.querySelector('app-footer') || document.querySelector('footer');
    test(
        'Footer Component',
        footer !== null,
        'Componente de footer encontrado'
    );
    
    // Teste 15: Verificar se há erros no console
    const errors = [];
    const originalError = console.error;
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    // Aguardar um pouco para capturar erros
    setTimeout(() => {
        test(
            'Console Sem Erros Críticos',
            errors.length === 0,
            errors.length > 0 ? `${errors.length} erros encontrados` : 'Nenhum erro detectado'
        );
        
        // Restaurar console
        console.log = originalLog;
        console.error = originalError;
        
        // Relatório final
        console.log('\n📊 RELATÓRIO FINAL DOS TESTES');
        console.log('================================');
        console.log(`✅ Testes Aprovados: ${tests.passed}`);
        console.log(`❌ Testes Falharam: ${tests.failed}`);
        console.log(`📈 Taxa de Sucesso: ${Math.round((tests.passed / tests.results.length) * 100)}%`);
        
        if (tests.failed === 0) {
            console.log('\n🎉 TODOS OS TESTES PASSARAM!');
            console.log('✅ A correção Angular Router está funcionando corretamente.');
        } else {
            console.log('\n⚠️ ALGUNS TESTES FALHARAM');
            console.log('❌ Verifique os itens marcados em vermelho acima.');
        }
        
        // Instruções para teste manual
        console.log('\n🔍 TESTE MANUAL RECOMENDADO:');
        console.log('1. Navegue: Home → Empresa → Serviços → Home');
        console.log('2. Verifique se os estilos persistem');
        console.log('3. Teste botões "Solicite Agora"');
        console.log('4. Verifique carrosséis e animações');
        console.log('5. Confirme que não há recarregamento de página');
        
        return {
            passed: tests.passed,
            failed: tests.failed,
            total: tests.results.length,
            success: tests.failed === 0,
            results: tests.results
        };
        
    }, 2000);
    
})();

// Função para testar navegação programaticamente
function testNavigation() {
    console.log('🧭 Testando navegação programática...');
    
    const routes = ['/', '/empresa', '/servicos', '/contato', '/'];
    let currentIndex = 0;
    
    function navigateNext() {
        if (currentIndex < routes.length) {
            const route = routes[currentIndex];
            console.log(`📍 Navegando para: ${route}`);
            
            // Simular clique em link de navegação
            const link = document.querySelector(`a[href="${route}"]`);
            if (link) {
                link.click();
            } else {
                console.warn(`⚠️ Link não encontrado para rota: ${route}`);
            }
            
            currentIndex++;
            
            // Aguardar navegação e continuar
            setTimeout(navigateNext, 3000);
        } else {
            console.log('✅ Teste de navegação concluído!');
        }
    }
    
    navigateNext();
}

// Exportar funções para uso manual
window.testRouterFix = {
    runTests: () => location.reload(),
    testNavigation: testNavigation
};

console.log('💡 Use testRouterFix.testNavigation() para testar navegação automática');
